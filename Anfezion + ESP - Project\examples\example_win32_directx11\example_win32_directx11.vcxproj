﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{9F316E83-5AE5-4939-A723-305A94F48005}</ProjectGuid>
    <RootNamespace>example_win32_directx11</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(ProjectDir)$(Configuration)\</OutDir>
    <IntDir>$(ProjectDir)$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>$(ProjectDir)$(Configuration)\</OutDir>
    <IntDir>$(ProjectDir)$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(ProjectDir)$(Configuration)\</OutDir>
    <IntDir>$(ProjectDir)$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>..\EXE</OutDir>
    <IntDir>$(ProjectDir)$(Configuration)\</IntDir>
    <IncludePath>C:\Users\<USER>\Desktop\SDK\Include;C:\Users\<USER>\Desktop\freetype\include;$(IncludePath)</IncludePath>
    <LibraryPath>C:\Users\<USER>\Desktop\freetype\win64;C:\Users\<USER>\Desktop\SDK\Lib\x64;$(LibraryPath)</LibraryPath>
    <TargetName>4chan</TargetName>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\..;..\..\backends;%(AdditionalIncludeDirectories);</AdditionalIncludeDirectories>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>d3d11.lib;d3dcompiler.lib;dxgi.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(DXSDK_DIR)/Lib/x86;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <SubSystem>Console</SubSystem>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\..;..\..\backends;%(AdditionalIncludeDirectories);</AdditionalIncludeDirectories>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>d3d11.lib;d3dcompiler.lib;dxgi.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(DXSDK_DIR)/Lib/x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <SubSystem>Console</SubSystem>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>..\..;..\..\backends;%(AdditionalIncludeDirectories);</AdditionalIncludeDirectories>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>d3d11.lib;d3dcompiler.lib;dxgi.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(DXSDK_DIR)/Lib/x86;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <SubSystem>Console</SubSystem>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>..\..;..\..\backends;%(AdditionalIncludeDirectories);</AdditionalIncludeDirectories>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>d3d11.lib;d3dcompiler.lib;dxgi.lib;Winhttp.lib;winmm.lib;freetype.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(DXSDK_DIR)/Lib/x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <SubSystem>Windows</SubSystem>
      <IgnoreAllDefaultLibraries>
      </IgnoreAllDefaultLibraries>
      <UACExecutionLevel>AsInvoker</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\..\imconfig.h" />
    <ClInclude Include="..\..\imgui.h" />
    <ClInclude Include="..\..\imgui_edited.hpp" />
    <ClInclude Include="..\..\imgui_freetype.h" />
    <ClInclude Include="..\..\imgui_internal.h" />
    <ClInclude Include="..\..\backends\imgui_impl_dx11.h" />
    <ClInclude Include="..\..\backends\imgui_impl_win32.h" />
    <ClInclude Include="..\..\imgui_settings.h" />
    <ClInclude Include="AuthDLL\Header.h" />
    <ClInclude Include="AuthDLL\json.hpp" />
    <ClInclude Include="AuthDLL\skCrypter.h" />
    <ClInclude Include="AuthDLL\wnetwrap.h" />
    <ClInclude Include="auth\auth.hpp" />
    <ClInclude Include="auth\json.hpp" />
    <ClInclude Include="auth\skStr.h" />
    <ClInclude Include="auth\utils.hpp" />
    <ClInclude Include="DiscordRPC\Discord.h" />
    <ClInclude Include="DiscordSDK\src\backoff.h" />
    <ClInclude Include="DiscordSDK\src\connection.h" />
    <ClInclude Include="DiscordSDK\src\discord_register.h" />
    <ClInclude Include="DiscordSDK\src\discord_rpc.h" />
    <ClInclude Include="DiscordSDK\src\msg_queue.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\allocators.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\cursorstreamwrapper.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\document.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\encodedstream.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\encodings.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\error\en.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\error\error.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\filereadstream.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\filewritestream.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\fwd.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\internal\biginteger.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\internal\clzll.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\internal\diyfp.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\internal\dtoa.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\internal\ieee754.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\internal\itoa.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\internal\meta.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\internal\pow10.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\internal\regex.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\internal\stack.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\internal\strfunc.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\internal\strtod.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\internal\swap.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\istreamwrapper.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\memorybuffer.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\memorystream.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\msinttypes\inttypes.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\msinttypes\stdint.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\ostreamwrapper.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\pointer.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\prettywriter.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\rapidjson.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\reader.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\schema.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\stream.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\stringbuffer.h" />
    <ClInclude Include="DiscordSDK\src\rapidjson\writer.h" />
    <ClInclude Include="DiscordSDK\src\rpc_connection.h" />
    <ClInclude Include="DiscordSDK\src\serialization.h" />
    <ClInclude Include="ESPFont.h" />
    <ClInclude Include="ESP\ESP LINES\ADBZapi.hpp" />
    <ClInclude Include="ESP\ESP LINES\Bones.hpp" />
    <ClInclude Include="ESP\ESP LINES\bool3.h" />
    <ClInclude Include="ESP\ESP LINES\Complementos.h" />
    <ClInclude Include="ESP\ESP LINES\Offset.hpp" />
    <ClInclude Include="ESP\ESP LINES\Process.hpp" />
    <ClInclude Include="ESP\ESP LINES\Quaternion.hpp" />
    <ClInclude Include="ESP\ESP LINES\Unity.hh" />
    <ClInclude Include="ESP\ESP LINES\Vector2.hpp" />
    <ClInclude Include="ESP\ESP LINES\Vector3.hpp" />
    <ClInclude Include="ESP\Fonts\NotoArabic.h" />
    <ClInclude Include="ESP\Fonts\NotoDevanagari.h" />
    <ClInclude Include="ESP\Fonts\NotoKhmer.h" />
    <ClInclude Include="ESP\Fonts\NotoKorean.h" />
    <ClInclude Include="ESP\Fonts\NotoRegular.h" />
    <ClInclude Include="ESP\Fonts\NotoSinhala.h" />
    <ClInclude Include="ESP\Fonts\NotoSymbols2.h" />
    <ClInclude Include="ESP\Fonts\NotoTamil.h" />
    <ClInclude Include="ESP\Fonts\NotoThai.h" />
    <ClInclude Include="ESP\Fonts\Unifont.h" />
    <ClInclude Include="ESP\MinHook\include\MinHook.h" />
    <ClInclude Include="ESP\MinHook\src\buffer.h" />
    <ClInclude Include="ESP\MinHook\src\hde\hde32.h" />
    <ClInclude Include="ESP\MinHook\src\hde\hde64.h" />
    <ClInclude Include="ESP\MinHook\src\hde\pstdint.h" />
    <ClInclude Include="ESP\MinHook\src\hde\table32.h" />
    <ClInclude Include="ESP\MinHook\src\hde\table64.h" />
    <ClInclude Include="ESP\MinHook\src\trampoline.h" />
    <ClInclude Include="font.h" />
    <ClInclude Include="include\dx\debug.h" />
    <ClInclude Include="include\dx\dx.h" />
    <ClInclude Include="include\dx\handle.h" />
    <ClInclude Include="main.h" />
    <ClInclude Include="PicoMem.h" />
    <ClInclude Include="texture.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\imgui.cpp" />
    <ClCompile Include="..\..\imgui_draw.cpp" />
    <ClCompile Include="..\..\imgui_edited.cpp" />
    <ClCompile Include="..\..\imgui_freetype.cpp" />
    <ClCompile Include="..\..\imgui_tables.cpp" />
    <ClCompile Include="..\..\imgui_widgets.cpp" />
    <ClCompile Include="..\..\backends\imgui_impl_dx11.cpp" />
    <ClCompile Include="..\..\backends\imgui_impl_win32.cpp" />
    <ClCompile Include="AuthDLL\wnetwrap.cpp" />
    <ClCompile Include="DiscordRPC\Discord.cpp" />
    <ClCompile Include="DiscordSDK\src\connection_win.cpp" />
    <ClCompile Include="DiscordSDK\src\discord_register_win.cpp" />
    <ClCompile Include="DiscordSDK\src\discord_rpc.cpp" />
    <ClCompile Include="DiscordSDK\src\rpc_connection.cpp" />
    <ClCompile Include="DiscordSDK\src\serialization.cpp" />
    <ClCompile Include="ESP\MinHook\src\buffer.c" />
    <ClCompile Include="ESP\MinHook\src\hde\hde32.c" />
    <ClCompile Include="ESP\MinHook\src\hde\hde64.c" />
    <ClCompile Include="ESP\MinHook\src\hook.c" />
    <ClCompile Include="ESP\MinHook\src\trampoline.c" />
    <ClCompile Include="main.cpp" />
    <ClCompile Include="Encryptor.hpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\misc\debuggers\imgui.natstepfilter" />
    <None Include="..\..\misc\debuggers\imgui.natvis" />
    <None Include="build_win32.bat" />
    <None Include="imgui.ini" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Library Include="auth\libcurl.lib" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>