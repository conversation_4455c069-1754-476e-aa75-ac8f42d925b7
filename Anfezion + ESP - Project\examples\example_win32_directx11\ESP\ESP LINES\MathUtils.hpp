#pragma once
#include <cmath>
#include <algorithm>
#include "Vector3.hpp"

struct Quaternion {
    float x, y, z, w;
    
    Quaternion() : x(0), y(0), z(0), w(1) {}
    Quaternion(float x, float y, float z, float w) : x(x), y(y), z(z), w(w) {}
    
    // Identity quaternion
    static Quaternion Identity() {
        return Quaternion(0, 0, 0, 1);
    }
};

class MathUtils {
private:
    static constexpr float SMALL_FLOAT = 0.0000000001f;
    static constexpr float PI = 3.14159265359f;

public:
    // Get rotation to look at target location
    static Quaternion GetRotationToLocation(Vector3 targetLocation, float yBias, Vector3 myLocation) {
        Vector3 forwards = targetLocation + Vector3(0, yBias, 0) - myLocation;
        Vector3 upwards = Vector3(0, 1, 0);
        return LookRotation(forwards, upwards);
    }

    // Convert quaternion to angle
    static float QuaternionToAngle(Quaternion quaternion) {
        Vector3 euler = QuaternionToEulerAngles(quaternion);
        return euler.Y; // Yaw angle
    }

    // Convert quaternion to Euler angles
    static Vector3 QuaternionToEulerAngles(Quaternion q) {
        float pitch = asin(2 * (q.y * q.z + q.w * q.x));
        float yaw = atan2(2 * (q.w * q.y - q.x * q.z), 1 - 2 * (q.y * q.y + q.z * q.z));
        float roll = atan2(2 * (q.x * q.y + q.w * q.z), 1 - 2 * (q.y * q.y + q.z * q.z));
        return Vector3(pitch, yaw, roll);
    }

    // Create look rotation
    static Quaternion LookRotation(Vector3 forwards, Vector3 upwards) {
        forwards = Normalized(forwards);
        upwards = Normalized(upwards);

        if (SqrMagnitude(forwards) < SMALL_FLOAT || SqrMagnitude(upwards) < SMALL_FLOAT)
            return Quaternion::Identity();

        if (1 - abs(Dot(forwards, upwards)) < SMALL_FLOAT)
            return FromToRotation(forwards, upwards);

        Vector3 right = Normalized(Cross(upwards, forwards));
        upwards = Cross(forwards, right);

        Quaternion quaternion;
        float radicand = right.X + upwards.Y + forwards.Z;

        if (radicand > 0) {
            quaternion.w = sqrt(1.0f + radicand) * 0.5f;
            float recip = 1.0f / (4.0f * quaternion.w);
            quaternion.x = (upwards.Z - forwards.Y) * recip;
            quaternion.y = (forwards.X - right.Z) * recip;
            quaternion.z = (right.Y - upwards.X) * recip;
        }
        else if (right.X >= upwards.Y && right.X >= forwards.Z) {
            quaternion.x = sqrt(1.0f + right.X - upwards.Y - forwards.Z) * 0.5f;
            float recip = 1.0f / (4.0f * quaternion.x);
            quaternion.w = (upwards.Z - forwards.Y) * recip;
            quaternion.z = (forwards.X + right.Z) * recip;
            quaternion.y = (right.Y + upwards.X) * recip;
        }
        else if (upwards.Y > forwards.Z) {
            quaternion.y = sqrt(1.0f - right.X + upwards.Y - forwards.Z) * 0.5f;
            float recip = 1.0f / (4.0f * quaternion.y);
            quaternion.z = (upwards.Z + forwards.Y) * recip;
            quaternion.w = (forwards.X - right.Z) * recip;
            quaternion.x = (right.Y + upwards.X) * recip;
        }
        else {
            quaternion.z = sqrt(1.0f - right.X - upwards.Y + forwards.Z) * 0.5f;
            float recip = 1.0f / (4.0f * quaternion.z);
            quaternion.y = (upwards.Z + forwards.Y) * recip;
            quaternion.x = (forwards.X + right.Z) * recip;
            quaternion.w = (right.Y - upwards.X) * recip;
        }
        return quaternion;
    }

    // Vector operations
    static Vector3 Normalized(Vector3 vector) {
        float mag = Magnitude(vector);
        if (mag == 0) return Vector3(0, 0, 0);
        return vector / mag;
    }

    static float Magnitude(Vector3 vector) {
        return sqrt(SqrMagnitude(vector));
    }

    static float SqrMagnitude(Vector3 v) {
        return v.X * v.X + v.Y * v.Y + v.Z * v.Z;
    }

    static float Dot(Vector3 a, Vector3 b) {
        return a.X * b.X + a.Y * b.Y + a.Z * b.Z;
    }

    static Vector3 Cross(Vector3 a, Vector3 b) {
        return Vector3(
            a.Y * b.Z - a.Z * b.Y,
            a.Z * b.X - a.X * b.Z,
            a.X * b.Y - a.Y * b.X
        );
    }

    // Check if point is inside FOV circle
    static bool IsInsideFOV(int x, int y, int centerX, int centerY, int radius) {
        if (radius <= 0) return true;
        int dx = x - centerX;
        int dy = y - centerY;
        return (dx * dx + dy * dy) <= (radius * radius);
    }

    // Calculate distance between two 2D points
    static float Distance2D(float x1, float y1, float x2, float y2) {
        float dx = x2 - x1;
        float dy = y2 - y1;
        return sqrt(dx * dx + dy * dy);
    }

private:
    static Quaternion FromToRotation(Vector3 forwards, Vector3 upwards) {
        float dot = Dot(forwards, upwards);
        float k = sqrt(SqrMagnitude(forwards) * SqrMagnitude(upwards));

        if (abs(dot / k + 1) < 0.00001f) {
            Vector3 ortho = Orthogonal(forwards);
            return Quaternion(ortho.X, ortho.Y, ortho.Z, 0);
        }

        Vector3 cross = Cross(forwards, upwards);
        return NormalizedQuaternion(Quaternion(cross.X, cross.Y, cross.Z, dot + k));
    }

    static Vector3 Orthogonal(Vector3 v) {
        return v.Z < v.X ? Vector3(v.Y, -v.X, 0) : Vector3(0, -v.Z, v.Y);
    }

    static Quaternion NormalizedQuaternion(Quaternion rotation) {
        float norm = sqrt(rotation.x * rotation.x + rotation.y * rotation.y + 
                         rotation.z * rotation.z + rotation.w * rotation.w);
        return Quaternion(rotation.x / norm, rotation.y / norm, 
                         rotation.z / norm, rotation.w / norm);
    }
};
