{"runtimeTarget": {"name": ".NETCoreApp,Version=v7.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v7.0": {}, ".NETCoreApp,Version=v7.0/win-x64": {"Client/1.0.0": {"dependencies": {"ClickableTransparentOverlay": "9.3.0", "DNNE": "2.0.6", "GlobalHotKey": "1.1.0", "Guna.UI2.WinForms": "2.0.4.6", "Microsoft.DotNet.ILCompiler": "7.0.20", "Microsoft.NET.ILLink.Analyzers": "7.0.100-1.23211.1", "Microsoft.NET.ILLink.Tasks": "7.0.100-1.23211.1", "MinHook.NET": "1.1.1", "Newtonsoft.Json": "13.0.3", "SixLabors.Fonts": "2.1.2", "System.Reflection": "4.3.0", "WinFormsComInterop": "0.5.0", "Memory": "1.2.27.0", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "7.0.20", "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64": "7.0.20"}, "runtime": {"Client.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/7.0.20": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "12.0.0.0", "fileVersion": "12.0.2024.26716"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "7.0.2024.26716"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.29.30152.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "7.0.2024.26716"}, "clretwrc.dll": {"fileVersion": "7.0.2024.26716"}, "clrgc.dll": {"fileVersion": "7.0.2024.26716"}, "clrjit.dll": {"fileVersion": "7.0.2024.26716"}, "coreclr.dll": {"fileVersion": "7.0.2024.26716"}, "createdump.exe": {"fileVersion": "7.0.2024.26716"}, "hostfxr.dll": {"fileVersion": "7.0.2024.26716"}, "hostpolicy.dll": {"fileVersion": "7.0.2024.26716"}, "mscordaccore.dll": {"fileVersion": "7.0.2024.26716"}, "mscordaccore_amd64_amd64_7.0.2024.26716.dll": {"fileVersion": "7.0.2024.26716"}, "mscordbi.dll": {"fileVersion": "7.0.2024.26716"}, "mscorrc.dll": {"fileVersion": "7.0.2024.26716"}, "msquic.dll": {"fileVersion": "*******"}}}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/7.0.20": {"runtime": {"Accessibility.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26907"}, "DirectWriteForwarder.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "Microsoft.VisualBasic.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26907"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "********", "fileVersion": "7.0.2024.26907"}, "Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "PresentationCore.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "PresentationFramework-SystemCore.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "PresentationFramework-SystemData.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "PresentationFramework-SystemDrawing.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "PresentationFramework-SystemXml.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "PresentationFramework-SystemXmlLinq.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "PresentationFramework.Aero.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "PresentationFramework.Aero2.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "PresentationFramework.AeroLite.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "PresentationFramework.Classic.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "PresentationFramework.Luna.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "PresentationFramework.Royale.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "PresentationFramework.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "PresentationUI.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "ReachFramework.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Design.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26907"}, "System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Drawing.Design.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26907"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26907"}, "System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Printing.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "System.Resources.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Windows.Controls.Ribbon.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26716"}, "System.Windows.Forms.Design.Editors.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26907"}, "System.Windows.Forms.Design.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26907"}, "System.Windows.Forms.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26907"}, "System.Windows.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26907"}, "System.Windows.Input.Manipulations.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "System.Windows.Presentation.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "System.Xaml.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "UIAutomationClient.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "UIAutomationClientSideProviders.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "UIAutomationProvider.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "UIAutomationTypes.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}, "WindowsFormsIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.2024.26905"}}, "native": {"D3DCompiler_47_cor3.dll": {"fileVersion": "10.0.22621.3233"}, "PenImc_cor3.dll": {"fileVersion": "7.0.2024.26905"}, "PresentationNative_cor3.dll": {"fileVersion": "7.0.24.17201"}, "vcruntime140_cor3.dll": {"fileVersion": "14.40.33807.0"}, "wpfgfx_cor3.dll": {"fileVersion": "7.0.2024.26905"}}}, "ClickableTransparentOverlay/9.3.0": {"dependencies": {"ImGui.NET": "1.90.1.1", "SixLabors.ImageSharp": "3.1.2", "Vortice.D3DCompiler": "3.3.4", "Vortice.Direct3D11": "3.3.4"}, "runtime": {"lib/net7.0/ClickableTransparentOverlay.dll": {"assemblyVersion": "9.3.0.0", "fileVersion": "9.3.0.0"}}}, "DNNE/2.0.6": {}, "GlobalHotKey/1.1.0": {"runtime": {"lib/GlobalHotKey.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.0"}}}, "Guna.UI2.WinForms/2.0.4.6": {"dependencies": {"System.Management": "7.0.0"}, "runtime": {"lib/net7.0-windows7.0/Guna.UI2.dll": {"assemblyVersion": "2.0.4.6", "fileVersion": "2.0.4.6"}}}, "ImGui.NET/1.90.1.1": {"dependencies": {"System.Buffers": "4.4.0", "System.Numerics.Vectors": "4.4.0", "System.Runtime.CompilerServices.Unsafe": "4.4.0"}, "runtime": {"lib/net6.0/ImGui.NET.dll": {"assemblyVersion": "1.90.1.1", "fileVersion": "1.90.1.1"}}, "native": {"runtimes/win-x64/native/cimgui.dll": {"fileVersion": "0.0.0.0"}}}, "Microsoft.DotNet.ILCompiler/7.0.20": {"dependencies": {"runtime.win-x64.Microsoft.DotNet.ILCompiler": "7.0.20"}}, "Microsoft.NET.ILLink.Analyzers/7.0.100-1.23211.1": {}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23211.1": {}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "MinHook.NET/1.1.1": {"runtime": {"lib/net45/MinHook.NET.dll": {"assemblyVersion": "1.1.1.0", "fileVersion": "1.1.1.0"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "runtime.any.System.IO/4.3.0": {"runtime": {"lib/netstandard1.5/System.IO.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.6.24705.1"}}}, "runtime.any.System.Reflection/4.3.0": {"runtime": {"lib/netstandard1.5/System.Reflection.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.6.24705.1"}}}, "runtime.any.System.Reflection.Primitives/4.3.0": {"runtime": {"lib/netstandard1.3/System.Reflection.Primitives.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.24705.1"}}}, "runtime.any.System.Runtime/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}, "runtime": {"lib/netstandard1.5/System.Runtime.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.6.24705.1"}}}, "runtime.any.System.Text.Encoding/4.3.0": {"runtime": {"lib/netstandard1.3/System.Text.Encoding.dll": {"assemblyVersion": "4.0.12.0", "fileVersion": "4.6.24705.1"}}}, "runtime.any.System.Threading.Tasks/4.3.0": {"runtime": {"lib/netstandard1.3/System.Threading.Tasks.dll": {"assemblyVersion": "4.0.12.0", "fileVersion": "4.6.24705.1"}}}, "runtime.win-x64.Microsoft.DotNet.ILCompiler/7.0.20": {}, "SharpGen.Runtime/2.1.2-beta": {"runtime": {"lib/net7.0/SharpGen.Runtime.dll": {"assemblyVersion": "2.1.2.0", "fileVersion": "2.1.2.0"}}}, "SharpGen.Runtime.COM/2.1.2-beta": {"dependencies": {"SharpGen.Runtime": "2.1.2-beta"}, "runtime": {"lib/net7.0/SharpGen.Runtime.COM.dll": {"assemblyVersion": "2.1.2.0", "fileVersion": "2.1.2.0"}}}, "SixLabors.Fonts/2.1.2": {"runtime": {"lib/net6.0/SixLabors.Fonts.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.1.2.0"}}}, "SixLabors.ImageSharp/3.1.2": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.1.2.0"}}}, "System.Buffers/4.4.0": {}, "System.CodeDom/7.0.0": {"runtime": {"lib/net7.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.any.System.IO": "4.3.0"}}, "System.Management/7.0.0": {"dependencies": {"System.CodeDom": "7.0.0"}, "runtime": {"runtimes/win/lib/net7.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Numerics.Vectors/4.4.0": {}, "System.Private.Uri/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Primitives": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.any.System.Runtime": "4.3.0"}}, "System.Runtime.CompilerServices.Unsafe/4.4.0": {"runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "0.0.0.0"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Text.Encoding": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Threading.Tasks": "4.3.0"}}, "Vortice.D3DCompiler/3.3.4": {"dependencies": {"SharpGen.Runtime": "2.1.2-beta", "SharpGen.Runtime.COM": "2.1.2-beta", "Vortice.DirectX": "3.3.4", "Vortice.Mathematics": "1.7.2"}, "runtime": {"lib/net7.0/Vortice.D3DCompiler.dll": {"assemblyVersion": "3.3.4.0", "fileVersion": "3.3.4.0"}}}, "Vortice.Direct3D11/3.3.4": {"dependencies": {"SharpGen.Runtime": "2.1.2-beta", "SharpGen.Runtime.COM": "2.1.2-beta", "Vortice.DXGI": "3.3.4", "Vortice.Mathematics": "1.7.2"}, "runtime": {"lib/net7.0/Vortice.Direct3D11.dll": {"assemblyVersion": "3.3.4.0", "fileVersion": "3.3.4.0"}}}, "Vortice.DirectX/3.3.4": {"dependencies": {"SharpGen.Runtime": "2.1.2-beta", "SharpGen.Runtime.COM": "2.1.2-beta", "Vortice.Mathematics": "1.7.2"}, "runtime": {"lib/net7.0/Vortice.DirectX.dll": {"assemblyVersion": "3.3.4.0", "fileVersion": "3.3.4.0"}}}, "Vortice.DXGI/3.3.4": {"dependencies": {"SharpGen.Runtime": "2.1.2-beta", "SharpGen.Runtime.COM": "2.1.2-beta", "Vortice.DirectX": "3.3.4", "Vortice.Mathematics": "1.7.2"}, "runtime": {"lib/net7.0/Vortice.DXGI.dll": {"assemblyVersion": "3.3.4.0", "fileVersion": "3.3.4.0"}}}, "Vortice.Mathematics/1.7.2": {"runtime": {"lib/net7.0/Vortice.Mathematics.dll": {"assemblyVersion": "1.7.2.0", "fileVersion": "1.7.2.0"}}}, "WinFormsComInterop/0.5.0": {"runtime": {"lib/net7.0/WinFormsComInterop.dll": {"assemblyVersion": "0.5.0.0", "fileVersion": "0.5.0.0"}}}, "Memory/1.2.27.0": {"runtime": {"Memory.dll": {"assemblyVersion": "1.2.27.0", "fileVersion": "1.2.27.0"}}}}}, "libraries": {"Client/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/7.0.20": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/7.0.20": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "ClickableTransparentOverlay/9.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DxhW1jh50MRyZZtSKmKUjxg8ik0rvCxnUMc22UjOHLlVQtFaxS8QOIYLjEed4X+CCsl0VXJybNlvBxJisZX83Q==", "path": "clickabletransparentoverlay/9.3.0", "hashPath": "clickabletransparentoverlay.9.3.0.nupkg.sha512"}, "DNNE/2.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-C9f/e0nlN1iIvIhmQFcr84KDuEQgkxGpAX4Cde+4ZaGegiSEGRV9M4vGDhfRiw7b8DW7/zObcFOkORcEAgoMng==", "path": "dnne/2.0.6", "hashPath": "dnne.2.0.6.nupkg.sha512"}, "GlobalHotKey/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-KkEHhyM+sSh27kF1d3YlfpXdYlkfu17XCiI3WfYy1cn65y3loR7f+GMkMwHTgf0xSyS3QdXTKCRfVLzb7jgJFQ==", "path": "globalhotkey/1.1.0", "hashPath": "globalhotkey.1.1.0.nupkg.sha512"}, "Guna.UI2.WinForms/2.0.4.6": {"type": "package", "serviceable": true, "sha512": "sha512-NYzSJp7e47Cz3NgGCHvzadash8ycmxxUnpLg4Rr/MSYG9WDw0HOoTI4SkbDW+HbACrf6tsWYCuzWkgvjuM/O6w==", "path": "guna.ui2.winforms/2.0.4.6", "hashPath": "guna.ui2.winforms.2.0.4.6.nupkg.sha512"}, "ImGui.NET/1.90.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-/VNgkRywtXV72cpxtBj09nZX9Yx+xG3NxiwEEXPVcK+pHKHA31cPJKy425tDGj68tgRbCS+C+DG8puBj3iJ4zA==", "path": "imgui.net/1.90.1.1", "hashPath": "imgui.net.1.90.1.1.nupkg.sha512"}, "Microsoft.DotNet.ILCompiler/7.0.20": {"type": "package", "serviceable": true, "sha512": "sha512-9gGpu74pc/FBQuHqTb0pnvTftfSpQIkOfic7z9czAPTlCxEvY8rsEsYcfIX7ExvcIEr35Rf5cpxqCH09gApW3Q==", "path": "microsoft.dotnet.ilcompiler/7.0.20", "hashPath": "microsoft.dotnet.ilcompiler.7.0.20.nupkg.sha512"}, "Microsoft.NET.ILLink.Analyzers/7.0.100-1.23211.1": {"type": "package", "serviceable": true, "sha512": "sha512-0GvbEgDGcUQA9KuWcQU1WwYHXt1tBzNr1Nls/M57rM7NA/AndFwCaCEoJpJkmxRY7xLlPDBnmGp8h5+FNqUngg==", "path": "microsoft.net.illink.analyzers/7.0.100-1.23211.1", "hashPath": "microsoft.net.illink.analyzers.7.0.100-1.23211.1.nupkg.sha512"}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23211.1": {"type": "package", "serviceable": true, "sha512": "sha512-tvG8XZYLjT0o3WicCyKBZysVWo1jC9HdCFmNRmddx3WbAz0UCsd0qKZqpiEo99VLA8Re+FzWK51OcRldQPbt2Q==", "path": "microsoft.net.illink.tasks/7.0.100-1.23211.1", "hashPath": "microsoft.net.illink.tasks.7.0.100-1.23211.1.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "MinHook.NET/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-k+cLk84AxAAiOB/qBLT80p6/y9g8jXGzhLtxRwfbyNR1opxOYOmQWv+v58hQ2xyT+xkNM8GVhY1VTLuPTwP8kw==", "path": "minhook.net/1.1.1", "hashPath": "minhook.net.1.1.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "runtime.any.System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SDZ5AD1DtyRoxYtEcqQ3HDlcrorMYXZeCt7ZhG9US9I5Vva+gpIWDGMkcwa5XiKL0ceQKRZIX2x0XEjLX7PDzQ==", "path": "runtime.any.system.io/4.3.0", "hashPath": "runtime.any.system.io.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hLC3A3rI8jipR5d9k7+f0MgRCW6texsAp0MWkN/ci18FMtQ9KH7E2vDn/DH2LkxsszlpJpOn9qy6Z6/69rH6eQ==", "path": "runtime.any.system.reflection/4.3.0", "hashPath": "runtime.any.system.reflection.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nrm1p3armp6TTf2xuvaa+jGTTmncALWFq22CpmwRvhDf6dE9ZmH40EbOswD4GnFLrMRS0Ki6Kx5aUPmKK/hZBg==", "path": "runtime.any.system.reflection.primitives/4.3.0", "hashPath": "runtime.any.system.reflection.primitives.4.3.0.nupkg.sha512"}, "runtime.any.System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fRS7zJgaG9NkifaAxGGclDDoRn9HC7hXACl52Or06a/fxdzDajWb5wov3c6a+gVSlekRoexfjwQSK9sh5um5LQ==", "path": "runtime.any.system.runtime/4.3.0", "hashPath": "runtime.any.system.runtime.4.3.0.nupkg.sha512"}, "runtime.any.System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+ihI5VaXFCMVPJNstG4O4eo1CfbrByLxRrQQTqOTp1ttK0kUKDqOdBSTaCB2IBk/QtjDrs6+x4xuezyMXdm0HQ==", "path": "runtime.any.system.text.encoding/4.3.0", "hashPath": "runtime.any.system.text.encoding.4.3.0.nupkg.sha512"}, "runtime.any.System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OhBAVBQG5kFj1S+hCEQ3TUHBAEtZ3fbEMgZMRNdN8A0Pj4x+5nTELEqL59DU0TjKVE6II3dqKw4Dklb3szT65w==", "path": "runtime.any.system.threading.tasks/4.3.0", "hashPath": "runtime.any.system.threading.tasks.4.3.0.nupkg.sha512"}, "runtime.win-x64.Microsoft.DotNet.ILCompiler/7.0.20": {"type": "package", "serviceable": true, "sha512": "sha512-Xn/ZM3iUfLMdEKJeOJMJVLOEsIkH3jmasq8/ig9xKkk/3myJLpN9d8wj8fu48ztiRievi8n3Zpp5uDUZI1phmQ==", "path": "runtime.win-x64.microsoft.dotnet.ilcompiler/7.0.20", "hashPath": "runtime.win-x64.microsoft.dotnet.ilcompiler.7.0.20.nupkg.sha512"}, "SharpGen.Runtime/2.1.2-beta": {"type": "package", "serviceable": true, "sha512": "sha512-nqZAjfEG1jX1ivvdZLsi6Pkt0DiOJyuOgRgldNFsmjXFPhxUbXQibofLSwuDZidL2kkmtTF8qLoRIeqeVdXgYw==", "path": "sharpgen.runtime/2.1.2-beta", "hashPath": "sharpgen.runtime.2.1.2-beta.nupkg.sha512"}, "SharpGen.Runtime.COM/2.1.2-beta": {"type": "package", "serviceable": true, "sha512": "sha512-HBCrb6HfnUWx9v5/GjJeBr5DuodZLnHlFQQYXPrQs1Hbe1c6Wd0uCXf+SJp4hW8fQNxjXEu0FgiyHGlA/SRzRw==", "path": "sharpgen.runtime.com/2.1.2-beta", "hashPath": "sharpgen.runtime.com.2.1.2-beta.nupkg.sha512"}, "SixLabors.Fonts/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-UGl99i9hCJ4MXLaoGw2aq8nklIL/31QcRU7oqQza4AqCg54XojtIIRczj9aO7zJPDGF+XoA3yJ6X1NOfhZOrWA==", "path": "sixlabors.fonts/2.1.2", "hashPath": "sixlabors.fonts.2.1.2.nupkg.sha512"}, "SixLabors.ImageSharp/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-PYdR6GUI+gW6LBaAQKTik0Ai8oLpFAz3a/KrVusxoTg3kf7F3cuIqKMhJGsuQcmDHCF+iD81Pyn4cexyHrb1ZA==", "path": "sixlabors.imagesharp/3.1.2", "hashPath": "sixlabors.imagesharp.3.1.2.nupkg.sha512"}, "System.Buffers/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-AwarXzzoDwX6BgrhjoJsk6tUezZEozOT5Y9QKF94Gl4JK91I4PIIBkBco9068Y9/Dra8Dkbie99kXB8+1BaYKw==", "path": "system.buffers/4.4.0", "hashPath": "system.buffers.4.4.0.nupkg.sha512"}, "System.CodeDom/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GLltyqEsE5/3IE+zYRP5sNa1l44qKl9v+bfdMcwg+M9qnQf47wK3H0SUR/T+3N4JEQXF3vV4CSuuo0rsg+nq2A==", "path": "system.codedom/7.0.0", "hashPath": "system.codedom.7.0.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.Management/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-A4jed4QUviDOm7fJNKAJObEAEkEUXmkGL/w0iyCYTzrl1rezTj8LGFHfsVst4Vb9JwFcTpboiDrvdST48avBpw==", "path": "system.management/7.0.0", "hashPath": "system.management.7.0.0.nupkg.sha512"}, "System.Numerics.Vectors/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-UiLzLW+Lw6HLed1Hcg+8jSRttrbuXv7DANVj0DkL9g6EnnzbL75EB7EWsw5uRbhxd/4YdG8li5XizGWepmG3PQ==", "path": "system.numerics.vectors/4.4.0", "hashPath": "system.numerics.vectors.4.4.0.nupkg.sha512"}, "System.Private.Uri/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I4SwANiUGho1esj4V4oSlPllXjzCZDE+5XXso2P03LW2vOda2Enzh8DWOxwN6hnrJyp314c7KuVu31QYhRzOGg==", "path": "system.private.uri/4.3.0", "hashPath": "system.private.uri.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-9dLLuBxr5GNmOfl2jSMcsHuteEg32BEfUotmmUkmZjpR3RpVHE8YQwt0ow3p6prwA1ME8WqDVZqrr8z6H8G+Kw==", "path": "system.runtime.compilerservices.unsafe/4.4.0", "hashPath": "system.runtime.compilerservices.unsafe.4.4.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "Vortice.D3DCompiler/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-G0yAN1us091ulIzPfMVJeJf8JX3Tu/Wu0l4zip4HPLfVUWWxsBJF0eaEdwSPrUyz97LEG6zjHruL4sZoNRBHcA==", "path": "vortice.d3dcompiler/3.3.4", "hashPath": "vortice.d3dcompiler.3.3.4.nupkg.sha512"}, "Vortice.Direct3D11/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-ma8ysRo7WBmJwt+nNpFi0icHKIgNud6Nb0rz6XPN4V/X93nkVZiN3t96cqdy0kC0QtzYlpFccQ2d/JPrvHt6og==", "path": "vortice.direct3d11/3.3.4", "hashPath": "vortice.direct3d11.3.3.4.nupkg.sha512"}, "Vortice.DirectX/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-HWvIGYz8XtWZpJW8xGYq5gex/HUPf5u3kDY7w/5WLCXzuR+hqLB7p6tInYePnuVLvfuI/rKMMhlzmnceoP3tIw==", "path": "vortice.directx/3.3.4", "hashPath": "vortice.directx.3.3.4.nupkg.sha512"}, "Vortice.DXGI/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-toya4AmKpcVJyaZzDJnrZlYsVSQxHTTrwWYee3G4Dzthww5HTgtGluN5ciQKl0KOChbR9xjgSK8+flkw6RNdTg==", "path": "vortice.dxgi/3.3.4", "hashPath": "vortice.dxgi.3.3.4.nupkg.sha512"}, "Vortice.Mathematics/1.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-WUUzB3MowHdOqNfSMAms5Ovra+SKgLC4ttH1w75ra3srDGctebSg+aywm7hLdvDn592ydgdbOJN11JKsyAcX+A==", "path": "vortice.mathematics/1.7.2", "hashPath": "vortice.mathematics.1.7.2.nupkg.sha512"}, "WinFormsComInterop/0.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-afCh9mbBjXfkTansOWtmCDjdtzudIcFLxRUqrgL3CfaariduFMhAleKyp/QKWP+xSfSzE8FwQWNLqH+bUE0csw==", "path": "winformscominterop/0.5.0", "hashPath": "winformscominterop.0.5.0.nupkg.sha512"}, "Memory/1.2.27.0": {"type": "reference", "serviceable": false, "sha512": ""}}, "runtimes": {"win-x64": ["win", "any", "base"], "win-x64-aot": ["win-aot", "win-x64", "win", "aot", "any", "base"], "win10-x64": ["win10", "win81-x64", "win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win10-x64-aot": ["win10-aot", "win10-x64", "win10", "win81-x64-aot", "win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win7-x64": ["win7", "win-x64", "win", "any", "base"], "win7-x64-aot": ["win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win8-x64": ["win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win8-x64-aot": ["win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win81-x64": ["win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win81-x64-aot": ["win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"]}}