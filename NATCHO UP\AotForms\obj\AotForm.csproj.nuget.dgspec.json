{"format": 1, "restore": {"F:\\New folder (2)\\!   I N T E R N A L\\AotForms\\AotForm.csproj": {}}, "projects": {"F:\\New folder (2)\\!   I N T E R N A L\\AotForms\\AotForm.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\New folder (2)\\!   I N T E R N A L\\AotForms\\AotForm.csproj", "projectName": "AotForm", "projectPath": "F:\\New folder (2)\\!   I N T E R N A L\\AotForms\\AotForm.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\New folder (2)\\!   I N T E R N A L\\AotForms\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "dependencies": {"ClickableTransparentOverlay": {"target": "Package", "version": "[9.3.0, )"}, "DNNE": {"target": "Package", "version": "[2.0.6, )"}, "Guna.UI2.WinForms": {"target": "Package", "version": "[2.0.4.6, )"}, "Microsoft.DotNet.ILCompiler": {"suppressParent": "All", "target": "Package", "version": "[7.0.20, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23401.1, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23401.1, )", "autoReferenced": true}, "WinFormsComInterop": {"target": "Package", "version": "[0.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "runtime.win-x64.Microsoft.DotNet.ILCompiler", "version": "[7.0.20, 7.0.20]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.303\\RuntimeIdentifierGraph.json"}}}}}