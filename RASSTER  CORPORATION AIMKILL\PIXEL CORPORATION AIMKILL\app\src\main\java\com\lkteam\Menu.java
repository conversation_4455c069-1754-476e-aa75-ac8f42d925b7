package com.lkteam;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Canvas;
import android.graphics.PixelFormat;
import android.graphics.PorterDuff;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.RippleDrawable;
import android.os.Build;
import android.text.Html;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import com.topjohnwu.superuser.Shell;

import java.io.File;

public class Menu {

    //Native Functions
    public static native void Functions();
    public static native void ChangesID(int ID, int Value);
    public static native void Init();

    private String target = "com.dts.freefireth";
    private int injectType;

    //Variables Menu
    private int buttonClick = 0;
    public static int PrimaryColor = 0xFFA70000;
    private static Context context;
    private static Utils utils;
    private native String imageBase64();

    //Parte Do Sistema De Janela
    private WindowManager windowManager;
    private WindowManager.LayoutParams windowManagerParams;
    private FrameLayout frameLayout;

    //DrawView Global
    DrawView drawView;

    //Parte do Draw
    WindowManager.LayoutParams windowManagerDrawViewParams;
    public static native void OnDrawLoad(DrawView drawView, Canvas canvas);
    public void DrawCanvas() {
        int LAYOUT_FLAG;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            LAYOUT_FLAG = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            LAYOUT_FLAG = WindowManager.LayoutParams.TYPE_PHONE;
        }

        drawView = new DrawView(context);
        windowManagerDrawViewParams = new WindowManager.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT, LAYOUT_FLAG, WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL | WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_FULLSCREEN, PixelFormat.TRANSPARENT);
        windowManagerDrawViewParams.gravity = Gravity.CENTER;
        windowManager.addView(drawView, windowManagerDrawViewParams);
    }

    //Parte Do Template Do Menu
    static LinearLayout container_features;

    public Menu(Context globContext, int glob_injectType) {
        context = globContext;
        utils = new Utils(context);
        injectType = glob_injectType;
        System.loadLibrary("hawdawdawdawda");
        onCreate();
    }

    public void onCreate() {
        onCreateSystemWindow();
        onCreateTemplate();
    }

    //Criar Template
    public void onCreateTemplate() {
        GradientDrawable gradientDrawable_container = new GradientDrawable();
        gradientDrawable_container.setColor(0xFF111111);
        gradientDrawable_container.setCornerRadius(utils.FixDP(8));

        LinearLayout container = new LinearLayout(context);
        container.setOrientation(LinearLayout.VERTICAL);

        final LinearLayout container_menu = new LinearLayout(context);
        container_menu.setLayoutParams(new LinearLayout.LayoutParams(utils.FixDP(210), ViewGroup.LayoutParams.WRAP_CONTENT));
        container_menu.setBackgroundColor(0xFF111111);
        container_menu.setVisibility(View.GONE);
        container_menu.setOrientation(LinearLayout.VERTICAL);
        container_menu.setBackground(gradientDrawable_container);

        final ImageBase64 icon_cheat = new ImageBase64(context);
        icon_cheat.setLayoutParams(new LinearLayout.LayoutParams(utils.FixDP(50), utils.FixDP(50)));
        icon_cheat.setImageBase64(imageBase64());
        icon_cheat.setOnTouchListener(onTouchListener());
        icon_cheat.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                icon_cheat.setVisibility(View.GONE);
                container_menu.setVisibility(View.VISIBLE);
            }
        });

        LinearLayout container_top = new LinearLayout(context);
        container_top.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        container_top.setPadding(utils.FixDP(8), utils.FixDP(8), utils.FixDP(8), utils.FixDP(8));
        container_top.setGravity(Gravity.CENTER);
        container_top.setOrientation(LinearLayout.HORIZONTAL);

        ImageBase64 icon_menu = new ImageBase64(context);
        icon_menu.setLayoutParams(new LinearLayout.LayoutParams(utils.FixDP(45), utils.FixDP(45)));
        icon_menu.setImageBase64(imageBase64());

        final LinearLayout container_center = new LinearLayout(context);
        container_center.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, utils.FixDP(180)));
        container_center.setGravity(Gravity.CENTER);

        final ScrollView scrollView_center = new ScrollView(context);
        scrollView_center.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));

        container_features = new LinearLayout(context);
        container_features.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        container_features.setPadding(utils.FixDP(8), 0, utils.FixDP(8), 0);
        container_features.setOrientation(LinearLayout.VERTICAL);

        final ProgressBar progressBar = new ProgressBar(context);
        progressBar.setLayoutParams(new LinearLayout.LayoutParams(utils.FixDP(40), utils.FixDP(40)));
        progressBar.getIndeterminateDrawable().setColorFilter(PrimaryColor, PorterDuff.Mode.SRC_IN);

        LinearLayout container_bottom = new LinearLayout(context);
        container_bottom.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        container_bottom.setPadding(utils.FixDP(3), utils.FixDP(8), utils.FixDP(3), utils.FixDP(3));
        container_bottom.setOrientation(LinearLayout.VERTICAL);
        container_bottom.setGravity(Gravity.RIGHT | Gravity.CENTER_VERTICAL);

        GradientDrawable gradientDrawable_inject_close = new GradientDrawable();
        gradientDrawable_inject_close.setColor(PrimaryColor);
        gradientDrawable_inject_close.setCornerRadius(utils.FixDP(8));
        RippleDrawable rippleDrawable = new RippleDrawable(ColorStateList.valueOf(0xFF111111), gradientDrawable_inject_close, null);

        final Button inject_close = new Button(context);
        inject_close.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, utils.FixDP(33)));
        inject_close.setPadding(0, 0, 0, 0);
        inject_close.setText("INJECT");
        inject_close.setTextSize(10);
        inject_close.setTextColor(0xFFFFFFFF);
        inject_close.setBackground(rippleDrawable);
        inject_close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (buttonClick == 0) {
                    if (injectType == 0) {
                        if (InjectX32("libjifjf.so")) {
                            Init();
                            progressBar.setVisibility(View.GONE);
                            inject_close.setText("CLOSE");
                            container_center.addView(scrollView_center);
                            buttonClick++;
                        }
                    } else if (injectType == 1) {
                        if (InjectX86("libjifjf.so")) {
                            Init();
                            progressBar.setVisibility(View.GONE);
                            inject_close.setText("CLOSE");
                            container_center.addView(scrollView_center);
                            buttonClick++;
                        }
                    }
                } else if (buttonClick == 1) {
                    icon_cheat.setVisibility(View.VISIBLE);
                    container_menu.setVisibility(View.GONE);
                }
            }
        });

        frameLayout.addView(container);
        container.addView(icon_cheat);
        container.addView(container_menu);

        container_menu.addView(container_top);
        container_top.addView(icon_menu);

        container_menu.addView(container_center);
        container_center.addView(progressBar);

        scrollView_center.addView(container_features);

        container_menu.addView(container_bottom);
        container_bottom.addView(inject_close);
    }

    //Criar Sistema Da Janela
    public void onCreateSystemWindow() {
        int LAYOUT_FLAG;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            LAYOUT_FLAG = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            LAYOUT_FLAG = WindowManager.LayoutParams.TYPE_PHONE;
        }

        frameLayout = new FrameLayout(context);
        frameLayout.setLayoutParams(new LinearLayout.LayoutParams(-2, -2));
        frameLayout.setOnTouchListener(onTouchListener());
        frameLayout.setAlpha(0.8f);

        windowManagerParams = new WindowManager.LayoutParams(WindowManager.LayoutParams.WRAP_CONTENT, WindowManager.LayoutParams.WRAP_CONTENT, LAYOUT_FLAG, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_LAYOUT_IN_OVERSCAN | WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM | WindowManager.LayoutParams.FLAG_SPLIT_TOUCH, PixelFormat.TRANSPARENT);
        windowManagerParams.gravity = Gravity.TOP | Gravity.LEFT;
        windowManagerParams.x = 15;
        windowManagerParams.y = 15;

        windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        DrawCanvas();
        windowManager.addView(frameLayout, windowManagerParams);
    }

    //OnTouchListener Do Menu
    private View.OnTouchListener onTouchListener() {
        return new View.OnTouchListener() {

            private int x;
            private int y;

            @Override
            public boolean onTouch(View v, MotionEvent event) {

                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        x = (int) event.getRawX();
                        y = (int) event.getRawY();
                        frameLayout.setAlpha(0.8f);
                        break;

                    case MotionEvent.ACTION_MOVE:
                        int nowX = (int) event.getRawX();
                        int nowY = (int) event.getRawY();
                        int movedX = nowX - x;
                        int movedY = nowY - y;
                        x = nowX;
                        y = nowY;
                        windowManagerParams.x = windowManagerParams.x + movedX;
                        windowManagerParams.y = windowManagerParams.y + movedY;
                        windowManager.updateViewLayout(frameLayout, windowManagerParams);
                        break;

                    case MotionEvent.ACTION_UP:
                        frameLayout.setAlpha(0.9f);
                        break;
                    default:
                        break;
                }
                return false;
            }
        };
    }

    public static void addCategory(String name) {
        GradientDrawable gradientDrawable = new GradientDrawable();
        gradientDrawable.setColor(PrimaryColor);
        gradientDrawable.setCornerRadius(utils.FixDP(4));

        LinearLayout linearLayout = new LinearLayout(context);
        linearLayout.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, utils.FixDP(25)));
        linearLayout.setBackground(gradientDrawable);
        linearLayout.setGravity(Gravity.CENTER);

        TextView textView = new TextView(context);
        textView.setText(name);
        textView.setTextSize(11);
        textView.setTextColor(0xFFFFFFFF);

        container_features.addView(linearLayout);
        linearLayout.addView(textView);
    }

    public static void addSwitch(String name, final int ID) {
        LinearLayout linearLayout = new LinearLayout(context);
        linearLayout.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        linearLayout.setPadding(0, utils.FixDP(2), 0, utils.FixDP(2));
        linearLayout.setOrientation(LinearLayout.HORIZONTAL);
        linearLayout.setGravity(Gravity.CENTER);

        TextView textView = new TextView(context);
        textView.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT, 1));
        textView.setGravity(Gravity.CENTER_VERTICAL);
        textView.setText(name);
        textView.setTextColor(0xFFFFFFFF);
        textView.setTextSize(11);

        final SwitchStyle switchStyle = new SwitchStyle(context);
        switchStyle.setLayoutParams(new LinearLayout.LayoutParams(65, 35));
        switchStyle.setOnCheckedChangeListener(new SwitchStyle.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(SwitchStyle view, boolean isChecked) {
                ChangesID(ID, 0);
            }
        });

        linearLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                switchStyle.setChecked(!switchStyle.isChecked());
            }
        });

        linearLayout.addView(textView);
        linearLayout.addView(switchStyle);
        container_features.addView(linearLayout);
    }

    public static void addSeekBar(final String name, int value, int max, final String type, final int ID) {
        LinearLayout linearLayout = new LinearLayout(context);
        linearLayout.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        linearLayout.setPadding(0, utils.FixDP(2), 0, utils.FixDP(2));
        linearLayout.setOrientation(LinearLayout.VERTICAL);

        final TextView textView = new TextView(context);
        textView.setText(name.concat(": ") + value + type);
        textView.setTextSize(11);
        textView.setTextColor(0xFFFFFFFF);
        if (type.equals("Color")) {
            if(value == 0) {
                textView.setText(Html.fromHtml(name + ": <font color='#ffffff'>" + "Branco" + "</font>"));
            } else if(value == 1) {
                textView.setText(Html.fromHtml(name + ": <font color='#00FF00'>" + "Verde" + "</font>"));
            } else if(value == 2) {
                textView.setText(Html.fromHtml(name + ": <font color='#0000FF'>" + "Azul" + "</font>"));
            } else if(value == 3) {
                textView.setText(Html.fromHtml(name + ": <font color='#FF0000'>" + "Vermelho" + "</font>"));
            } else if(value == 4) {
                textView.setText(Html.fromHtml(name + ": <font color='#000000'>" + "Preto" + "</font>"));
            } else if(value == 5) {
                textView.setText(Html.fromHtml(name + ": <font color='#FFFF00'>" + "Amarelo" + "</font>"));
            } else if(value == 6) {
                textView.setText(Html.fromHtml(name + ": <font color='#00FFFF'>" + "Ciano" + "</font>"));
            } else if(value == 7) {
                textView.setText(Html.fromHtml(name + ": <font color='#FF00FF'>" + "Magenta" + "</font>"));
            } else if(value == 8) {
                textView.setText(Html.fromHtml(name + ": <font color='#808080'>" + "Cinza" + "</font>"));
            } else if(value == 9) {
                textView.setText(Html.fromHtml(name + ": <font color='#A020F0'>" + "Roxo" + "</font>"));
            }
        } else if (type.equals("BoxType")) {
            if (value == 0) {
                textView.setText(name.concat(": Stroke"));
            } else if (value == 1) {
                textView.setText(name.concat(": Filled"));
            } else if (value == 2) {
                textView.setText(name.concat(": Rounded"));
            }
        } else if (type.equals("LineType")) {
            if (value == 0) {
                textView.setText(name.concat(": Top"));
            } else if (value == 1) {
                textView.setText(name.concat(": Center"));
            } else if (value == 2) {
                textView.setText(name.concat(": Bottom"));
            }
        }

        SeekBar seekBar = new SeekBar(context);
        seekBar.getThumb().setColorFilter(PrimaryColor, PorterDuff.Mode.SRC_IN);
        seekBar.getProgressDrawable().setColorFilter(PrimaryColor, PorterDuff.Mode.SRC_IN);
        seekBar.setProgress(value);
        seekBar.setMax(max);
        if (type.equals("Color")) {
            seekBar.setMax(9);
        } else if (type.equals("BoxType")) {
            seekBar.setMax(2);
        } else if (type.equals("LineType")) {
            seekBar.setMax(2);
        }

        seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int i, boolean b) {
                if (type.equals("Color")) {
                    if(i == 0) {
                        textView.setText(Html.fromHtml(name + ": <font color='#ffffff'>" + "Branco" + "</font>"));
                    } else if(i == 1) {
                        textView.setText(Html.fromHtml(name + ": <font color='#00FF00'>" + "Verde" + "</font>"));
                    } else if(i == 2) {
                        textView.setText(Html.fromHtml(name + ": <font color='#0000FF'>" + "Azul" + "</font>"));
                    } else if(i == 3) {
                        textView.setText(Html.fromHtml(name + ": <font color='#FF0000'>" + "Vermelho" + "</font>"));
                    } else if(i == 4) {
                        textView.setText(Html.fromHtml(name + ": <font color='#000000'>" + "Preto" + "</font>"));
                    } else if(i == 5) {
                        textView.setText(Html.fromHtml(name + ": <font color='#FFFF00'>" + "Amarelo" + "</font>"));
                    } else if(i == 6) {
                        textView.setText(Html.fromHtml(name + ": <font color='#00FFFF'>" + "Ciano" + "</font>"));
                    } else if(i == 7) {
                        textView.setText(Html.fromHtml(name + ": <font color='#FF00FF'>" + "Magenta" + "</font>"));
                    } else if(i == 8) {
                        textView.setText(Html.fromHtml(name + ": <font color='#808080'>" + "Cinza" + "</font>"));
                    } else if(i == 9) {
                        textView.setText(Html.fromHtml(name + ": <font color='#A020F0'>" + "Roxo" + "</font>"));
                    }
                } else if (type.equals("BoxType")) {
                    if (i == 0) {
                        textView.setText(name.concat(": Stroke"));
                    } else if (i == 1) {
                        textView.setText(name.concat(": Filled"));
                    } else if (i == 2) {
                        textView.setText(name.concat(": Corner"));
                    }
                } else if (type.equals("LineType")) {
                    if (i == 0) {
                        textView.setText(name.concat(": Top"));
                    } else if (i == 1) {
                        textView.setText(name.concat(": Center"));
                    } else if (i == 2) {
                        textView.setText(name.concat(": Bottom"));
                    }
                } else {
                    textView.setText(name.concat(": ") + i + type);
                }

                ChangesID(ID, i);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });

        container_features.addView(linearLayout);
        linearLayout.addView(textView);
        linearLayout.addView(seekBar);
    }



    private boolean InjectX86(String Lib) {
        try {
            String injector = context.getApplicationInfo().nativeLibraryDir + File.separator + "liblkteam.so";
            String payload_source = context.getApplicationInfo().nativeLibraryDir + File.separator + Lib;
            String payload_dest = "/data/local/" + Lib;
            String payload_dest2 = "/data/local/libifuhiufoi.so";

            Shell.su("cp " + payload_source + " " + payload_dest).exec();
            Shell.su("cp " + injector + " " + payload_dest2).exec();
            Shell.su("su -c chmod 777 " + payload_dest).exec();
            Shell.su("su -c chmod 777 " + payload_dest2).exec();
            Shell.su("su -c " + payload_dest2 + " " + target + " " + payload_dest).exec();
            Shell.su("rm -f " + payload_dest).exec();
            Shell.su("rm -f " + payload_dest2).exec();
            Functions();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    private boolean InjectX32(String Lib) {
        try {
            String injector = context.getApplicationInfo().nativeLibraryDir + File.separator + "libinjectMobile.so";
            String payload_source = context.getApplicationInfo().nativeLibraryDir + File.separator + Lib;
            String payload_dest = "/data/local/" + Lib;
            String payload_dest2 = "/data/local/libinject.so";

            Shell.su("cp " + payload_source + " " + payload_dest).exec();
            Shell.su("cp " + injector + " " + payload_dest2).exec();
            Shell.su("su -c chmod 755 " + payload_dest).exec();
            Shell.su("su -c chmod 777 " + payload_dest2).exec();
            Shell.su("su -c " + payload_dest2 + " -f -n " + target + " -so " + payload_dest + " --hide-memory").exec();
            Shell.su("rm -f " + payload_dest).exec();
            Shell.su("rm -f " + payload_dest2).exec();
            Functions();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}