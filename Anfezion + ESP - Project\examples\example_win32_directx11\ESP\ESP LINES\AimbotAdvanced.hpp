#pragma once
#include "Vector3.hpp"
#include "MathUtils.hpp"
#include "Offset.hpp"
#include "Unity.hh"
#include "Bones.hpp"
#include <vector>
#include <algorithm>
#include <chrono>

// Forward declarations for external functions
extern bool ReadZ(uint32_t address, uint32_t& value);
extern bool ReadZ(uint32_t address, bool& value);
extern bool ReadZ(uint32_t address, int& value);
extern bool ReadZ(uint32_t address, Vector3& value);
extern bool ReadZ(uint32_t address, Matrix4x4& value);
extern bool WriteZ(uint32_t address, const Vector3& value);
extern bool WriteZ(uint32_t address, const Quaternion& value);
extern bool WriteZ(uint32_t address, uint32_t value);
extern void GetNodePosition(uint32_t bone, Vector3& position);
extern ImVec2 WorldToScreenImVec2(Matrix4x4 viewMatrix, Vector3 worldPos, int width, int height);

struct AimbotTarget {
    uint32_t entityAddress;
    Vector3 headPosition;
    Vector3 screenPosition;
    float distance;
    float crosshairDistance;
    bool isVisible;
    bool isKnocked;
    bool isDead;
};

class AimbotAdvanced {
private:
    static std::vector<AimbotTarget> validTargets;
    static AimbotTarget* currentTarget;
    static std::chrono::steady_clock::time_point lastTargetTime;
    
public:
    // Aimbot settings
    static bool enableSilentAim;
    static bool enableVisibleAim;
    static bool enableNovaAim;
    static bool ignoreKnocked;
    static bool visibleOnly;
    static float maxDistance;
    static float fovRadius;
    static float smoothness;
    static float yBias;
    static int targetBone; // 0=Head, 1=Neck, 2=Chest
    
    // Initialize aimbot system
    static void Initialize() {
        enableSilentAim = false;
        enableVisibleAim = false;
        enableNovaAim = false;
        ignoreKnocked = true;
        visibleOnly = true;
        maxDistance = 300.0f;
        fovRadius = 60.0f;
        smoothness = 0.05f;
        yBias = 0.0f;
        targetBone = 0;
        currentTarget = nullptr;
        lastTargetTime = std::chrono::steady_clock::now();
    }
    
    // Main aimbot update function
    static void Update(uint32_t currentMatch, Matrix4x4 viewMatrix, Vector3 localCameraPos, 
                      int screenWidth, int screenHeight) {
        
        if (!enableSilentAim && !enableVisibleAim && !enableNovaAim) {
            return;
        }
        
        // Clear previous targets
        validTargets.clear();
        
        // Get entities
        uint32_t entityDictionary;
        if (!ReadZ(currentMatch + DictionaryEntities, entityDictionary)) return;

        uint32_t entities;
        if (!ReadZ(entityDictionary + 0x14, entities)) return;
        entities = entities + 0x10;

        uint32_t entitiesCount;
        if (!ReadZ(entityDictionary + 0x18, entitiesCount)) return;
        
        // Find valid targets
        FindValidTargets(entities, entitiesCount, viewMatrix, localCameraPos, 
                        screenWidth, screenHeight);
        
        // Select best target
        AimbotTarget* bestTarget = SelectBestTarget(screenWidth, screenHeight);
        
        if (bestTarget) {
            // Apply aimbot based on type
            if (enableSilentAim) {
                ApplySilentAim(bestTarget, currentMatch);
            }
            else if (enableVisibleAim) {
                ApplyVisibleAim(bestTarget);
            }
            else if (enableNovaAim) {
                ApplyNovaAim(bestTarget, localCameraPos, currentMatch);
            }
        }
    }
    
private:
    // Find all valid targets
    static void FindValidTargets(uint32_t entities, uint32_t entitiesCount, 
                               Matrix4x4 viewMatrix, Vector3 localCameraPos,
                               int screenWidth, int screenHeight) {
        
        for (uint32_t i = 0; i < entitiesCount; i++) {
            uint32_t entity;
            if (!ReadZ(entities + (i * 0x8), entity)) continue;
            
            // Check if entity is valid
            if (!IsValidTarget(entity)) continue;
            
            // Get target position based on bone selection
            Vector3 targetPos = GetTargetBonePosition(entity);
            if (targetPos.X == 0 && targetPos.Y == 0 && targetPos.Z == 0) continue;
            
            // Calculate distance
            float distance = Vector3::Distance(localCameraPos, targetPos);
            if (distance > maxDistance) continue;
            
            // Convert to screen coordinates
            auto screenPos = WorldToScreenImVec2(viewMatrix, targetPos, screenWidth, screenHeight);
            if (screenPos.x < 0 || screenPos.y < 0 || 
                screenPos.x > screenWidth || screenPos.y > screenHeight) continue;
            
            // Calculate crosshair distance
            float centerX = screenWidth / 2.0f;
            float centerY = screenHeight / 2.0f;
            float crosshairDist = MathUtils::Distance2D(screenPos.x, screenPos.y, centerX, centerY);
            
            // Check FOV
            if (!MathUtils::IsInsideFOV(screenPos.x, screenPos.y, centerX, centerY, fovRadius)) continue;
            
            // Create target
            AimbotTarget target;
            target.entityAddress = entity;
            target.headPosition = targetPos;
            target.screenPosition = Vector3(screenPos.x, screenPos.y, 0);
            target.distance = distance;
            target.crosshairDistance = crosshairDist;
            target.isVisible = CheckVisibility(entity);
            target.isKnocked = CheckKnocked(entity);
            target.isDead = CheckDead(entity);
            
            validTargets.push_back(target);
        }
    }
    
    // Select the best target from valid targets
    static AimbotTarget* SelectBestTarget(int screenWidth, int screenHeight) {
        if (validTargets.empty()) return nullptr;
        
        // Sort by crosshair distance (closest to center)
        std::sort(validTargets.begin(), validTargets.end(), 
                 [](const AimbotTarget& a, const AimbotTarget& b) {
                     return a.crosshairDistance < b.crosshairDistance;
                 });
        
        // Return the closest target to crosshair
        return &validTargets[0];
    }
    
    // Apply Silent Aimbot (most powerful)
    static void ApplySilentAim(AimbotTarget* target, uint32_t currentMatch) {
        uint32_t localPlayer;
        if (!ReadZ(currentMatch + LocalPlayer, localPlayer)) return;
        
        // Check if player is firing
        bool isFiring;
        if (!ReadZ(localPlayer + 0x454, isFiring) || !isFiring) return;
        
        // Get bullet trajectory object
        uint32_t bulletTrajectory;
        if (!ReadZ(localPlayer + 0x7DC, bulletTrajectory) || bulletTrajectory == 0) return;
        
        // Get start position
        Vector3 startPosition;
        if (!ReadZ(bulletTrajectory + 0x38, startPosition)) return;
        
        // Calculate direction to target
        Vector3 direction = target->headPosition - startPosition;
        
        // Write new bullet direction
        WriteZ(bulletTrajectory + 0x2C, direction);
    }
    
    // Apply Visible Aimbot (writes to head collider)
    static void ApplyVisibleAim(AimbotTarget* target) {
        // Get head collider
        uint32_t headCollider;
        if (!ReadZ(target->entityAddress + 0x3B8, headCollider) || headCollider == 0) return;
        
        // Write to current match multiple times for reliability
        for (int i = 0; i < 10; i++) {
            WriteZ(target->entityAddress + 0x50, headCollider);
        }
    }
    
    // Apply Nova Aimbot (smooth aim rotation)
    static void ApplyNovaAim(AimbotTarget* target, Vector3 localCameraPos, uint32_t currentMatch) {
        uint32_t localPlayer;
        if (!ReadZ(currentMatch + LocalPlayer, localPlayer)) return;
        
        // Calculate rotation to target
        Quaternion targetRotation = MathUtils::GetRotationToLocation(
            target->headPosition, yBias, localCameraPos);
        
        // Apply smoothness (lerp between current and target rotation)
        // Note: This is simplified - in practice you'd need current rotation
        
        // Write aim rotation
        WriteZ(localPlayer + AimRotation, targetRotation);
    }
    
    // Helper functions
    static bool IsValidTarget(uint32_t entity) {
        // Check if entity is team member
        uint32_t avatarManager;
        if (!ReadZ(entity + AvatarManager, avatarManager)) return false;
        
        uint32_t avatar;
        if (!ReadZ(avatarManager + Avatar, avatar)) return false;
        
        uint32_t avatarData;
        if (!ReadZ(avatar + Avatar_Data, avatarData)) return false;
        
        bool isTeam;
        if (!ReadZ(avatarData + Avatar_Data_IsTeam, isTeam)) return false;
        
        return !isTeam; // Only target enemies
    }
    
    static Vector3 GetTargetBonePosition(uint32_t entity) {
        uint32_t boneOffset;
        switch (targetBone) {
            case 0: boneOffset = Head; break;
            case 1: boneOffset = Neck; break;
            case 2: boneOffset = Hip; break;
            default: boneOffset = Head; break;
        }
        
        uint32_t bone;
        if (!ReadZ(entity + boneOffset, bone)) return Vector3(0, 0, 0);
        
        Vector3 position;
        GetNodePosition(bone, position);
        return position;
    }
    
    static bool CheckVisibility(uint32_t entity) {
        if (!visibleOnly) return true;
        
        uint32_t avatarManager;
        if (!ReadZ(entity + AvatarManager, avatarManager)) return false;
        
        uint32_t avatar;
        if (!ReadZ(avatarManager + Avatar, avatar)) return false;
        
        bool isVisible;
        if (!ReadZ(avatar + Avatar_IsVisible, isVisible)) return false;
        
        return isVisible;
    }
    
    static bool CheckKnocked(uint32_t entity) {
        if (!ignoreKnocked) return false;
        
        uint32_t shadowBase;
        if (!ReadZ(entity + Player_ShadowBase, shadowBase)) return false;
        
        int xpose;
        if (!ReadZ(shadowBase + XPose, xpose)) return false;
        
        return xpose == 8;
    }
    
    static bool CheckDead(uint32_t entity) {
        bool isDead;
        if (!ReadZ(entity + Player_IsDead, isDead)) return false;
        return isDead;
    }
};
