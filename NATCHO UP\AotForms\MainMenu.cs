using AotForms;
using Client;
using Guna.UI2.WinForms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
//using Helper;
using System.Threading.Tasks;
using System.Windows.Forms;
using static AotForms.MouseHook;
using System.Runtime.InteropServices;
using System.Media;
using System.Reflection;
using Microsoft.VisualBasic.Logging;
using Reborn;
using System.Reflection.Emit;
using System.Net;
using HeXmem;
using System;
using System.Diagnostics;
using System.Windows.Forms;
using Microsoft.VisualBasic.Devices;
using ImGuiNET;
using System.Net.NetworkInformation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using System.Diagnostics;
using System.Threading.Tasks;
using System.IO;
using Memory;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Rebar;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
//using Helper;
using System.Threading.Tasks;
using System.Windows.Forms;
using static AotForms.MouseHook;
using System.Reflection.Metadata;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.TrayNotify;








namespace AotForms
{

    public partial class MainMenu : Form

    {
        public static api KeyAuthApp = new api(
    name: "Shubham",
    ownerid: "yPc1PAktFG",
    secret: "d88c26b4a1f62487fe5142415befb680a1b77e9de31b1b0e4680866b698e4a1c",
    version: "1.0"
);

        private List<Triangle> triangles; // List to store triangle particles
        private Random rand;
        private int targetWidth1, targetWidth2, targetWidth3;  // Target width for each slider
        private const int minWidth = 5;  // Minimum width for the slider panel
        private const int maxValueFov = 360; // Maximum value for the sliders
        private const int maxValueSmooth = 100; // Maximum value for the sliders
        private const int maxValueRange = 500; // Maximum value for the sliders
        private const int WM_HOTKEY = 0x0312;
        private const int MOD_NOREPEAT = 0x4000; // Prevents holding key from triggering multiple times
        private const int VK_F3 = (int)Keys.F3;
        private bool waitingForKeyPress = false;

        IntPtr mainHandle;
        //KeyHelper kh = new KeyHelper();
        bool keybind1 = false;
        bool shift;


        private void HideConsole()
        {
            IntPtr hWnd = GetConsoleWindow();
            if (hWnd != IntPtr.Zero)
            {
                ShowWindow(hWnd, SW_HIDE); // Hide the console window
            }
        }
        // Import necessary functions from kernel32.dll and user32.dll
        [DllImport("kernel32.dll")]
        private static extern bool AllocConsole();

        [DllImport("kernel32.dll")]
        private static extern IntPtr GetConsoleWindow();

        [DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        private const int SW_HIDE = 0;
        private const int SW_SHOW = 5;

        public MainMenu(IntPtr handle)
        {


            // Manually setting properties for the form
            this.Text = "Triangle Particles";
            this.Width = 600;
            this.Height = 500;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Normal;
            this.TopMost = true;  // Ensure form is always on top
            this.DoubleBuffered = true; // Helps to avoid flickering
            rand = new Random();
            triangles = new List<Triangle>();

            // Create initial particles randomly spawned from all edges
            for (int i = 0; i < 100; i++)
            {
                triangles.Add(new Triangle(rand, this.Width, this.Height)); // Random spawn on all edges
            }

            // Timer to update the animation every 16ms for 60 FPS
            System.Windows.Forms.Timer timer = new System.Windows.Forms.Timer();
            timer.Interval = 16;  // ~60 FPS
            timer.Tick += Timer_Tick;
            timer.Start();


            // ImGui setup
            ImGui.CreateContext();
            ImGuiIOPtr io = ImGui.GetIO();
            io.IniSavingRate = -1f; // Disable automatic saving
            mainHandle = handle;
            InitializeComponent();



            //kh.KeyDown += Kh_KeyDown;
            //kh.KeyUp += Kh_KeyUp;
            // Set the mouse hook
            MouseHook.SetHook();
            MouseHook.LeftButtonDown += MouseHook_LButtonDown;
            MouseHook.LeftButtonUp += MouseHook_LButtonUp;
            Application.ApplicationExit += (sender, e) => MouseHook.Unhook();
            // Initialize event handlers for Slider 1
            this.Slider1.MouseDown += new MouseEventHandler(this.SliderPanel1_MouseDown);
            this.Slider1.MouseMove += new MouseEventHandler(this.SliderPanel1_MouseMove);
            this.Slider1.MouseUp += new MouseEventHandler(this.SliderPanel1_MouseUp);

            // Initialize event handlers for Slider 2
            this.Slider2.MouseDown += new MouseEventHandler(this.SliderPanel2_MouseDown);
            this.Slider2.MouseMove += new MouseEventHandler(this.SliderPanel2_MouseMove);
            this.Slider2.MouseUp += new MouseEventHandler(this.SliderPanel2_MouseUp);
            // Initialize event handlers for Slider 3
            this.Slider3.MouseDown += new MouseEventHandler(this.SliderPanel3_MouseDown);
            this.Slider3.MouseMove += new MouseEventHandler(this.SliderPanel3_MouseMove);
            this.Slider3.MouseUp += new MouseEventHandler(this.SliderPanel3_MouseUp);

            // Initialize the timer for animation for Slider 1

            animationTimer1.Interval = 10;  // Set a small interval for smooth animation
            animationTimer1.Tick += AnimationTimer1_Tick;

            // Initialize the timer for animation for Slider 2

            animationTimer2.Interval = 10;  // Set a small interval for smooth animation
            animationTimer2.Tick += AnimationTimer2_Tick;
            animationTimer3.Interval = 10;  // Set a small interval for smooth animation
            animationTimer3.Tick += AnimationTimer3_Tick;

            AllocConsole();
            HideConsole();
            this.KeyPreview = true;  // Ensure the form captures key events
            this.KeyDown += new KeyEventHandler(Form1_KeyDown);  // Assign the KeyDown event to your handler

        }





        private void SliderPanel1_MouseDown(object sender, MouseEventArgs e)
        {
            Slider1.Capture = true;
            SetTargetWidth1(e.X);
        }

        private void SliderPanel1_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                SetTargetWidth1(e.X);
            }
        }

        private void SliderPanel1_MouseUp(object sender, MouseEventArgs e)
        {
            Slider1.Capture = false;
        }

        // Mouse events for Slider 2
        private void SliderPanel2_MouseDown(object sender, MouseEventArgs e)
        {
            Slider2.Capture = true;
            SetTargetWidth2(e.X);
        }

        private void SliderPanel2_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                SetTargetWidth2(e.X);
            }
        }

        private void SliderPanel2_MouseUp(object sender, MouseEventArgs e)
        {
            Slider2.Capture = false;
        }
        // Mouse events for Slider 3
        private void SliderPanel3_MouseDown(object sender, MouseEventArgs e)
        {
            Slider3.Capture = true;
            SetTargetWidth3(e.X);
        }
        private void SliderPanel3_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                SetTargetWidth3(e.X);
            }
        }

        private void SliderPanel3_MouseUp(object sender, MouseEventArgs e)
        {
            Slider3.Capture = false;
        }
        // Sets the target width for Slider 1 and starts the animation
        private void SetTargetWidth1(int mouseX)
        {
            targetWidth1 = mouseX;

            if (targetWidth1 < minWidth)
                targetWidth1 = minWidth;
            else if (targetWidth1 > panel1.Width)
                targetWidth1 = panel1.Width;

            animationTimer1.Start();
        }

        // Sets the target width for Slider 2 and starts the animation
        private void SetTargetWidth2(int mouseX)
        {
            targetWidth2 = mouseX;

            if (targetWidth2 < minWidth)
                targetWidth2 = minWidth;
            else if (targetWidth2 > panel2.Width)
                targetWidth2 = panel2.Width;

            animationTimer2.Start();
        }
        // Set target width and animate for Slider 3
        private void SetTargetWidth3(int mouseX)
        {
            targetWidth3 = mouseX;
            if (targetWidth3 < minWidth)
                targetWidth3 = minWidth;
            else if (targetWidth3 > panel3.Width)
                targetWidth3 = panel3.Width;

            animationTimer3.Start();
        }
        // Animation for Slider 1
        private void AnimationTimer1_Tick(object sender, EventArgs e)
        {
            int currentWidth = Slider1.Width;
            int step = 5;

            if (currentWidth < targetWidth1)
            {
                currentWidth = Math.Min(currentWidth + step, targetWidth1);
            }
            else if (currentWidth > targetWidth1)
            {
                currentWidth = Math.Max(currentWidth - step, targetWidth1);
            }

            if (currentWidth < minWidth)
            {
                currentWidth = minWidth;
            }

            Slider1.Width = currentWidth;

            int value1 = (int)(((float)(currentWidth - minWidth) / (panel1.Width - minWidth)) * maxValueFov);
            Config.Aimfov = value1;
            valuelbl1.Text = $"{value1}";


            if (currentWidth == targetWidth1)
            {
                animationTimer1.Stop();
            }
        }

        // Animation for Slider 2
        // Animation for Slider 2 
        // Animation for Slider 2 
        private void AnimationTimer2_Tick(object sender, EventArgs e)
        {
            int currentWidth = Slider2.Width;
            int step = 5;

            if (currentWidth < targetWidth2)
            {
                currentWidth = Math.Min(currentWidth + step, targetWidth2);
            }
            else if (currentWidth > targetWidth2)
            {
                currentWidth = Math.Max(currentWidth - step, targetWidth2);
            }

            if (currentWidth < minWidth)
            {
                currentWidth = minWidth;
            }

            Slider2.Width = currentWidth;

            // Calculate the normal value
            int value2 = (int)(((float)(currentWidth - minWidth) / (panel2.Width - minWidth)) * maxValueSmooth);

            // Invert the value only for Config.Aimbotype
            Config.Aimbotype = maxValueSmooth - value2;

            // Keep the text update unchanged

            smthlvl2.Text = $"{value2}";

            if (currentWidth == targetWidth2)
            {
                animationTimer2.Stop();
            }
        }


        private void AnimationTimer3_Tick(object sender, EventArgs e)
        {
            int currentWidth = Slider3.Width;
            int step = 5;

            if (currentWidth < targetWidth3)
            {
                currentWidth = Math.Min(currentWidth + step, targetWidth3);
            }
            else if (currentWidth > targetWidth3)
            {
                currentWidth = Math.Max(currentWidth - step, targetWidth3);
            }

            Slider3.Width = currentWidth;
            int value3 = (int)(((float)(currentWidth - minWidth) / (panel3.Width - minWidth)) * maxValueRange);
            rangelvl1.Text = $"{value3}";


            Config.AimBotMaxDistance = value3;
            if (currentWidth == targetWidth3)
            {
                animationTimer3.Stop();
            }
        }
        bool aimbotOnOff = false;

        private void Kh_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.LShiftKey || e.KeyCode == Keys.RShiftKey) shift = false;
        }
        private async void Kh_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.LShiftKey || e.KeyCode == Keys.RShiftKey) shift = true;
            if (e.KeyCode == Config.AimbotKey2)
            {
                if (!aimbotOnOff)
                {
                    Config.Notif();
                    Config.AimBot = false;
                    aimbotOnOff = true;
                }
                else
                {
                    Config.Notif();
                    Config.AimBot = true;
                    aimbotOnOff = false;
                }
            }
            if (e.KeyCode == Keys.F1)
            {
                if (Config.FixEsp)
                {
                    Core.Entities = new();
                    InternalMemory.Cache = new();
                }
                else { }
            }



            if (e.KeyCode == Keys.Insert)
            {
                Config.Notif();
                if (keybind1 == false)
                {
                    this.Hide();
                    keybind1 = true;
                }
                else
                {
                    this.Show();

                    SetStreamMode();

                    void SetStreamMode()
                    {
                        foreach (var obj in Application.OpenForms)
                        {
                            var form = obj as Form;

                            if (Config.StreamMode)
                            {
                                SetWindowDisplayAffinity(form.Handle, WDA_NONE);
                                SetWindowDisplayAffinity(form.Handle, WDA_EXCLUDEFROMCAPTURE);

                            }
                            else
                            {



                            }
                        }
                    }

                    keybind1 = false;
                }
            }
        }

        private async void Form2_Load(object sender, EventArgs e)
        {
            // Initialize KeyAuthApp (assuming it's for some authentication)
            var KeyauthInt = Task.Run(() => KeyAuthApp.init());
            await KeyauthInt;
            var KeyauthCheck = Task.Run(() => KeyAuthApp.check());
            await KeyauthCheck;

            // Make the form always on top
            this.TopMost = true;

            // Populate the ComboBox with the 4 options
            guna2ComboBox2.Items.Add("ESP UP");
            guna2ComboBox2.Items.Add("ESP BOTTOM");
            guna2ComboBox2.Items.Add("ESP LEFT");
            guna2ComboBox2.Items.Add("ESP RIGHT");

            // Set the default ComboBox selection (could be "ESP UP")
            guna2ComboBox2.Text = "ESP UP";

            // Update Config.linePosition to reflect the default selection
            Config.linePosition = "Up"; // Set the default value to "Up"
        }
        bool startk = false;

        private CancellationTokenSource _cts;
        private async void MouseHook_LButtonDown()
        {
            if (startk)
            {
                _cts = new CancellationTokenSource();
                try
                {
                    await Task.Delay(Config.Aimbotype, _cts.Token); // Wait for 5 seconds or until canceled
                    Config.AimBotLeft = true;
                }
                catch (TaskCanceledException)
                {
                    // Task was canceled, do nothing
                }
            }

        }

        private void MouseHook_LButtonUp()
        {
            if (startk)
            {
                if (_cts != null)
                {
                    _cts.Cancel(); // Cancel the delay when the button is released
                    Config.AimBotLeft = false;

                }
            }
        }



        bool connected = false;


        private async void guna2Button1_Click(object sender, EventArgs e)
        {
        }



        private void guna2Button5_Click(object sender, EventArgs e)
        {
            Config.Notif();
            Core.Entities = new();
            InternalMemory.Cache = new();
        }

        private void KillHDPlayerProcess()
        {
            try
            {
                // Get all processes with the name "HD-Player"
                var processes = Process.GetProcessesByName("HD-Player");

                foreach (var process in processes)
                {
                    // Kill the process
                    process.Kill();
                    process.WaitForExit(); // Optionally wait for the process to exit
                }


            }
            catch (Exception ex)
            {

            }
        }


        [DllImport("user32.dll")]
        static extern uint SetWindowDisplayAffinity(IntPtr hWnd, uint dwAffinity);

        const uint WDA_NONE = 0x00000000;
        const uint WDA_MONITOR = 0x00000001;
        const uint WDA_EXCLUDEFROMCAPTURE = 0x00000011;





        private void label7_Click(object sender, EventArgs e)
        {
            try
            {
                Config.Notif(); // Assuming this is a valid method
                ProcessStartInfo psi = new ProcessStartInfo
                {
                    FileName = "https://discord.gg/BuriedMods",
                    UseShellExecute = true // Ensures that it opens the URL in the default browser
                };
                Process.Start(psi);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"AN ERROR OCCURED: {ex.Message}");
            }
        }


        public static void DoneSound()
        {

        }


        private void guna2Button10_Click(object sender, EventArgs e)
        {
            Config.Notif();
            var picker = new ColorDialog();
            var result = picker.ShowDialog();

            if (result == DialogResult.OK)
            {
                guna2Button10.FillColor = Color.White;
                Config.ESPLineColor = picker.Color;
            }
        }

        private void guna2Button9_Click(object sender, EventArgs e)
        {
            Config.Notif();
            var picker = new ColorDialog();
            var result = picker.ShowDialog();

            if (result == DialogResult.OK)
            {
                guna2Button9.FillColor = Color.White;
                Config.ESPBoxColor = picker.Color;
            }
        }

        private void guna2Button8_Click(object sender, EventArgs e)
        {
            Config.Notif();
            var picker = new ColorDialog();
            var result = picker.ShowDialog();

            if (result == DialogResult.OK)
            {
                guna2Button8.FillColor = Color.White;
                Config.ESPFillBoxColor = picker.Color;
            }
        }

        private void guna2Button7_Click(object sender, EventArgs e)
        {
            Config.Notif();
            var picker = new ColorDialog();
            var result = picker.ShowDialog();

            if (result == DialogResult.OK)
            {
                guna2Button7.FillColor = Color.White;
                Config.ESPSkeletonColor = picker.Color;
            }
        }



        private void guna2ComboBox1_SelectedIndexChanged_1(object sender, EventArgs e)
        {

        }
        private void Form1_KeyDown(object sender, KeyEventArgs e)
        {
            // Check if we are waiting for a key press
            if (waitingForKeyPress)
            {
                // Update the label with the selected key
                guna2Button13.Text = e.KeyCode.ToString();
                Config.AimbotKey2 = e.KeyCode;
                // Reset the flag
                waitingForKeyPress = false;
            }
        }




        public void ExecuteCommand(string command)
        {
            ProcessStartInfo psi = new ProcessStartInfo("cmd.exe")
            {
                RedirectStandardInput = true,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using (Process process = new Process { StartInfo = psi })
            {
                process.Start();
                process.StandardInput.WriteLine(command);
                process.StandardInput.Flush();
                process.StandardInput.Close();
                process.WaitForExit();
            }
        }


        private void guna2Panel3_Paint(object sender, PaintEventArgs e)
        {

        }

        private void guna2CustomCheckBox5_Click(object sender, EventArgs e)
        {

        }





        private void guna2CustomCheckBox9_Click(object sender, EventArgs e)
        {
            Config.Notif();
            Config.IgnoreKnocked = guna2CustomCheckBox9.Checked;
        }

        private void guna2CustomCheckBox14_Click(object sender, EventArgs e)
        {
            Config.Notif();
            Config.Aimfovc = guna2CustomCheckBox14.Checked;
        }

        private void guna2CustomCheckBox16_Click(object sender, EventArgs e)
        {
            Config.Notif();
            Config.ESPLine = guna2CustomCheckBox16.Checked;
        }

        private void guna2CustomCheckBox15_Click(object sender, EventArgs e)
        {
            Config.Notif();
            Config.ESPBox2 = guna2CustomCheckBox15.Checked;
        }

        private void guna2CustomCheckBox13_Click(object sender, EventArgs e)
        {
            Config.Notif();
            Config.ESPFillBox = guna2CustomCheckBox13.Checked;
        }

        private void guna2CustomCheckBox12_Click(object sender, EventArgs e)
        {
            Config.Notif();
            Config.ESPBox = guna2CustomCheckBox12.Checked;
        }

        private void guna2CustomCheckBox11_Click(object sender, EventArgs e)
        {
            Config.Notif();
            Config.ESPSkeleton = guna2CustomCheckBox11.Checked;
        }

        private void guna2CustomCheckBox10_Click(object sender, EventArgs e)
        {
            Config.Notif();
            Config.ESPName = guna2CustomCheckBox10.Checked;
            Config.espbg = guna2CustomCheckBox10.Checked;
        }
        private void guna2Button13_Click(object sender, EventArgs e)
        {
            guna2Button13.Text = "SET KEYBIND";  // Indicate waiting for key press
            waitingForKeyPress = true;
        }

        private async void guna2CustomCheckBox22_Click(object sender, EventArgs e)
        {
            //THESE ARE ON LIBIL2CPP.SO
            string search1 = "1c 00 85 e5 00 70 94 e5 c0 50 96 e5 00 00 57 e3 01 00 00 1a 00 00 a0 e3 64 7f d7 eb fc 50 87 e5 00 70 94 e5 c4 50 96 e5";
            string replace1 = "1d";
            string search2 = "28 01 87 e5 00 70 94 e5 01 10 9f e7 00 00 91 e5 bf 10 d0 e5 02 00 11 e3 03 00 00 0a 70 10 90 e5 00 00 51 e3 00 00 00 1a";
            string replace2 = "10";
            string search3 = "3c 51 87 e5 00 70 94 e5 cc 50 96 e5 00 00 57 e3 01 00 00 1a 00 00 a0 e3 98 7e d7 eb 44 51 87 e5 00 70 94 e5 98 50 d6 e5";
            string replace3 = "10";
            //THESE ARE ON LIBIL2CPP.SO
            string search4 = "10 4c 2d e9 08 b0 8d e2 18 d0 4d e2 00 40 a0 e1 80 00 9f e5 00 00 8f e0 00 00 d0 e5 00 00 50 e3 06 00 00 1a 70 00 9f e5";
            string replace4 = "00 00 a0 e3 1e ff 2f e1";
            // //THESE ARE ON LIBANOGS.SO
            string search5 = "30 48 2d e9 08 b0 8d e2 ?? ?? 4d e2 ?? ?? 9f e5 ?? ?? 9f e7 0";
            string replace5 = "00 00 a0 e3 1e ff 2f e1";
            string search6 = "10 4c 2d e9 08 b0 8d e2 40 d0 4d e2 0c c0 9b e5 08 e0 9b e5 ?? ?? 9f e5 04 40 9f e7 00 40 94 e5 0c 40 0b e5 ?? 00 0b e5";
            string replace6 = "00 00 a0 e3 1e ff 2f e1";
            string processName = "HD-Player";
            string injectingMessage = "IMPLEMENTING BYPASS EMU";
            string successMessage = "BYPASS EMU IMPLEMENTED";
            string failureMessage = "BYPASS EMU VANDALIZED";

            await ApplyHack(processName, search1, replace1, successMessage, failureMessage, injectingMessage);
            await ApplyHack(processName, search2, replace2, successMessage, failureMessage, injectingMessage);
            await ApplyHack(processName, search3, replace3, successMessage, failureMessage, injectingMessage);
            await ApplyHack(processName, search4, replace4, successMessage, failureMessage, injectingMessage);
            await ApplyHack(processName, search5, replace5, successMessage, failureMessage, injectingMessage);
            await ApplyHack(processName, search6, replace6, successMessage, failureMessage, injectingMessage);
        }


        private void guna2CustomCheckBox21_Click(object sender, EventArgs e)
        {
            Config.FixEsp = guna2CustomCheckBox21.Checked;
        }

        private void guna2CustomCheckBox23_Click(object sender, EventArgs e)
        {

        }

        private void guna2CustomCheckBox20_Click(object sender, EventArgs e)
        {
            Config.Notif();
            KillHDPlayerProcess();
            Environment.Exit(0);
        }

        private void guna2CustomCheckBox2_Click_1(object sender, EventArgs e)
        {



        }

        private void guna2CustomCheckBox1_Click(object sender, EventArgs e)
        {
            if (guna2CustomCheckBox1.Checked)
            {
                Config.LagFix = 1;
            }
            else
            { Config.LagFix = 0; }

        }

        private void panel1_Paint(object sender, PaintEventArgs e)
        {

        }

        private void guna2CustomCheckBox2_Click(object sender, EventArgs e)
        {
            if (guna2CustomCheckBox2.Checked) ;
            else { guna2CustomCheckBox2.Checked = false; }

            SetStreamMode(guna2CustomCheckBox2.Checked);
            Config.StreamMode = guna2CustomCheckBox2.Checked;
            Config.sound = guna2CustomCheckBox2.Checked;
            void SetStreamMode(bool state)
            {
                foreach (var obj in Application.OpenForms)
                {
                    var form = obj as Form;

                    if (state)
                    {
                        SetWindowDisplayAffinity(form.Handle, WDA_EXCLUDEFROMCAPTURE);

                    }
                    else
                    {

                        SetWindowDisplayAffinity(form.Handle, WDA_NONE);

                    }
                }
            }
        }

        private void guna2CustomCheckBox23_Click_1(object sender, EventArgs e)
        {

        }

        private void guna2Button11_Click(object sender, EventArgs e)
        {
            Config.Notif();
            Core.Entities = new();
            InternalMemory.Cache = new();
        }

        private void label5_Click(object sender, EventArgs e)
        {

        }



        private void configpanel_Paint(object sender, PaintEventArgs e)
        {

        }

        private void guna2PictureBox3_Click(object sender, EventArgs e)
        {

        }

        private void guna2Panel3_Paint_1(object sender, PaintEventArgs e)
        {

        }






        private void label22_Click(object sender, EventArgs e)
        {

        }

        private void label24_Click(object sender, EventArgs e)
        {

        }

        private async void guna2CustomCheckBox4_Click(object sender, EventArgs e)
        {

        }



        public class MainForm : Form
        {
            private Guna2CustomCheckBox guna2CustomCheckBox4;

            public MainForm()
            {
                InitializeComponent();
            }

            private void InitializeComponent()
            {
                this.guna2CustomCheckBox4 = new Guna2CustomCheckBox();
                this.SuspendLayout();

                // 
                // guna2CustomCheckBox4
                // 
                this.guna2CustomCheckBox4.AutoSize = true;
                this.guna2CustomCheckBox4.Location = new System.Drawing.Point(30, 30);
                this.guna2CustomCheckBox4.Name = "guna2CustomCheckBox4";
                this.guna2CustomCheckBox4.Size = new System.Drawing.Size(120, 20);
                this.guna2CustomCheckBox4.TabIndex = 0;
                this.guna2CustomCheckBox4.Text = "Block Internet";
                this.guna2CustomCheckBox4.CheckedChanged += new System.EventHandler(this.guna2CustomCheckBox4_CheckedChanged);


                // 
                // MainForm
                // 
                this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
                this.ClientSize = new System.Drawing.Size(300, 100);
                this.Controls.Add(this.guna2CustomCheckBox4);
                this.Name = "MainForm";
                this.ResumeLayout(false);
                this.PerformLayout();
            }

            private void guna2CustomCheckBox4_CheckedChanged(object sender, EventArgs e)
            {

            }

            private void ExecuteCommand(string command)
            {
                try
                {
                    System.Diagnostics.Process.Start("cmd.exe", "/C " + command);
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Error executing command: " + ex.Message);
                }
            }
        }

        private void guna2Panel6_Paint(object sender, PaintEventArgs e)
        {

        }


        private void guna2Panel7_Paint(object sender, PaintEventArgs e)
        {

        }

        private void label21_Click(object sender, EventArgs e)
        {

        }

        private void label16_Click(object sender, EventArgs e)
        {

        }


        private void guna2Panel2_Paint(object sender, PaintEventArgs e)
        {

        }

        private void guna2Panel4_Paint(object sender, PaintEventArgs e)
        {

        }

        private void statuslable2_Click(object sender, EventArgs e)
        {

        }

        private void label35_Click(object sender, EventArgs e)
        {

        }

        private void label13_Click(object sender, EventArgs e)
        {

        }

        private void guna2Separator3_Click(object sender, EventArgs e)
        {

        }

        private void rangelvl1_Click(object sender, EventArgs e)
        {

        }

        private void Slider2_Paint(object sender, PaintEventArgs e)
        {

        }

        private void guna2Separator4_Click(object sender, EventArgs e)
        {

        }

        private void guna2ComboBox2_SelectedIndexChanged(object sender, EventArgs e)
        {
            ComboBox comboBox = (ComboBox)sender;
            string selectedItem = (string)comboBox.SelectedItem;

            // Update Config.linePosition based on ComboBox selection
            if (selectedItem == "ESP UP")
            {
                Config.linePosition = "Up";
            }
            else if (selectedItem == "ESP BOTTOM")
            {
                Config.linePosition = "Bottom";
            }
            else if (selectedItem == "ESP LEFT")
            {
                Config.linePosition = "Left";
            }
            else if (selectedItem == "ESP RIGHT")
            {
                Config.linePosition = "Right";
            }
        }

        // Method to update the position of triangles each frame
        private void Timer_Tick(object sender, EventArgs e)
        {
            foreach (var triangle in triangles)
            {
                triangle.UpdatePosition();
            }
            // Redraw the screen
            this.Invalidate();  // Triggers the Paint event
        }

        // Override Paint to draw the triangles
        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            Graphics g = e.Graphics;
            foreach (var triangle in triangles)
            {
                triangle.Draw(g);
            }
        }

        // Triangle class to define each particle's behavior and appearance
        public class Triangle
        {
            public float X { get; set; }
            public float Y { get; set; }
            public float Size { get; set; }
            public float SpeedX { get; set; }
            public float SpeedY { get; set; }
            public Color Color { get; set; }

            private Random rand;
            private float maxX, maxY;

            // Constructor to initialize the triangle
            public Triangle(Random rand, float maxX, float maxY)
            {
                this.rand = rand;
                this.maxX = maxX;
                this.maxY = maxY;

                // Randomly spawn on any edge
                int edge = rand.Next(4); // Randomly choose one of the four edges
                switch (edge)
                {
                    case 0: // Top edge
                        X = rand.Next(0, (int)maxX);
                        Y = 0;
                        break;
                    case 1: // Bottom edge
                        X = rand.Next(0, (int)maxX);
                        Y = maxY;
                        break;
                    case 2: // Left edge
                        X = 0;
                        Y = rand.Next(0, (int)maxY);
                        break;
                    case 3: // Right edge
                        X = maxX;
                        Y = rand.Next(0, (int)maxY);
                        break;
                }

                // Set medium size for the triangles (between 3 and 5)
                Size = 3 + (float)rand.NextDouble() * 2;  // Size between 3 and 5

                // Set fast speeds for the triangles
                SpeedX = (float)(rand.NextDouble() * 10 - 5); // Horizontal speed between -5 and 5 (faster speeds)
                SpeedY = (float)(rand.NextDouble() * 10 - 5); // Vertical speed between -5 and 5 (faster speeds)

                // Color Pale Violet Red
                Color = Color.SlateBlue;
            }

            // Update the position of the triangle
            public void UpdatePosition()
            {
                // Update the position based on current speed
                X += SpeedX;
                Y += SpeedY;

                // Bounce off edges (simple border collision)
                if (X < 0 || X > maxX) SpeedX = -SpeedX;  // Bounce off left/right edges
                if (Y < 0 || Y > maxY) SpeedY = -SpeedY;  // Bounce off top/bottom edges
            }

            // Draw the triangle on the screen
            public void Draw(Graphics g)
            {
                // Create a triangle shape (equilateral triangle for simplicity)
                PointF[] points = new PointF[3];
                points[0] = new PointF(X, Y);  // Tip of the triangle
                points[1] = new PointF(X + Size, Y + Size);  // Bottom-right corner
                points[2] = new PointF(X - Size, Y + Size);  // Bottom-left corner

                // Draw the triangle with Pale Violet Red color
                using (Brush brush = new SolidBrush(Color))
                {
                    g.FillPolygon(brush, points);
                }
            }
        }

        // Main entry point for the application
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new MainForm());
        }



        private void guna2Button3_Click(object sender, EventArgs e)
        {
            Config.Notif();
            var picker = new ColorDialog();
            var result = picker.ShowDialog();

            if (result == DialogResult.OK)
            {
                guna2Button9.FillColor = Color.White;
                Config.ESPBoxColor = picker.Color;
            }

        }

        Mem J = new Mem();  // Initialize memory manipulation class
        string adbPath = @"C:\Program Files\BlueStacks_nxt\HD-Adb.exe";



        // ADB command execution
        private void ExecuteAdbCommand(string command)
        {
            var psi = new ProcessStartInfo(adbPath)
            {
                Arguments = command,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using (var process = new Process { StartInfo = psi })
            {
                process.Start();
                process.WaitForExit();
                string output = process.StandardOutput.ReadToEnd();
                string error = process.StandardError.ReadToEnd();

                if (!string.IsNullOrEmpty(error))
                {

                }
            }
        }

        // ADB Logic to Kill Existing Processes and Start ADB Server
        private async Task StartAdb()
        {
            await Task.Run(() =>
            {
                ExecuteAdbCommand("kill-server"); // Kill existing ADB server
                ExecuteAdbCommand("start-server"); // Start ADB server
            });
        }

        // Restart Free Fire application
        private async Task RestartFreeFire()
        {
            await Task.Run(() =>
            {
                // Kill the Free Fire process
                ExecuteAdbCommand("shell am force-stop com.dts.freefireth"); // Adjust package name if necessary
                                                                             // Wait for a second to ensure the app is fully closed
                Task.Delay(100).Wait();
                // Start the Free Fire application
                ExecuteAdbCommand("shell monkey -p com.dts.freefireth -c android.intent.category.LAUNCHER 1"); // Adjust package name if necessary
            });
        }

        // Bypass Logic for Memory Patching
        public async Task bypass()
        {
            statuslable2.Text = "IMPLEMENTING BYPASS";

            // Define all search patterns and their corresponding replacements
            List<string> SE = new List<string>
        {
            "1c 00 85 e5 00 70 94 e5 c0 50 96 e5 00 00 57 e3 01 00 00 1a 00 00 a0 e3 64 7f d7 eb fc 50 87 e5 00 70 94 e5 c4 50 96 e5",
            "28 01 87 e5 00 70 94 e5 01 10 9f e7 00 00 91 e5 bf 10 d0 e5 02 00 11 e3 03 00 00 0a 70 10 90 e5 00 00 51 e3 00 00 00 1a",
            "3c 51 87 e5 00 70 94 e5 cc 50 96 e5 00 00 57 e3 01 00 00 1a 00 00 a0 e3 98 7e d7 eb 44 51 87 e5 00 70 94 e5 98 50 d6 e5",
            "10 4c 2d e9 08 b0 8d e2 18 d0 4d e2 00 40 a0 e1 80 00 9f e5 00 00 8f e0 00 00 d0 e5 00 00 50 e3 06 00 00 1a 70 00 9f e5",
            "30 48 2d e9 08 b0 8d e2 ?? ?? 4d e2 ?? ?? 9f e5 ?? ?? 9f e7 0",
            "10 4c 2d e9 08 b0 8d e2 40 d0 4d e2 0c c0 9b e5 08 e0 9b e5 ?? ?? 9f e5 04 40 9f e7 00 40 94 e5 0c 40 0b e5 ?? 00 0b e5",
        };

            List<string> RE = new List<string>
        {
            "1d",
            "10",
            "10",
            "00 00 a0 e3 1e ff 2f e1",
            "00 00 a0 e3 1e ff 2f e1",
            "00 00 a0 e3 1e ff 2f e1",

        };

            // Attach to BlueStacks Emulator process
            J.OpenProcess("HD-Player");

            // Start applying patches concurrently
            var patchTasks = new List<Task>();

            // Start applying patches immediately when Free Fire starts
            statuslable2.Text = "IMPLEMENTING BYPASS";

            // Add all patch tasks in parallel (as quickly as possible)
            for (int j = 0; j < SE.Count; j++)
            {
                int patchIndex = j;  // To use in the async task

                patchTasks.Add(Task.Run(async () =>
                {
                    // Perform scan and apply patches asynchronously
                    IEnumerable<long> cu = await Task.Run(() => J.AoBScan(SE[patchIndex], writable: true, true).Result);

                    if (cu.Any())
                    {
                        foreach (var addr in cu)
                        {
                            await Task.Run(() => J.WriteMemory(addr.ToString("X"), "bytes", RE[patchIndex]));
                        }
                    }
                }));
            }

            // Wait for all patching tasks to complete within 3 seconds
            var completedTask = Task.WhenAll(patchTasks);

            if (await Task.WhenAny(completedTask, Task.Delay(3000)) == completedTask)
            {
                // All patches applied within 3 seconds
                statuslable2.Text = "BYPASS IMPLEMENTED";
            }
            else
            {
                // Timeout reached before all patches were applied
                statuslable2.Text = "BYPASS VANDALIZED";
            }
        }

        private void guna2Panel1_Paint(object sender, PaintEventArgs e)
        {

        }

        private async void guna2Button6_Click(object sender, EventArgs e)
        {

        }


        private void UpdateStatus(string message)
        {
            statuslable2.Text = message;
            statuslable2.Text = message;
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            // Initialize KeyAuth
            KeyAuthApp.init();
        }

        private void passwordtxt_TextChanged(object sender, EventArgs e)
        {

        }

        private void usernametxt_TextChanged(object sender, EventArgs e)
        {

        }

        private void statuslbl_Click(object sender, EventArgs e)
        {

        }

        public Mem MemLib = new Mem();
        private string injectingMessage;
        private string successMessage;
        private string failureMessage;

        // Method to apply the hack
        private async Task ApplyHack(string processName, string searchPattern, string replacePattern, string successMessage, string failureMessage, string injectingMessage)
        {
            try
            {
                // Check if the process is running
                if (Process.GetProcessesByName(processName).Length == 0)
                {
                    statuslable2.Text = "EMULATOR NOT FOUND";
                    return;
                }

                // Open the process
                MemLib.OpenProcess(processName);
                statuslable2.Text = injectingMessage;

                // Perform AoBScan asynchronously
                IEnumerable<long> addresses = await MemLib.AoBScan(searchPattern, writable: true);

                // Check if any addresses were found
                if (addresses != null && addresses.Any())
                {
                    foreach (long address in addresses)
                    {
                        // Write the replacement pattern to each address found
                        MemLib.WriteMemory(address.ToString("X"), "bytes", replacePattern);
                    }

                    // If successful, update the status message
                    statuslable2.Text = successMessage;
                }
                else
                {
                    // If no addresses were found, display failure message
                    statuslable2.Text = failureMessage;
                }
            }
            catch (Exception ex)
            {
                // Catch any exceptions and display an error message
                statuslable2.Text = $"ERROR: {ex.Message}";
            }
        }

        private async void guna2CustomCheckBox3_Click(object sender, EventArgs e)
        {
            Config.AimbotVisible = guna2CustomCheckBox3.Checked;
        }

        private void guna2Panel5_Paint(object sender, PaintEventArgs e)
        {

        }

        private async void guna2CustomCheckBox7_Click(object sender, EventArgs e)
        {
            if (!connected)
            {
                statuslable2.Text = "ENABLING";
                Config.Notif();

                var processes = Process.GetProcessesByName("HD-Player");

                if (processes.Length != 1)
                {
                    statuslable2.ForeColor = Color.Red;

                    statuslable2.Text = "ERROR";
                    return;
                }

                var process = processes[0];
                var mainModulePath = Path.GetDirectoryName(process.MainModule.FileName);
                var adbPath = Path.Combine(mainModulePath, "HD-Adb.exe");

                if (!File.Exists(adbPath))
                {
                    statuslable2.ForeColor = Color.Red;

                    statuslable2.Text = "MODULE FOUND";
                    return;
                }


                var adb = new Adb(adbPath);
                await adb.Kill();

                var started = await adb.Start();
                if (!started)
                {

                    statuslable2.ForeColor = Color.Red;
                    statuslable2.Text = "ERROR";
                    return;
                }

                String pkg = "com.dts.freefireth";
                String lib = "libil2cpp.so";

                bool isFreeFireMax = false;
                if (isFreeFireMax)
                {
                    pkg = "com.dts.freefiremax";
                }

                var moduleAddr = await adb.FindModule(pkg, lib);
                if (moduleAddr == 0) // If the module address is not found
                {
                    statuslable2.ForeColor = Color.Red;

                    statuslable2.Text = "MODULE NOT FOUND";
                    return;
                }

                Offsets.Il2Cpp = moduleAddr;
                Core.Handle = FindRenderWindow(mainHandle);

                var esp = new ESP();
                await esp.Start();

                new Thread(Data.Work) { IsBackground = true }.Start();
                new Thread(AimbotVisible.Work) { IsBackground = true }.Start();
                new Thread(AimbotNova.Work) { IsBackground = true }.Start();
                new Thread(Silent.Work) { IsBackground = true }.Start();
                new Thread(UpPlayer.Work) { IsBackground = true }.Start();
                new Thread(ProxTelekill.Work) { IsBackground = true }.Start();
                new Thread(FlyMe.Work) { IsBackground = true }.Start();


                statuslable2.ForeColor = Color.LimeGreen;

                statuslable2.Text = "ENABLED SUCCESSFULLY";
                DoneSound();
                startk = true;
                connected = true;
            }
            else
            {

            }

        }
        static IntPtr FindRenderWindow(IntPtr parent)
        {
            IntPtr renderWindow = IntPtr.Zero;
            WinAPI.EnumChildWindows(parent, (hWnd, lParam) =>
            {
                StringBuilder sb = new StringBuilder(256);
                WinAPI.GetWindowText(hWnd, sb, sb.Capacity);
                string windowName = sb.ToString();
                if (!string.IsNullOrEmpty(windowName))
                {
                    if (windowName != "HD-Player")
                    {
                        renderWindow = hWnd;
                    }
                }
                return true;
            }, IntPtr.Zero);

            return renderWindow;
        }

        private void label8_Click(object sender, EventArgs e)
        {

        }

        private async void guna2CustomCheckBox4_Click_1(object sender, EventArgs e)
        {
            string search1 = "ff ff ff ff 08 00 00 00 00 00 60 40 cd cc 8c 3f 8f c2 f5 3c cd cc cc 3d 06 00 00 00 00 00 00 00 00 00 00 00 00 00 f0 41 00 00 48 42 00 00 00 3f 33 33 13 40 00 00 b0 3f 00 00 80 3f 01 00 00 00";
            string replace1 = "ff ff ff ff 08 00 00 00 00 00 60 40 e0 b1 ff ff e0 b1 ff ff e0 b1 ff ff e0 b1 ff ff e0 b1 ff ff 00 00 00 00 00 00 f0 41 00 00 48 42 00 00 00 3f 33 33 13 40 00 00 b0 3f 00 00 80 3f 01 00 00 00";
            string processName = "HD-Player";
            string injectingMessage = "IMPLEMENTING MIRA AWM";
            string successMessage = "MIRA AWM IMPLEMENTED";
            string failureMessage = "MIRA AWM VANDALIZED";

            await ApplyHack(processName, search1, replace1, successMessage, failureMessage, injectingMessage);

        }





        private void guna2CustomCheckBox6_Click(object sender, EventArgs e)
        {
            ToggleUndercam();
        }

        private void ToggleUndercam()
        {
            // Chama a função de notificação que você já tem.
            Config.Notif();

            // Lê e escreve na memória com base no estado do toggle
            var undercamacess = InternalMemory.Read<uint>(Offsets.Il2Cpp + Offsets.CNIKONPMDHF, out var undercamacess2);
            var undercamStaticClass = InternalMemory.Read<uint>(undercamacess2 + 0x5c, out var undercamStaticClass2);

            // Alterna o estado do checkbox
            guna2CustomCheckBox6.Checked = !guna2CustomCheckBox6.Checked;

            // Define o valor de 'undercam' dependendo se o toggle está ativado ou não
            InternalMemory.Write(undercamStaticClass2 + Offsets.undercam, guna2CustomCheckBox6.Checked ? 4.5f : 1.0f);
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            if (keyData == Keys.F2)
            {
                ToggleUndercam();
                return true; // Indica que a tecla foi processada
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }

        private async void guna2CustomCheckBox8_Click(object sender, EventArgs e)
        {
            Config.UpPlayer = guna2CustomCheckBox8.Checked;

        }


        private async void guna2CustomCheckBox17_Click(object sender, EventArgs e)
        {
            Config.AimbotSilent = guna2CustomCheckBox17.Checked;
            Config.NoRecoil = guna2CustomCheckBox17.Checked;
        }



        private void guna2CustomCheckBox18_Click(object sender, EventArgs e)
        {
            Config.AimbotNova = guna2CustomCheckBox18.Checked;
        }

        private void guna2CustomCheckBox4_Click_2(object sender, EventArgs e)
        {
            Config.ESPHealth = guna2CustomCheckBox4.Checked;
        }

        private void label11_Click(object sender, EventArgs e)
        {

        }

        private async void guna2CustomCheckBox19_Click(object sender, EventArgs e)
        {
            string search1 = "10 4C 2D E9 08 B0 8D E2 0C 01 9F E5 00 00 8F E0 00 00 D0 E5 00 00 50 E3 06 00 00 1A FC 00 9F E5 00 00 9F E7 00 00 90 E5 65 FE 06 EB F0 00 9F E5 01 10 A0 E3 00 10 CF E7 1D 09 01 E3 00 10 A0 E3 53 FE 06 EB 01 00 50";
            string replace1 = "01 00 A0 E3 1E FF 2F E1 0C 01 9F E5 00 00 8F E0 00 00 D0 E5 00 00 50 E3 06 00 00 1A FC 00 9F E5 00 00 9F E7 00 00 90 E5 65 FE 06 EB F0 00 9F E5 01 10 A0 E3 00 10 CF E7 1D 09 01 E3 00 10 A0 E3 53 FE 06 EB 01 00 50";
            string processName = "HD-Player";
            string injectingMessage = "IMPLEMENTING MIRA AWM";
            string successMessage = "MIRA AWM IMPLEMENTED";
            string failureMessage = "MIRA AWM VANDALIZED";

            await ApplyHack(processName, search1, replace1, successMessage, failureMessage, injectingMessage);
        }

        private void guna2CustomCheckBox5_Click_1(object sender, EventArgs e)
        {
            Config.proxtelekill = guna2CustomCheckBox5.Checked;
        }

        private void Slider3_Paint(object sender, PaintEventArgs e)
        {

        }

        private void guna2CustomCheckBox19_Click_1(object sender, EventArgs e)
        {
            Config.flyme = guna2CustomCheckBox19.Checked;
        }
    }
}


