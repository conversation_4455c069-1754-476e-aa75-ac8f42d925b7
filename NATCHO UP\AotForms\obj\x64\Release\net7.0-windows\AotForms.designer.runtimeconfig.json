{"runtimeOptions": {"tfm": "net7.0", "frameworks": [{"name": "Microsoft.NETCore.App", "version": "7.0.0"}, {"name": "Microsoft.WindowsDesktop.App", "version": "7.0.0"}], "additionalProbingPaths": ["C:\\Users\\<USER>\\.nuget\\packages"], "configProperties": {"Microsoft.Extensions.DependencyInjection.VerifyOpenGenericServiceTrimmability": true, "System.ComponentModel.TypeConverter.EnableUnsafeBinaryFormatterInDesigntimeLicenseContextSerialization": false, "System.Diagnostics.Tracing.EventSource.IsSupported": false, "System.Reflection.Metadata.MetadataUpdater.IsSupported": false, "System.Resources.ResourceManager.AllowCustomResourceTypes": true, "System.Runtime.InteropServices.BuiltInComInterop.IsSupported": true, "System.Runtime.InteropServices.EnableConsumingManagedCodeFromNativeHosting": false, "System.Runtime.InteropServices.EnableCppCLIHostActivation": false, "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": true, "System.StartupHookProvider.IsSupported": false, "System.Text.Encoding.EnableUnsafeUTF7Encoding": false, "System.Threading.Thread.EnableAutoreleasePool": false, "Microsoft.NETCore.DotNetHostPolicy.SetAppPaths": true}}}