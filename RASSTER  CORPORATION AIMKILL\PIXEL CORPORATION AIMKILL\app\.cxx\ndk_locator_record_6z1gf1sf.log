[{"level": "INFO", "message": "android.ndkVersion from module build.gradle is 26.1.10909125"}, {"level": "INFO", "message": "ndk.dir in local.properties is not set"}, {"level": "INFO", "message": "ANDROID_NDK_HOME environment variable is not set"}, {"level": "INFO", "message": "sdkFolder is C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk"}, {"level": "INFO", "message": "Considering C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk-bundle in SDK ndk-bundle folder"}, {"level": "INFO", "message": "Considering C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\21.0.6113669 in SDK ndk folder"}, {"level": "INFO", "message": "Considering C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\21.4.7075529 in SDK ndk folder"}, {"level": "INFO", "message": "Considering C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\22.0.7026061 in SDK ndk folder"}, {"level": "INFO", "message": "Considering C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\23.1.7779620 in SDK ndk folder"}, {"level": "INFO", "message": "Considering C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125 in SDK ndk folder"}, {"level": "INFO", "message": "Found requested NDK version 26.1.10909125 at C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125"}]