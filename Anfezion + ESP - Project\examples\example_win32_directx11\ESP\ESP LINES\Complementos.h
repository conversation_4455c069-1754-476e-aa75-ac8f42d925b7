ImVec4 colorTop(255, 0, 0, 255);
ImVec4 colorBottom(0, 0, 0, 255);
ImVec4 boxColor(25 / 255.f, 215 / 255.f, 130 / 255.f, 1.0f);
ImVec4 lineColorTeam(0.0f, 1.0f, 0.0f, 1.0f);
ImVec4 lineColorEnemy(25 / 255.f, 215 / 255.f, 130 / 255.f, 1.0f);
ImVec4 nameColor(1.f, 1.f, 1.f, 1.0f);
ImVec4 distanceColor(25 / 255.f, 215 / 255.f, 130 / 255.f, 1.0f);
ImVec4 bonesColor(25 / 255.f, 215 / 255.f, 130 / 255.f, 1.0f);
ImVec4 fovColor(1.0f, 1.0f, 1.0f, 1.0f);
ImVec4 fovColor2(255, 0, 0, 255);
ImVec4 boxColorFilled = ImVec4(25 / 255.f, 215 / 255.f, 130 / 255.f, 1.0f);
ImVec4 healthcolor = ImVec4(0.0f, 1.0f, 0.f, 1.0f); // Verde ne�n de la imagen
ImVec4 lowHealthColor = ImVec4(1.0f, 0.0f, 0.f, 1.0f); // Rojo ne�n de la imagen
static ImVec4 linecolor(1, 0.65f, 0, 1);
static ImVec4 glowcolorfirst(255, 0, 0, 255);
float rgbSpeed = 1.0f;        // Velocidad del Rainbow
float smoothFactor = 0.1f;
//float fov = 100.0f;
float fov = 50.0f;
int fov2 = 100;

static auto lastCleanTime = std::chrono::steady_clock::now();
std::mutex cacheMutex;  // Protecci�n para acceso concurrente al cach�
int entitiesCount = 0;
std::atomic<bool> runningxd2(true);
const size_t MAX_CACHE_SIZE = 11200;  // Tama�o m�ximo del cach� antes de limpieza
//int memoryespbyfenix = 6000;
const std::chrono::seconds CACHE_RESET_INTERVAL(1);

std::unordered_map<uintptr_t, uintptr_t> CacheZ;

enum class AimKey { RightMouseButton, LeftMouseButton, MiddleMouseButton, ShiftKey, ControlKey, AltKey };
AimKey selectedKey = AimKey::LeftMouseButton;
MatchStatusEnum currentMatchStatus = MATCH_NOT_STARTED;

void* vmPtr = nullptr;

typedef int(__cdecl* PGMPhysReadFunc)(void*, uintptr_t, void*, size_t);
typedef int(__cdecl* PGMPhysSimpleWriteGCPhysFunc)(void*, uintptr_t, void*, size_t);
typedef int(__cdecl* PGMPhysGCPtr2GCPhysFunc)(void*, uintptr_t, uintptr_t*);
typedef void* (__cdecl* VMMGetCpuByIdFunc)(void*, int);

PGMPhysReadFunc ogPhysRead = nullptr;
VMMGetCpuByIdFunc ogCPU = nullptr;
PGMPhysGCPtr2GCPhysFunc ogCast = nullptr;
PGMPhysSimpleWriteGCPhysFunc ogWrite = nullptr;

size_t WriteCallback(void* contents, size_t size, size_t nmemb, std::string* buffer) {
    buffer->append((char*)contents, size * nmemb);
    return size * nmemb;
}

int __cdecl HookedPGMPhysRead(void* pVM, uintptr_t GCPhys, void* pvBuf, size_t cbRead) {
    if (vmPtr == nullptr)
    {

        vmPtr = pVM;

    }
    return ogPhysRead(pVM, GCPhys, pvBuf, cbRead);
}

int zHookWrite(void* pVM, uintptr_t GCPhys, void* pvBuf, size_t cbRead) {
    return ogWrite(pVM, GCPhys, pvBuf, cbRead);
}

int zHookRead(void* pVM, uintptr_t GCPhys, void* pvBuf, size_t cbRead) {
    return ogPhysRead(pVM, GCPhys, pvBuf, cbRead);
}

void* CPU(void* pVM, int cpuId) {
    return ogCPU(pVM, cpuId);
}

int Cast(void* pVCpu, uintptr_t address, uintptr_t* physAddress) {
    return ogCast(pVCpu, address, physAddress);
}

void* pVMAddr = nullptr;
void* cpuAddr = nullptr;


std::atomic<bool> isRunning(false);
std::thread cacheThread;
std::mutex CacheLock;

std::unordered_map<uintptr_t, uintptr_t> Cache;
std::unordered_map<uintptr_t, uintptr_t> CacheOrder;

void* pVM = nullptr;

void InitializeZ(void* pVM)
{

    pVMAddr = pVM;
    cpuAddr = CPU(pVM, 0);
    CacheZ.clear();
}




bool ConvertZ(uintptr_t address, uintptr_t& phys) {
    auto it = CacheZ.find(address);
    if (it != CacheZ.end()) {
        phys = it->second;
        return true;
    }

    if (!cpuAddr) {
        cpuAddr = CPU(pVMAddr, 0);
        if (!cpuAddr) return false;
    }

    int status = Cast(cpuAddr, address, &phys);
    if (status != 0) return false;

    CacheZ[address] = phys;
    return true;
}

template<typename T>
bool ReadZ(uintptr_t address, T& data) {
    uintptr_t physAddress;
    if (!ConvertZ(address, physAddress)) return false;

    int status = zHookRead(pVMAddr, physAddress, &data, sizeof(T));
    return (status == 0);
}

template<typename T>
bool ReadArrayZ(uintptr_t address, std::vector<T>& array) {
    uintptr_t physAddress;
    if (!ConvertZ(address, physAddress)) return false;

    size_t size = sizeof(T) * array.size();
    int status = zHookRead(pVMAddr, physAddress, array.data(), size);
    return (status == 0);
}

std::string ReadStringZ(uintptr_t address, int size, bool unicode = false) {
    if (size <= 0) return "";

    std::vector<uint8_t> stringBytes(size);
    if (!ReadArrayZ(address, stringBytes)) return "";

    std::string readString;
    if (unicode) {
        readString.assign(stringBytes.begin(), stringBytes.end());
    }
    else {
        readString = std::string(stringBytes.begin(), stringBytes.end());
    }

    size_t nullPos = readString.find('\0');
    if (nullPos != std::string::npos) {
        readString = readString.substr(0, nullPos);
    }

    return readString;
}



bool GetBluestacksMiddlePos(POINT& middlePos) {
    const char* windowTitles[] = {
        "BlueStacks App Player", "MSI App Player", "BlueStacks",
        "App Player", "BlueStacks 5", "Google Play Beta Emulator", nullptr
    };

    HWND HwndEmul = nullptr;
    for (int i = 0; windowTitles[i]; i++) {
        HwndEmul = FindWindow(NULL, windowTitles[i]);
        if (HwndEmul) break;
    }

    if (!HwndEmul) return false;

    RECT clientRect;
    if (!GetClientRect(HwndEmul, &clientRect)) return false;

    middlePos.x = (clientRect.right - clientRect.left) / 2;
    middlePos.y = (clientRect.bottom - clientRect.top) / 2;
    ClientToScreen(HwndEmul, &middlePos);
    return true;
}
std::atomic<int> memoryespbyfenix = 6000;

static bool threadRunning = false;
void ProcessEntities()
{
    while (runningxd2) {
        CacheZ.clear();

        // ?????? ???? memoryespbyfenix ?????
        int currentCacheValue = memoryespbyfenix.load();
        // ????: ??????? currentCacheValue ???????? ??? ??????
        // ...

        std::this_thread::sleep_for(std::chrono::seconds(5));
    }
}

template<typename T>
bool ReadArrayZ2(uintptr_t address, std::vector<T>& array)
{
    uintptr_t convertedAddress;
    bool result = ConvertZ(address, convertedAddress);

    if (!result)
        return false;

    size_t size = sizeof(T) * array.size();
    DWORD status = zHookRead(pVMAddr, convertedAddress, array.data(), size);

    return status == 0;
}

std::string ReadStringZ2(uintptr_t address, int size, bool unicode = true)
{
    std::vector<uint8_t> stringBytes(size);

    bool read = ReadArrayZ2(address, stringBytes);

    if (!read) return "";

    std::string readString;
    if (unicode)
    {
        std::wstring wideString(reinterpret_cast<wchar_t*>(stringBytes.data()), size / 2);
        readString = std::string(wideString.begin(), wideString.end());
    }
    else
    {
        readString = std::string(stringBytes.begin(), stringBytes.end());
    }

    auto nullTerminator = readString.find('\0');
    if (nullTerminator != std::string::npos)
        readString = readString.substr(0, nullTerminator);

    return readString;
}

template<typename T>
void WriteZ(uintptr_t address, const T& value) {
    uintptr_t physAddress;
    if (!ConvertZ(address, physAddress)) {
        std::cerr << "" << std::endl;
        return;
    }

    int status = zHookWrite(pVMAddr, physAddress, (void*)&value, sizeof(T));
    if (status != 0) {
        std::cerr << "" << physAddress << std::endl;
    }
}
struct TMatrix
{
    Vector4 position;
    Quaternion rotation;
    Vector4 scale;
};

struct HealthData {
    float vidaPorcentaje;
    std::string vidaStr;
};

struct BoxData {
    ImVec2 topLeft;
    ImVec2 bottomRight;
};

bool GetPosition(uint32_t transform, Vector3& pos)
{
    pos = Vector3::Zero();

    uint32_t transformObjValue;
    if (!ReadZ(transform + 0x8, transformObjValue))
    {
        return false;
    }

    uint32_t indexValue;
    if (!ReadZ(transformObjValue + 0x24, indexValue))
    {
        return false;
    }

    uint32_t matrixValue;
    if (!ReadZ(transformObjValue + 0x20, matrixValue))
    {
        return false;
    }

    uint32_t matrixListValue;
    if (!ReadZ(matrixValue + 0x10, matrixListValue))
    {
        return false;
    }

    uint32_t matrixIndicesValue;
    if (!ReadZ(matrixValue + 0x14, matrixIndicesValue))
    {
        return false;
    }

    Vector3 resultValue;
    if (!ReadZ(indexValue * 0x30 + matrixListValue, resultValue))
    {
        return false;
    }

    int maxTries = 50;
    int tries = 0;

    int transformIndexValue;
    if (!ReadZ((uint32_t)((indexValue * 0x4) + matrixIndicesValue), transformIndexValue))
    {
        return false;
    }

    while (transformIndexValue >= 0)
    {
        tries++;
        if (tries == maxTries) break;

        TMatrix tMatrixValue;
        if (!ReadZ((uint32_t)(0x30 * transformIndexValue + matrixListValue), tMatrixValue))
        {
            return false;
        }

        float rotX = tMatrixValue.rotation.X;
        float rotY = tMatrixValue.rotation.Y;
        float rotZ = tMatrixValue.rotation.Z;
        float rotW = tMatrixValue.rotation.W;

        float scaleX = resultValue.X * tMatrixValue.scale.x;
        float scaleY = resultValue.Y * tMatrixValue.scale.x;
        float scaleZ = resultValue.Z * tMatrixValue.scale.z;

        resultValue.X = (float)(tMatrixValue.position.x + scaleX +
            (scaleX * ((rotY * rotY * -2.0) - (rotZ * rotZ * 2.0))) +
            (scaleY * ((rotW * rotZ * -2.0) - (rotY * rotX * -2.0))) +
            (scaleZ * ((rotZ * rotX * 2.0) - (rotW * rotY * -2.0))));
        resultValue.Y = (float)(tMatrixValue.position.y + scaleY +
            (scaleX * ((rotX * rotY * 2.0) - (rotW * rotZ * -2.0))) +
            (scaleY * ((rotZ * rotZ * -2.0) - (rotX * rotX * 2.0))) +
            (scaleZ * ((rotW * rotX * -2.0) - (rotZ * rotY * -2.0))));
        resultValue.Z = (float)(tMatrixValue.position.z + scaleZ +
            (scaleX * ((rotW * rotY * -2.0) - (rotX * rotZ * -2.0))) +
            (scaleY * ((rotY * rotZ * 2.0) - (rotW * rotX * -2.0))) +
            (scaleZ * ((rotX * rotX * -2.0) - (rotY * rotY * 2.0))));

        if (!ReadZ((uint32_t)(transformIndexValue * 0x4 + matrixIndicesValue), transformIndexValue))
        {
            return false;
        }
    }

    pos = resultValue;
    return tries != maxTries;
}

bool GetNodePosition(uint32_t nodeTransform, Vector3& result)
{
    uint32_t transformValue;
    if (!ReadZ(nodeTransform + 0x8, transformValue))
    {
        result = Vector3::Zero();
        return false;
    }

    return GetPosition(transformValue, result);
}




std::string uInt_StringsZ(uint32_t value)
{
    std::stringstream stream;
    stream << "0x"
        << std::setfill('0') << std::setw(8)
        << std::hex << value;
    return stream.str();
}

BOOL CALLBACK GetPosEmlBS(HWND hWnd, LPARAM lParam) {
    char title[256];
    GetWindowTextA(hWnd, title, sizeof(title));
    std::string windowName(title);

    if (windowName == "HD-Player") {
        HWND* pRenderWindow = reinterpret_cast<HWND*>(lParam);
        *pRenderWindow = hWnd;
        return FALSE;
    }
    else
    {
        if (windowName == "_ctl.Window") {
            HWND* pRenderWindow = reinterpret_cast<HWND*>(lParam);
            *pRenderWindow = hWnd;
            return FALSE;
        }
    }

    return TRUE;
}

HWND FindHDPlayerWindow(HWND parent) {
    HWND renderWindow = nullptr;
    EnumChildWindows(parent, GetPosEmlBS, reinterpret_cast<LPARAM>(&renderWindow));
    return renderWindow;
}

ID3D11Device* device{ nullptr };
ID3D11DeviceContext* device_constext{ nullptr };
IDXGISwapChain* swap_chain{ nullptr };
ID3D11RenderTargetView* render_target_view{ nullptr };
D3D_FEATURE_LEVEL level{};

int numLines = 150;
float amplitude = 56.0f;
float frequency9 = 0.031f;
float phaseOffset = 0.4f;
float lineSpacing = 20.0f;
float xOffset = 1000.0f;
const ImU32 lineColor = IM_COL32(255, 255, 0, 10);

std::string ErrorLoadAdb = "NUll";

