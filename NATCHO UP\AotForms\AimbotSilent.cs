﻿using AotForms;
using System;
using System.Numerics;
using System.Threading;
using System.Threading.Tasks;

namespace Client
{
    internal static class Silent
    {
        internal static int AimbotSpeed = 1;

        internal static void Work()
        {
            while (true)
            {
                if (!Config.AimbotSilent)
                {
                    Thread.Sleep(0);
                    continue;
                }


                Entity target = null;
                float distance = float.MaxValue;

                if (Core.Width == -1 || Core.Height == -1) continue;
                if (!Core.HaveMatrix) continue;

                var screenCenter = new Vector2(Core.Width / 2f, Core.Height / 2f);

                foreach (var entity in Core.Entities.Values)
                {
                    if (entity.IsDead) continue;

                    if (entity.IsKnocked) continue;

                    if (entity.Status) continue;

                    var head2D = W2S.WorldToScreen(Core.CameraMatrix, entity.Head, Core.Width, Core.Height);

                    if (head2D.X < 1 || head2D.Y < 1) continue;


                    Vector2 headScreenPos2 = new Vector2(head2D.X, head2D.Y);
                    var playerDistance = Vector2.Distance(screenCenter, headScreenPos2);


                    if (isInsideFOV((int)head2D.X, (int)head2D.Y))
                    {
                        if (playerDistance < distance)
                        {

                            distance = playerDistance;

                            target = entity;

                        }
                    }
                }

                if (target != null)
                {

                    var firecheck = InternalMemory.Read<bool>(Core.LocalPlayer + 0x454, out var firecheck2);

                    if (firecheck2)
                    {

                        var testeee = InternalMemory.Read<uint>(Core.LocalPlayer + 0x7DC, out var testeee22);
                        if (testeee22 != 0)
                        {



                            InternalMemory.Read<Vector3>(testeee22 + 0x38, out var StartPosition);


                            InternalMemory.Write<Vector3>(testeee22 + 0x2C, target.Head - StartPosition);

                        }
                    }

                }
                Thread.Sleep(0);
            }
        }



        private static bool isInsideFOV(int x, int y)
        {
            if (Config.Aimfov <= 0)
                return true;

            int circle_x = Core.Width / 2;
            int circle_y = Core.Height / 2;
            int rad = Config.Aimfov; // Use half the maximum angle as the radius
            return (x - circle_x) * (x - circle_x) + (y - circle_y) * (y - circle_y) <= rad * rad;
        }
    }
}