#include <list>
#include <vector>
#include <string.h>
#include <pthread.h>
#include <cstring>
#include <jni.h>
#include <unistd.h>
#include <fstream>
#include <iostream>// valo kore korbi kintu nahole sehs
#include <dlfcn.h>

#include "Tools/Includes/Logger.h"
#include "Tools/Includes/obfuscate.h"
#include "Tools/Includes/Utils.h"
#include "Tools/SOCKET/Server.h"
#include "Tools/KittyMemory/KittyInclude.hpp"
#include "Hack/Memory.h"
#include "Hack/Offsets.h"
#include "Hack/class.h"
#include "Hack/il2cpp.h"
//Target lib here
#define targetLibName OBFUSCATE("libil2cpp.so")
#include "Tools/Includes/Macros.h"


struct {
    bool enableAimbot = false;
    bool aimbotShoot = false;
    bool aimbotScope = false;

    float aimbotFOV = 0.0f;
    bool FOV = true;
    bool enableESP = false;
    bool speedHack = false;
    bool undergroundCatapult = false;
    bool catapultDistance = false;
    bool ghostHack = false;
    bool wallHack = false;
    float vehicle_y = 0.0f;
    float vehicle_unY = 0.0f;
} MasterBool;

#include "dobby.h"
int g_screenWidth, g_screenHeight;

ElfScanner g_il2cppELF;




enum Mode {
    InitMode = 1,
    HackMode = 2,
    StopMode = 98,
    EspMode = 99,
};

struct Request {
    int Mode;
    bool boolean;
    int value;
    int ScreenWidth;
    int ScreenHeight;
};

#define maxplayerCount 54

struct PlayerData {
    Vector3 headPosition;
    Vector3 bottomPlayerPosition;

    Vector2 health;
    char name[2000];
    bool isDieing;
    float distance;
};

struct Response {
    bool Success;
    int PlayerCount;
    PlayerData Players[maxplayerCount];
};

SocketServer server;

int InitServer() {
    if (!server.Create()) {
        return -1;
    }
    if (!server.Bind()) {
        return -1;
    }
    if (!server.Listen()) {
        return -1;
    }
    return 0;
}


void CreateExtraSensoryPerceptionhackFixed(Response &response)
{

    if (MasterBool.enableESP) {
        void *GameFacade = *(void **) getRealOffset(0x854F5E0);
        if (GameFacade != nullptr) {
            void *MatchGame = *(void **) ((uint64_t) GameFacade + 0x5C);
            if (MatchGame != nullptr) {
                void *ClassMatchGame = *(void **) ((uint64_t) MatchGame + 0x4);
                if (ClassMatchGame != nullptr) {
                    void *current_match = *(void **) ((uint64_t) ClassMatchGame + 0x50);
                    if (current_match != nullptr) {
                        auto matchState = *(uint32_t *) ((uint64_t) current_match + 0x8);
                        if (matchState == 1) {
                            void *CurrentLocalPlayer = Current_Local_Player();
                            if (CurrentLocalPlayer != nullptr) {

                                System_Collections_Generic_Dictionary_IHAAMHPPLMG__Player__o *MonoPlayer = *(System_Collections_Generic_Dictionary_IHAAMHPPLMG__Player__o **)((uint64_t) current_match + pAddress.ListadeJogadores);
                                if (MonoPlayer != nullptr) {
                                    COW_GamePlay_Player_array *players = MonoPlayer->valueSlots;
                                    if (players != nullptr) {
                                        int countagempartida = players->max_length;
                                        if (countagempartida != 1) {
                                            for (int i = 0; i < countagempartida; ++i) {
                                                if (players->m_Items[i] != nullptr &&
                                                    players->m_Items[i] != CurrentLocalPlayer) {
                                                    if (IsStreamerVisible(players->m_Items[i]) &&!IsLocalTeammate(players->m_Items[i]) &&GetHp(players->m_Items[i]) > 0) {

                                                        void *HeadTF = TransformNode(*(void **) ((uintptr_t) players->m_Items[i] +pAddress.HeadTF));
                                                        void *PesTF = TransformNode(*(void **) ((uintptr_t) players->m_Items[i] +pAddress.PesTF));
                                                        void *HeadTFLocal = TransformNode(*(void **) ((uintptr_t) CurrentLocalPlayer +pAddress.HeadTF));
                                                        Vector3 WorldToScreenHead = WorldToScreenPoint(Transform_INTERNAL_GetPosition(HeadTF));

                                                        Vector3 WorldToScreenPes = WorldToScreenPoint(Transform_INTERNAL_GetPosition(PesTF));

                                                        bool Caido2 = IsDieing(players->m_Items[i]);
                                                        float distance = Vector3::Distance(Transform_INTERNAL_GetPosition(HeadTFLocal),Transform_INTERNAL_GetPosition(HeadTF));

                                                        PlayerData *data = &response.Players[response.PlayerCount];
                                                        if (data != nullptr) {
                                                            data->headPosition = WorldToScreenHead;
                                                            data->bottomPlayerPosition = WorldToScreenPes;
                                                            data->distance = distance;
                                                            data->isDieing = Caido2;
                                                            data->health = GetHp(players->m_Items[i]);
                                                        }
                                                        ++response.PlayerCount;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        } else {

                            response.PlayerCount = 0;
                        }
                    }
                }
            }
        }

    }
}

void *CreateServer(void *) {
    if (InitServer() == 0) {
        if (server.Accept()) {
            Request request{};
            while (server.receive((void*)&request) > 0) {
                Response response{};
                if (request.Mode == Mode::InitMode) {
                    response.Success = true;
                } else if (request.Mode == Mode::EspMode) {
                    g_screenWidth = request.ScreenWidth;
                    g_screenHeight = request.ScreenHeight;
                    //LOGI("g_screenWidth :%d",g_screenWidth);
                    response.Success = true;
                    CreateExtraSensoryPerceptionhackFixed(response);
                } else if (request.Mode == 3) {
                    MasterBool.enableESP = request.boolean;
                    response.Success = true;
                } else if (request.Mode == 4) {
                    MasterBool.speedHack = request.boolean;
                    response.Success = true;
                } else if (request.Mode == 6) {
                    MasterBool.undergroundCatapult = request.boolean;
                    response.Success = true;
                } else if (request.Mode == 101) {
                    MasterBool.enableAimbot = request.boolean;
                    response.Success = true;
                } else if (request.Mode == 102) {
                    MasterBool.aimbotShoot = request.boolean;
                    response.Success = true;
                } else if (request.Mode == 103) {
                    MasterBool.aimbotScope = request.boolean;
                    response.Success = true;
                } else if (request.Mode == 104) {
                    MasterBool.aimbotFOV = request.value;
                    // LOGI(" Aim.maxAngle  %f", MasterBool.aimbotFOV);
                    response.Success = true;
                }
                server.send((void*)& response, sizeof(response));
            }
        }
    }
    return NULL;
}

bool isInsideFOV(int x, int y) {

    float circle_x = g_screenWidth / 2;
    float circle_y = g_screenHeight / 2;
    float rad = MasterBool.aimbotFOV;
    return (x - circle_x) * (x - circle_x) + (y - circle_y) * (y - circle_y) <= rad * rad;
}

Vector3 cSubtract(Vector3 src, Vector3 dst)
{
    Vector3 diff;
    diff.X = src.X - dst.X;
    diff.Y = src.Y - dst.Y;
    diff.Z = src.Z - dst.Z;
    return diff;
}

float cMagnitude(Vector3 vec)
{
    return sqrtf(vec.X*vec.X + vec.Y*vec.Y + vec.Z*vec.Z);
}

float cClamp(float value, float min, float max)
{
    if (value < min)
        value = min;
    else if (value > max)
        value = max;
    return value;
}

float CalculateDistance(Vector3 src, Vector3 dst)
{
    Vector3 diff = cSubtract(src, dst);
    return cMagnitude(diff);
}

void *GetEnemyInsideFOV()
{

    float MaxDist = 9999.0f;
    void *closestEnemy = nullptr;
    void *GameFacade = *(void **) getRealOffset(0x854F5E0);
    if (GameFacade != nullptr) {
        void *MatchGame = *(void **) ((uint64_t) GameFacade + 0x5C);
        if (MatchGame != nullptr) {
            void *ClassMatchGame = *(void **) ((uint64_t) MatchGame + 0x4);
            if (ClassMatchGame != nullptr) {
                void *current_match = *(void **) ((uint64_t) ClassMatchGame + 0x50);
                if (current_match != nullptr) {
                    auto matchState = *(uint32_t *) ((uint64_t) current_match + 0x8);
                    if (matchState == 1) {
                        void *CurrentLocalPlayer = Current_Local_Player();
                        if (CurrentLocalPlayer != nullptr) {
                            System_Collections_Generic_Dictionary_IHAAMHPPLMG__Player__o *MonoPlayer = *(System_Collections_Generic_Dictionary_IHAAMHPPLMG__Player__o **)((uint64_t) current_match + pAddress.ListadeJogadores);
                            if (MonoPlayer != nullptr) {
                                COW_GamePlay_Player_array *players = MonoPlayer->valueSlots;
                                if (players != nullptr) {
                                    int countagempartida = players->max_length;
                                    if (countagempartida != 1) {
                                        for (int i = 0; i < countagempartida; ++i) {
                                            if (players->m_Items[i] != nullptr &&players->m_Items[i] != CurrentLocalPlayer) {
                                                if (IsStreamerVisible(players->m_Items[i]) && !IsDieing(players->m_Items[i]) && !IsLocalTeammate(players->m_Items[i]) && GetHp(players->m_Items[i]) > 0) {
                                                    void *HeadTF = TransformNode(*(void **) ((uint64_t) players->m_Items[i] +pAddress.HeadTF));
                                                    if (HeadTF != nullptr) {
                                                        Vector3 currentLocation = Transform_INTERNAL_GetPosition(HeadTF);
                                                        Vector3 WorldToScreenHead = WorldToScreenPoint(currentLocation);

                                                        if (WorldToScreenHead.Z >= 0.01f) {
                                                            Vector3 v2Middle = Vector3((float) (g_screenWidth / 2),(float) (g_screenHeight / 2));
                                                            Vector3 v2Loc = Vector3(WorldToScreenHead.X,WorldToScreenHead.Y);
                                                            float Distance = (float) CalculateDistance(v2Middle, v2Loc);
                                                            if (isInsideFOV((int) WorldToScreenHead.X,(int) WorldToScreenHead.Y)) {
                                                                if (Distance < MaxDist) {
                                                                    closestEnemy = players->m_Items[i];
                                                                    MaxDist = Distance;
                                                                }
                                                            }
                                                        }

                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    return closestEnemy;

}


struct DamageInfo2_o {
    void *klass;
    void *monitor;
    int32_t DBLBLKADCNP;
    int32_t KENBMOOEHBG;
    monoString* JANPNJIFOJJ;
    bool NNNADMOFPIE;
    COW_GamePlay_IHAAMHPPLMG_o DHGCIEKPBFA;
    void* GPBDEDFKJNA;
    int32_t PIAMIOFEBKF;
    Vector3 CNEICNJFGLM;
    Vector3 HECJHKEDFEB;
    Vector3 JNLGFLFLBHO;
    uint8_t ACAKHEABPEJ;
    bool MJIHLDJNHLF;
    int32_t LOKIMAEAPCB;
    monoDictionary<uint8_t*, void **> *FHLFLAHCIBN;
};


static int GetDamage(void *pthis)
{
    return ((int (*)(void *))getRealOffset(0x16B4B44))(pthis);
}
COW_GamePlay_IHAAMHPPLMG_o GetplayerID(void *_this)
{
    return ((COW_GamePlay_IHAAMHPPLMG_o (*)(void *))getRealOffset(0x126EB38))(_this);
}
static void *GetWeaponOnHand(void *local) {
    void *(*GetWeaponOnHand)(void *local) = (void *(*)(void *))getRealOffset(0x1284858);
    return GetWeaponOnHand(local);
}
void SwapWeapon(void *Pthis,int32_t FANMJANBFIL,bool GDKLMFLNNGM)
{
    return ((void (*)(void *,int,bool,void*))getRealOffset(0x1943EB8))(Pthis,FANMJANBFIL,GDKLMFLNNGM,nullptr);
}
bool GameFacade_Send(uint32_t messageID,void *msg,uint8_t sendOption,bool cacheMsgAnyWay)
{
    return ((bool (*)(uint32_t,void *,uint8_t,bool))getRealOffset(0x1BA6A68))(messageID,msg,sendOption,cacheMsgAnyWay);
}

uint32_t CFFPIACECIG(COW_GamePlay_IHAAMHPPLMG_o IDNEFEOPGIF)
{
    return ((uint32_t (*)(COW_GamePlay_IHAAMHPPLMG_o))getRealOffset(0x1F90240))(IDNEFEOPGIF);
}
void *GKHECDLGAJA(void *pthis, void* a1)
{
    return ((void* (*)(void *,void *))getRealOffset(0x12F4D3C))(pthis,a1);
}
void Syns_SwapWeapon(void *LocalPlayer)
{
    void *WeaponOnHand = GetWeaponOnHand(LocalPlayer);
    if (WeaponOnHand != nullptr)
    {
        void *Class_RUDP_CHANGE_INVENTORY_ON_HAND_c = *(void **)getRealOffset(0x85553E8);
        if (Class_RUDP_CHANGE_INVENTORY_ON_HAND_c == nullptr)
        {
            int max;
            max = 5;
            SwapWeapon(LocalPlayer,rand()%max,1);
        }
        if (Class_RUDP_CHANGE_INVENTORY_ON_HAND_c != nullptr)
        {
            auto RUDP_CHANGE_INVENTORY_ON_HAND = ((void * (*)(void *))getRealOffset(0xDBA44C))(Class_RUDP_CHANGE_INVENTORY_ON_HAND_c);
            if (RUDP_CHANGE_INVENTORY_ON_HAND)
            {

                *(uint32_t*)((uint64_t)RUDP_CHANGE_INVENTORY_ON_HAND + 0xc) = CFFPIACECIG(GetplayerID(LocalPlayer));
                *(uint32_t*)((uint64_t)RUDP_CHANGE_INVENTORY_ON_HAND + 0x10) = *(uint32_t*)((uint64_t)WeaponOnHand + 0x8);
                GameFacade_Send(108, (GCommon_UDPClientMessageBase_o *)RUDP_CHANGE_INVENTORY_ON_HAND, 2, 0);
            }
        }
    }
    return;
}
monoList<float *> *LCLHHHKFCFP(void *Weapon,void *CAGCICACKCF,void *HFBDJJDICLN,bool LDGHPOPPPNL,DamageInfo2_o *DamageInfo)
{
    return ((monoList<float *> * (*)(void*,void*,void*,bool,DamageInfo2_o*))getRealOffset(0x16DF950))(Weapon,CAGCICACKCF,HFBDJJDICLN,LDGHPOPPPNL,DamageInfo);
}

int32_t Player_TakeDamage(void *Player, int32_t p_damage, COW_GamePlay_IHAAMHPPLMG_o PlayerID, DamageInfo2_o *DamageInfo, int32_t WeaponDataID, Vector3 FirePos, Vector3 TargetPos, monoList<float *> *CheckParams, void *p_idk1, int32_t p_idk2) {
    return ((int32_t (*)(void *, int32_t, COW_GamePlay_IHAAMHPPLMG_o, DamageInfo2_o *, int32_t, Vector3, Vector3, monoList<float *> *, void *, uint32_t))getRealOffset(0x19417CC))(Player, p_damage, PlayerID, DamageInfo, WeaponDataID, FirePos, TargetPos, CheckParams, p_idk1, p_idk2);
}
void StartAimKill(void* targetVivo)
{
    if (MasterBool.aimbotScope)
    {
        void *LocalPlayer = Current_Local_Player();
        if (LocalPlayer != NULL) {
            void *weaponOnHand = GetWeaponOnHand(LocalPlayer);
            if (weaponOnHand != nullptr)
            {
                //LOGI("weaponOnHand");
                static bool s_Il2CppMethodIntialized;
                if (!s_Il2CppMethodIntialized)
                {
                    ((void (*)(void *,void*))getRealOffset(0x16DBEE4))(weaponOnHand, *(void**)((uint64_t)LocalPlayer + 0x7DC));
                    ((void* (*)(void *))getRealOffset(0x12666D4))(LocalPlayer);
                    s_Il2CppMethodIntialized = true;
                }

                void* targetEnemy = nullptr;
                targetEnemy = targetVivo;

                if (targetEnemy != nullptr)
                {

                    void *ObjectPool = *(void **)((uintptr_t)LocalPlayer + 0x7DC);
                    if(ObjectPool != nullptr)
                    {
                        // LOGI("ObjectPool");
                        Syns_SwapWeapon(LocalPlayer);
                        ((void* (*)(void *, void *))getRealOffset(0x1942DD0))(LocalPlayer, weaponOnHand);
                        auto weaponDataID = *(uint32_t*)((uint64_t)*(void**)((uint64_t)weaponOnHand + 0x58) + 0x8);
                        auto baseDamage = GetDamage(weaponOnHand);
                        auto playerID = GetplayerID(LocalPlayer);
                        auto playerID2 = GetplayerID(targetEnemy);
                        void *HeadTF = TransformNode(*(void **) ((uint64_t) targetEnemy + pAddress.HeadTF));
                        Vector3 m_Head = Transform_INTERNAL_GetPosition(HeadTF);

                        void *HeadTF2 = TransformNode(*(void **) ((uint64_t) LocalPlayer + pAddress.HeadTF));
                        Vector3 m_HeadLocal = Transform_INTERNAL_GetPosition(HeadTF2);



                        auto DamageS2c = (message_CHDLJFJCPFN_o *)((void* (*)(void *))getRealOffset(0x12666D4))(LocalPlayer);
                        if (DamageS2c)
                        {
                            //LOGI("DamageS2c");
                            void *Class_message_DamageInfo_c = *(void **)getRealOffset(0x854F71C);
                            if (Class_message_DamageInfo_c)
                            {
                                // LOGI("Class_message_DamageInfo_c");
                                auto DamageInfo = (DamageInfo2_o *)((void * (*)(void *))getRealOffset(0xDBA44C))(Class_message_DamageInfo_c);
                                if (DamageInfo)
                                {
                                    //LOGI("DamageInfo");
                                    *(int*)((char*)DamageInfo + 0x8) = baseDamage;
                                    *(int*)((char*)DamageInfo + 0xC) = 1;
                                    *(void**)((char*)DamageInfo + 0x30) = weaponOnHand;
                                    *(int*)((char*)DamageInfo + 0x34) = weaponDataID;
                                    *(COW_GamePlay_IHAAMHPPLMG_o *)((char*)DamageInfo + 0x18) = playerID;
                                    void *JEEIBOEGGPD = *(void**)((uint64_t)LocalPlayer + 0x7DC);
                                    *(void**)((uint64_t)JEEIBOEGGPD + string2Offset(OBFUSCATE("0xc"))) = get_gameObject(get_HeadCollider(targetEnemy));
                                    *(void**)((uint64_t)JEEIBOEGGPD + string2Offset(OBFUSCATE("0x10"))) = get_HeadCollider(targetEnemy);
                                    *(int*)((uint64_t)JEEIBOEGGPD + 0x50) = 1;
                                    monoList<float *> *CheckParametros = LCLHHHKFCFP(GetWeaponOnHand(LocalPlayer),GKHECDLGAJA(LocalPlayer,*(void**)((uint64_t)LocalPlayer + 0x7DC)),get_HeadCollider(targetEnemy),false,DamageInfo);

                                    Player_TakeDamage(targetEnemy, baseDamage, playerID, DamageInfo, weaponDataID, m_HeadLocal, m_Head, CheckParametros, CheckParametros, 0);

                                }
                            }
                        }
                    }
                }
            }
        }
    }
    return;
}


static float get_realtimeSinceStartup() {
    float (*_get_realtimeSinceStartup)(void *nuls) = (float (*)(void *))getRealOffset(0x6BBFDD4);
    return _get_realtimeSinceStartup(NULL);
}

void* CurrentGameSimulationTimer()
{
    return ((void* (*)(void *))getRealOffset(0x1B9FA50))(NULL);
}

struct message_CHDLJFJCPFN_o2 {
    void *klass;
    void *monitor;
    bool UDPClientMessageBase_m_GetFromPool;
    uint32_t ALFINFGBOBE;
    uint16_t ECDBFHHNPMI;
    uint16_t EKCONDDBKFO;
    uint32_t BJBPPEBIPFA;
    uint32_t LIIGLCNGOHG;
    int32_t PIAMIOFEBKF;
    uint32_t HCMIEJEBKAL;
    uint8_t ODCJPCEJHPK;
    uint32_t CEDJCPLOLNE;
    message_DEACEIFBHJK_o* CNEICNJFGLM;
    message_DEACEIFBHJK_o* PGDEDHFOMCN;
    monoList<float*> * AALHLOAJLEE;
    uint32_t HOBOHHJNDNH;
    float AILHIPMKJKJ;
    uint64_t LHGGPCFJNOO;
    int8_t ACAKHEABPEJ;
    bool MJIHLDJNHLF;
    bool MBGCAHPACOH;
    monoList<int*> * FIKOAMIDEHL;
    float IOGIIEFAALP;
    bool HDEJLJKNLCI;
};
message_DEACEIFBHJK_o *LHGGPDLOPAH(Vector3 JOGHOHLEJFL)
{
    return ((message_DEACEIFBHJK_o* (*)(Vector3))getRealOffset(0x5D8BE98))(JOGHOHLEJFL);
}
monoList<int*> *DPPPFBHIHJA()
{
    return ((monoList<int*> * (*)())getRealOffset(0x4B7E788))();
}
static int get_CurHP(void *player) {
    int (*_get_CurHP)(void *players) = (int (*)(void *))getRealOffset(0x12F84B8);
    return _get_CurHP(player);
}
void StartAimKillSend(void* targetVivo)
{
    if (MasterBool.aimbotScope)
    {
        void *LocalPlayer = Current_Local_Player();
        if (LocalPlayer != NULL) {
            void *weaponOnHand = GetWeaponOnHand(LocalPlayer);
            if (weaponOnHand != nullptr)
            {
                static bool s_Il2CppMethodIntialized;
                if (!s_Il2CppMethodIntialized)
                {
                    ((void (*)(void *,void*))getRealOffset(0x16DBEE4))(weaponOnHand, *(void**)((uint64_t)LocalPlayer + 0x7DC));
                    ((void* (*)(void *))getRealOffset(0x12666D4))(LocalPlayer);
                    s_Il2CppMethodIntialized = true;
                }
                void* targetEnemy = nullptr;
                targetEnemy = targetVivo;

                if (targetEnemy != nullptr)
                {
                    void *ObjectPool = *(void **)((uintptr_t)LocalPlayer + 0x7DC);
                    if(ObjectPool != nullptr)
                    {
                        //SendStartReload(LocalPlayer);
                        //Syns_SwapWeapon(LocalPlayer);
                        auto weaponDataID = *(uint32_t*)((uint64_t)*(void**)((uint64_t)weaponOnHand + 0x58) + 0x8);
                        auto baseDamage = GetDamage(weaponOnHand);
                        auto playerID = GetplayerID(LocalPlayer);
                        auto playerID2 = GetplayerID(targetEnemy);
                        void *HeadTF = TransformNode(*(void **) ((uint64_t) targetEnemy + pAddress.HeadTF));
                        Vector3 hitPos = Transform_INTERNAL_GetPosition(HeadTF);
                        Vector3 firePos = CameraPosition(LocalPlayer);

                        auto DamageS2c = (message_CHDLJFJCPFN_o2 *)((void* (*)(void *))getRealOffset(0x12666D4))(LocalPlayer);
                        if (DamageS2c)
                        {

                            void *Class_message_DamageInfo_c = *(void **)getRealOffset(0x854F71C);//ELMGJKHIIAA
                            if (Class_message_DamageInfo_c)
                            {

                                auto DamageInfo = (DamageInfo2_o *)((void * (*)(void *))getRealOffset(0xDBA44C))(Class_message_DamageInfo_c);
                                if (DamageInfo) {

                                    *(int*)((char*)DamageInfo + 0x8) = baseDamage;
                                    *(int*)((char*)DamageInfo + 0xC) = 1;
                                    *(void**)((char*)DamageInfo + 0x30) = weaponOnHand;
                                    *(int*)((char*)DamageInfo + 0x34) = weaponDataID;
                                    *(COW_GamePlay_IHAAMHPPLMG_o *)((char*)DamageInfo + 0x18) = playerID;
                                    void *JEEIBOEGGPD = *(void**)((uint64_t)LocalPlayer + 0x7DC);
                                    *(void**)((uint64_t)JEEIBOEGGPD + string2Offset(OBFUSCATE("0xc"))) = get_gameObject(get_HeadCollider(targetEnemy));
                                    *(void**)((uint64_t)JEEIBOEGGPD + string2Offset(OBFUSCATE("0x10"))) = get_HeadCollider(targetEnemy);
                                    *(int*)((uint64_t)JEEIBOEGGPD + 0x50) = 1;
                                    monoList<float *> *CheckParametros = LCLHHHKFCFP(GetWeaponOnHand(LocalPlayer),GKHECDLGAJA(LocalPlayer,*(void**)((uint64_t)LocalPlayer + 0x7DC)),get_HeadCollider(targetEnemy),false,DamageInfo);

                                    GCommon_TimeService_o *GameSimulation = (GCommon_TimeService_o *) CurrentGameSimulationTimer();
                                    if (GameSimulation != nullptr) {

                                        int32_t KENBMOOEHBG = 1;
                                        //StartWholeBodyFiring(LocalPlayer);
                                        Syns_SwapWeapon(LocalPlayer);
                                        ((void* (*)(void *, void *))getRealOffset(0x1942DD0))(LocalPlayer, weaponOnHand);
                                        DamageS2c->ALFINFGBOBE = CFFPIACECIG(playerID2);
                                        DamageS2c->LIIGLCNGOHG = CFFPIACECIG(playerID);
                                        DamageS2c->BJBPPEBIPFA = baseDamage | (KENBMOOEHBG << 24);
                                        DamageS2c->ODCJPCEJHPK = KENBMOOEHBG;
                                        DamageS2c->PIAMIOFEBKF = weaponDataID;
                                        DamageS2c->CEDJCPLOLNE = GameSimulation->m_TickCount;
                                        DamageS2c->CNEICNJFGLM = LHGGPDLOPAH(firePos);
                                        DamageS2c->PGDEDHFOMCN = LHGGPDLOPAH(hitPos);
                                        DamageS2c->ECDBFHHNPMI = get_CurHP(targetEnemy);
                                        DamageS2c->LHGGPCFJNOO = 0;
                                        DamageS2c->FIKOAMIDEHL = DPPPFBHIHJA();
                                        DamageS2c->AALHLOAJLEE = CheckParametros;
                                        DamageS2c->IOGIIEFAALP = get_realtimeSinceStartup();
                                        DamageS2c->LHGGPCFJNOO = 0 ;
                                        DamageS2c->ACAKHEABPEJ = 0 ;
                                        DamageS2c->MJIHLDJNHLF = false;
                                        DamageS2c->MBGCAHPACOH = true;
                                        DamageS2c->EKCONDDBKFO = 0;
                                        DamageS2c->HDEJLJKNLCI = false;
                                        DamageS2c->AILHIPMKJKJ = 0;
                                        GameFacade_Send(106,DamageS2c,2, 0);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    return;
}
static float get_Range(void *pthis)
{
    return ((float (*)(void *))getRealOffset(0x16B0338))(pthis);
}
bool isEnemyInRangeWeapon(void *player, void *enemy, void* weapon)
{
    if (player != nullptr && enemy != nullptr && weapon != nullptr)
    {
        void *HeadTF = TransformNode(*(void **) ((uint64_t) enemy + pAddress.HeadTF));
        void *HeadTF2 = TransformNode(*(void **) ((uint64_t) player + pAddress.HeadTF));
        Vector3 EnemyHeadPosition = Transform_INTERNAL_GetPosition(HeadTF);
        Vector3 PlayerHeadPosition = Transform_INTERNAL_GetPosition(HeadTF2);
        float distance = Vector3::Distance(PlayerHeadPosition, EnemyHeadPosition);
        float range = get_Range(weapon);

        if (distance <= range) {
            return true;
        }
    }
    return false;
}

std::chrono::steady_clock::time_point last_update_time = std::chrono::steady_clock::now();

GCommon_AnimationRuntimeHandle_o *(*GetCurrentRunningHandler)(GCommon_AnimationSystemComponent_o *Instance,int32_t layerIndex);
GCommon_AnimationRuntimeHandle_o *_GetCurrentRunningHandler(GCommon_AnimationSystemComponent_o *Instance,int32_t layerIndex)
{
    if (Instance != nullptr) {
        if (MasterBool.enableESP) {
            std::chrono::steady_clock::time_point current_time = std::chrono::steady_clock::now();
            auto elapsed_time = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - last_update_time).count();
            if (elapsed_time > 1000 /20) {
                void *LocalPlayer = Current_Local_Player();
                if (LocalPlayer != nullptr) {

                    void *ClosestEnemy = GetEnemyInsideFOV();
                    if (ClosestEnemy != nullptr) {
                        void *weaponOnHand = GetWeaponOnHand(LocalPlayer);
                        if (weaponOnHand != nullptr) {
                            if (isEnemyInRangeWeapon(LocalPlayer, ClosestEnemy, weaponOnHand)) {
                                if(isVisible_Aimbot(ClosestEnemy)) {
                                    StartAimKillSend(ClosestEnemy);

                                }
                            }
                        }
                    }
                }
                last_update_time = std::chrono::steady_clock::now();
            }

            return GetCurrentRunningHandler(Instance, layerIndex);
        }
    }
    return GetCurrentRunningHandler(Instance,layerIndex);
}


#define lkanoil2cpp(address, original, backup)  DobbyHook((void *)getRealOffset(address), (void *)original, (void **)&backup)
#define lkanoanogs(address, original, backup)  DobbyHook((void *)getRealOffsetAnogs(address), (void *)original, (void **)&backup)
void *testelkmod(void *arg) {
    while (true) {

        if (getRealOffset(0) != 0) {

            LOGI("IL2CPP CARREGOU LK HYPE");

            //MemoryPatch::createWithHex(getRealOffsetAnogs(0x11B360), "00 00 A0 E3 1E FF 2F E1").Modify();
            //MemoryPatch::createWithHex(getRealOffsetAnogs(0x11AF40), "00 00 A0 E3 1E FF 2F E1").Modify();
            //MemoryPatch::createWithHex(getRealOffsetAnogs(0x11B620), "00 00 A0 E3 1E FF 2F E1").Modify();
            //MemoryPatch::createWithHex(getRealOffsetAnogs(0x11B704), "00 F0 20 E3").Modify();


            //lkanoanogs(string2Offset(OBFUSCATE("0x1D9378")), _sub_1D9378, sub_1D9378);

            //lkanoil2cpp(string2Offset(OBFUSCATE("0x16D7B00")), _KDPOBABIMBM, KDPOBABIMBM);
            lkanoil2cpp(string2Offset(OBFUSCATE("0x44FDFD0")), _GetCurrentRunningHandler, GetCurrentRunningHandler);



            pthread_exit(0);
        }
        sleep(0);
    }
    return NULL;
}



__attribute__((constructor))
void initializer() {

    LOGI("initializer");
    pthread_t ptid12;
    pthread_create(&ptid12, nullptr, testelkmod, nullptr);

    pthread_t ptid1;
    pthread_create(&ptid1, nullptr, CreateServer, nullptr);

}
