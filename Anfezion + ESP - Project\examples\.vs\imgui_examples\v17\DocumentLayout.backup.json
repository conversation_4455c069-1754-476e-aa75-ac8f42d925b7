{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Downloads\\4CHAN ESP\\4CHAN ESP\\Anfezion + ESP - Project\\examples\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{9F316E83-5AE5-4939-A723-305A94F48005}|example_win32_directx11\\example_win32_directx11.vcxproj|C:\\Users\\<USER>\\Downloads\\4CHAN ESP\\4CHAN ESP\\Anfezion + ESP - Project\\examples\\example_win32_directx11\\main.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{9F316E83-5AE5-4939-A723-305A94F48005}|example_win32_directx11\\example_win32_directx11.vcxproj|solutionrelative:example_win32_directx11\\main.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{9F316E83-5AE5-4939-A723-305A94F48005}|example_win32_directx11\\example_win32_directx11.vcxproj|C:\\Users\\<USER>\\Downloads\\4CHAN ESP\\4CHAN ESP\\Anfezion + ESP - Project\\examples\\example_win32_directx11\\main.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{9F316E83-5AE5-4939-A723-305A94F48005}|example_win32_directx11\\example_win32_directx11.vcxproj|solutionrelative:example_win32_directx11\\main.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{9F316E83-5AE5-4939-A723-305A94F48005}|example_win32_directx11\\example_win32_directx11.vcxproj|C:\\PROGRAM FILES\\MICROSOFT VISUAL STUDIO\\2022\\COMMUNITY\\VC\\TOOLS\\MSVC\\14.44.35207\\INCLUDE\\XMEMORY||{3B902123-F8A7-4915-9F01-361F908088D0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "main.h", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\4CHAN ESP\\4CHAN ESP\\Anfezion + ESP - Project\\examples\\example_win32_directx11\\main.h", "RelativeDocumentMoniker": "example_win32_directx11\\main.h", "ToolTip": "C:\\Users\\<USER>\\Downloads\\4CHAN ESP\\4CHAN ESP\\Anfezion + ESP - Project\\examples\\example_win32_directx11\\main.h", "RelativeToolTip": "example_win32_directx11\\main.h", "ViewState": "AgIAAA8AAAAAAAAAAAAIwCAAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-16T23:19:07.828Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "xmemory", "DocumentMoniker": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\xmemory", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\..\\..\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\xmemory", "ToolTip": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\xmemory", "RelativeToolTip": "..\\..\\..\\..\\..\\..\\..\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\xmemory", "ViewState": "AgIAAMMDAAAAAAAAAAAIwNgDAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-07-16T22:45:13.866Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "main.cpp", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\4CHAN ESP\\4CHAN ESP\\Anfezion + ESP - Project\\examples\\example_win32_directx11\\main.cpp", "RelativeDocumentMoniker": "example_win32_directx11\\main.cpp", "ToolTip": "C:\\Users\\<USER>\\Downloads\\4CHAN ESP\\4CHAN ESP\\Anfezion + ESP - Project\\examples\\example_win32_directx11\\main.cpp", "RelativeToolTip": "example_win32_directx11\\main.cpp", "ViewState": "AgIAALMEAAAAAAAAAAAnwNQEAABlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-16T22:33:17.245Z", "EditorCaption": ""}]}]}]}