
#define IMGUI_DEFINE_MATH_OPERATORS

#include "main.h"
#include <cfloat>

static int aim_head;
static int aim_neck;
static int aim_drag;
static int fakelag_key;
static int streamer_mode;

Memory pico;

namespace texture
{
    ID3D11ShaderResourceView* logo = nullptr;
}

static float fakelag2 = 2.0f;

int page = 0;

static float tab_alpha = 0.f;   static float tab_add;   static int active_tab = 0;

std::string btn_txt = "Login";

DWORD picker_flags = ImGuiColorEditFlags_NoSidePreview | ImGuiColorEditFlags_AlphaBar | ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_AlphaPreview;

bool done = false;
bool fake_lag;
static bool streammode = false;

bool authed = false;

int g_windowWidth = GetSystemMetrics(SM_CXSCREEN);
int g_windowHeight = GetSystemMetrics(SM_CYSCREEN);
HWND hdPlayerWindow = NULL;
bool isClickable = true;

bool ESPLine = false;
bool ESPInfo = false;
bool ESPBoxV1 = false;
bool ESPBoxV2 = false;
bool ESPBoxFILLED = false;
bool ESPBones = false;
bool ShowInvis = false;
bool ShowFovCircle = false;
bool AimbotAI = false;
bool RgbMode = false;

// Simple Aimbot Settings
bool AimbotSilent = false;
bool AimbotVisible = false;
bool AimbotSmooth = false;

// Teleport Settings (like NATCHO UP)
bool TeleKill = false;          // Teleport to enemy and kill
bool FlyMe = false;             // Fly mode
bool UpPlayer = false;          // Lift enemies up
float FlyHeight = 50.0f;        // Height for fly mode
bool TeleportToClosest = false; // Teleport to closest enemy

// Simple Entity struct for teleport
struct Entity {
    uint32_t Address;
    Vector3 Head;
    Vector3 Root;
    bool IsDead;
    bool IsKnocked;
    float Distance;
};

// Teleport Settings
bool TeleportToEnemy = false;
bool TeleportToSafeZone = false;
bool TeleportToLoot = false;
bool TeleportEnabled = false;
Vector3 teleportTarget = Vector3(0, 0, 0);
bool hasTeleportTarget = false;
static bool AimShowFovCircle = false;
float fovThickness = 1.0f;     
float fovThicknessAimbot = 1.0f;     
float fovRadius = 60.0f;          
float fovRadiusAimbot = 10.0f;           
static float maxDistance = 150.f;
static float EsplineWidth = 1.0f;


int slider3 = 0;
int lineType = 0;
int lineType1 = 0;
int FlyWall;
int WallHack;
int CamarDerecha;
ImVec4 boxColornormalv1 = ImVec4(25 / 255.f, 215 / 255.f, 130 / 255.f, 1.0f);
ImVec4 boxColornormalv2 = ImVec4(25 / 255.f, 215 / 255.f, 130 / 255.f, 1.0f);
ImVec4 distanciaColor = ImVec4(1.f,1.f,1.f, 0.6f);



float clearesp = 5.0f;
auto lastClearTime = std::chrono::steady_clock::now();


void PicoMain()
{

    

    int width2 = GetSystemMetrics(SM_CXSCREEN);
    int height2 = GetSystemMetrics(SM_CYSCREEN);

    WNDCLASSEXW wc;
    wc.cbSize = sizeof(WNDCLASSEXW);
    wc.style = CS_CLASSDC;
    wc.lpfnWndProc = WndProc;
    wc.cbClsExtra = NULL;
    wc.cbWndExtra = NULL;
    wc.hInstance = nullptr;
    wc.hIcon = LoadIcon(0, IDI_APPLICATION);
    wc.hCursor = LoadCursor(0, IDC_ARROW);
    wc.hbrBackground = nullptr;
    wc.lpszMenuName = L"ImGui";
    wc.lpszClassName = L"Example";
    wc.hIconSm = LoadIcon(0, IDI_APPLICATION);
    ::RegisterClassExW(&wc);

    hwnd = CreateWindowExW(
        WS_EX_TOPMOST | WS_EX_TOOLWINDOW | WS_EX_LAYERED,
        wc.lpszClassName, L" ",
        WS_POPUP,
        0, 0, width2, height2,
        nullptr, nullptr, wc.hInstance, nullptr
    );



    SetWindowLongA(hwnd, GWL_EXSTYLE, GetWindowLong(hwnd, GWL_EXSTYLE) | WS_EX_LAYERED | ~WS_EX_TRANSPARENT);
    SetLayeredWindowAttributes(hwnd, RGB(0, 0, 0), 255, LWA_ALPHA);

    MARGINS margins = { -1 };
    DwmExtendFrameIntoClientArea(hwnd, &margins);


    POINT mouse;
    RECT rc = { 0 };


    GetWindowRect(hwnd, &rc);

    if (!CreateDeviceD3D(hwnd))
    {
        CleanupDeviceD3D();
        ::UnregisterClassW(wc.lpszClassName, wc.hInstance);
        return;
    }

    ::ShowWindow(hwnd, SW_SHOWDEFAULT);
    ::UpdateWindow(hwnd);

    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableGamepad;
    
    static ImWchar font_range[] = { 0x0020, 0x10FFFF, 0 };

    ImFontConfig cfg;
    cfg.FontBuilderFlags = ImGuiFreeTypeBuilderFlags_ForceAutoHint | ImGuiFreeTypeBuilderFlags_LightHinting | ImGuiFreeTypeBuilderFlags_LoadColor;
    cfg.OversampleH = 2;
    cfg.OversampleV = 1;
    cfg.PixelSnapH = true;


    io.Fonts->AddFontFromMemoryTTF(&PoppinsRegular, sizeof PoppinsRegular, 20, NULL, io.Fonts->GetGlyphRangesCyrillic());

    font::primary_font = io.Fonts->AddFontFromMemoryTTF(&PoppinsRegular, sizeof PoppinsRegular, 20, NULL, io.Fonts->GetGlyphRangesCyrillic());
    
    font::second_font = io.Fonts->AddFontFromMemoryTTF(&PoppinsRegular, sizeof(PoppinsRegular), 18, NULL, io.Fonts->GetGlyphRangesCyrillic());

    font::icon_font = io.Fonts->AddFontFromMemoryTTF(&ico_moon, sizeof(ico_moon), 20, NULL, io.Fonts->GetGlyphRangesCyrillic());

    font::lexend_general_bold = io.Fonts->AddFontFromMemoryTTF(lexend_bold, sizeof(lexend_bold), 18.f, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    font::lexend_general_bold2 = io.Fonts->AddFontFromMemoryTTF(lexend_bold2, sizeof(lexend_bold2), 30.f, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    font::lexend_bold = io.Fonts->AddFontFromMemoryTTF(lexend_regular, sizeof(lexend_regular), 17.f, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    font::lexend_regular = io.Fonts->AddFontFromMemoryTTF(lexend_regular, sizeof(lexend_regular), 14.f, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    font::icomoon = io.Fonts->AddFontFromMemoryTTF(icomoon, sizeof(icomoon), 20.f, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    font::picomoon = io.Fonts->AddFontFromMemoryTTF(picomoon, sizeof(picomoon), 20.f, &cfg, io.Fonts->GetGlyphRangesCyrillic());

    font::Nevan = io.Fonts->AddFontFromMemoryTTF(Nevan, sizeof(Nevan), 20.f, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    font::ContiB = io.Fonts->AddFontFromMemoryTTF(continuum_bold, sizeof(continuum_bold), 32.f, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    font::ContiM = io.Fonts->AddFontFromMemoryTTF(continuum_medium, sizeof(continuum_medium), 45.f, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    ImFont* ContiM2 = io.Fonts->AddFontFromMemoryTTF(continuum_medium, sizeof(continuum_medium), 32.f, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    font::tab_font = io.Fonts->AddFontFromMemoryTTF(continuum_medium, sizeof(continuum_medium), 18.f, &cfg, io.Fonts->GetGlyphRangesCyrillic());

    font::icomoon_widget = io.Fonts->AddFontFromMemoryTTF(icomoon_widget, sizeof(icomoon_widget), 15.f, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    font::icomoon_widget2 = io.Fonts->AddFontFromMemoryTTF(icomoon, sizeof(icomoon), 16.f, &cfg, io.Fonts->GetGlyphRangesCyrillic());
    font::icon_font2 = io.Fonts->AddFontFromMemoryTTF(&icomoon2, sizeof icomoon2, 35, NULL, io.Fonts->GetGlyphRangesCyrillic());

    font::gff = io.Fonts->AddFontFromMemoryTTF(&gff, sizeof gff, 16.f, NULL, io.Fonts->GetGlyphRangesCyrillic());



    





    D3DX11_IMAGE_LOAD_INFO info; 
    ID3DX11ThreadPump* pump{ nullptr };
    if (texture::logo == nullptr) D3DX11CreateShaderResourceViewFromMemory(g_pd3dDevice, logo, sizeof(logo), &info, pump, &texture::logo, 0);

    ImGui_ImplWin32_Init(hwnd);
    ImGui_ImplDX11_Init(g_pd3dDevice, g_pd3dDeviceContext);



    while (!done)
    {

        MSG msg;
        while (::PeekMessage(&msg, nullptr, 0U, 0U, PM_REMOVE))
        {
            ::TranslateMessage(&msg);
            ::DispatchMessage(&msg);
            if (msg.message == WM_QUIT)
                done = true;
        }
        if (done) break;

        if (g_ResizeWidth != 0 && g_ResizeHeight != 0)
        {
            CleanupRenderTarget();
            g_pSwapChain->ResizeBuffers(0, g_ResizeWidth, g_ResizeHeight, DXGI_FORMAT_UNKNOWN, 0);
            g_ResizeWidth = g_ResizeHeight = 0;
            CreateRenderTarget();
        }

        ImGui_ImplDX11_NewFrame();
        ImGui_ImplWin32_NewFrame();

        static int opacity = 255;
        static bool hide = false;

        if (GetAsyncKeyState(VK_INSERT) & 1) 
        {

            hide = !hide;
            isClickable = !isClickable;
            ToggleClickability(isClickable);

        }


        if (GetAsyncKeyState(streamer_mode) & 1)
        {
            streammode = !streammode;

            HWND hwnd = GetActiveWindow();

            if (streammode)
            {
                notificationSystem.AddNotification("Done", "Stream Mode Enabled!", ImGui::GetColorU32(c::accent));

                SetWindowDisplayAffinity(hwnd, WDA_EXCLUDEFROMCAPTURE);

                ITaskbarList* pTaskList = nullptr;
                CoInitialize(nullptr);
                if (SUCCEEDED(CoCreateInstance(CLSID_TaskbarList, nullptr, CLSCTX_INPROC_SERVER, IID_ITaskbarList, (LPVOID*)&pTaskList)))
                {
                    pTaskList->DeleteTab(hwnd);
                    pTaskList->Release();
                }
                CoUninitialize();
            }
            else
            {
                notificationSystem.AddNotification("Done", "Stream Mode Disabled!", ImGui::GetColorU32(c::accent));

                SetWindowDisplayAffinity(hwnd, WDA_NONE);

                ITaskbarList* pTaskList = nullptr;
                CoInitialize(nullptr);
                if (SUCCEEDED(CoCreateInstance(CLSID_TaskbarList, nullptr, CLSCTX_INPROC_SERVER, IID_ITaskbarList, (LPVOID*)&pTaskList)))
                {
                    pTaskList->AddTab(hwnd);
                    pTaskList->Release();
                }
                CoUninitialize();
            }
        }

        if (GetAsyncKeyState(VK_DELETE) & 1)
        {
            exit(0);
        }
        auto currentTime = std::chrono::steady_clock::now();
        std::chrono::duration<double> elapsedSeconds = currentTime - lastClearTime;
        if (elapsedSeconds.count() >= clearesp) {
            CacheZ.clear();
            lastClearTime = currentTime;
        }
        static bool isKeyPressed = false;

        if ((GetAsyncKeyState(fakelag_key) & 0x8000) && fakelag2 > 0)
        {
            if (!isKeyPressed && fake_lag)
            {
                isKeyPressed = true;
                std::thread([]
                    {
                        Beep(444, 500);
                        auto start = std::chrono::steady_clock::now();

                        PauseNetwork();

                        auto elapsed = std::chrono::steady_clock::now() - start;
                        auto delay = ToMilliseconds(fakelag2); 

                        auto remaining = delay - std::chrono::duration_cast<std::chrono::milliseconds>(elapsed);

                        if (remaining.count() > 0)
                            std::this_thread::sleep_for(remaining); 

                        ResumeNetwork();
                        Beep(666, 470);
                    }).detach();
            }
        }
        else isKeyPressed = false;



        ImGui::NewFrame();
        {
            if (setup_done)
            {
                RECT rect;
                GetWindowRect(hdPlayerWindow, &rect);
                int x = rect.left;
                int y = rect.top;
                int width = rect.right - rect.left;
                int height = rect.bottom - rect.top;
                Vector2 screenCenter = Vector2(x + width / 2, y + height / 2);

                uint32_t baseGameFacade;
                ReadZ(Il2Cpp + InitBase, baseGameFacade);

                uint32_t gameFacade;
                ReadZ(baseGameFacade, gameFacade);

                uint32_t staticGameFacade;
                ReadZ(gameFacade + StaticClass, staticGameFacade);

                uint32_t currentGame;
                ReadZ(staticGameFacade, currentGame);

                uint32_t currentMatch;
                ReadZ(currentGame + CurrentMatch, currentMatch);

                uint32_t matchStatus;
                ReadZ(currentMatch + MatchStatus, matchStatus);

                uint32_t localPlayer;
                if (!ReadZ(currentMatch + LocalPlayer, localPlayer) || localPlayer == 0) {
                    std::cerr << "[Error] No se pudo leer localPlayer o es nulo." << std::endl;
                    localPlayer = 0;
                }

                uint32_t mainTransform;
                ReadZ(localPlayer + MainCameraTransform, mainTransform);

                Vector3 mainPos;
                GetPosition(mainTransform, mainPos);

                // FlyMe - Set player height
                if (FlyMe) {
                    uint32_t localRootBone;
                    if (ReadZ(localPlayer + Root, localRootBone) && localRootBone) {
                        uint32_t localTransform1, localTransform2, localMatrixPtr;
                        if (ReadZ(localRootBone + 0x8, localTransform1) && localTransform1 &&
                            ReadZ(localTransform1 + 0x8, localTransform2) && localTransform2 &&
                            ReadZ(localTransform2 + 0x20, localMatrixPtr) && localMatrixPtr) {

                            Vector3 currentPos;
                            if (ReadZ(localMatrixPtr + 0x80, currentPos)) {
                                currentPos.Y = FlyHeight; // Set to desired height
                                WriteZ(localMatrixPtr + 0x80, currentPos);
                            }
                        }
                    }
                }



                uint32_t followCamera;
                ReadZ(localPlayer + FollowCamera, followCamera);

                uint32_t camera;
                ReadZ(followCamera + Camera, camera);

                uint32_t cameraBase;
                ReadZ(camera + 0x8, cameraBase);

                Matrix4x4 viewMatrix;
                ReadZ(cameraBase + ViewMatrix, viewMatrix);

                uint32_t entityDictionary;
                ReadZ(currentGame + DictionaryEntities, entityDictionary);

                uint32_t entities;
                ReadZ(entityDictionary + 0x14, entities);

                entities = entities + 0x10;

                uint32_t entitiesCount;
                ReadZ(entityDictionary + 0x18, entitiesCount);

                for (int i = 0; i < entitiesCount; i++) {
                    bool isDead = false, IsKnown = false, IsTeam = false, isVisible = false, isBot = false;
                    int EspHealthInt = 0, enemyId = i;
                    float DistanceA = 0.0f, rectWidth = 150.0f, rectHeight = 30.0f;


                    struct BonePositions {
                        Vector3 RootV3, HeadV3, NeckV3, HipV3, RightShoulderV3, LeftShoulderV3;
                        Vector3 RightElbowV3, LeftElbowV3, RightWristV3, LeftWristV3, RightFootV3, LeftFootV3;
                    } bonePositions;


                    uint32_t entity;
                    if (!ReadZ((uintptr_t)(i * 0x4 + entities), entity) || entity == 0) continue;

                    uint32_t avatarManager;
                    if (!ReadZ(entity + AvatarManager, avatarManager) || avatarManager == 0) continue;


                    uint32_t avatar;
                    if (!ReadZ(avatarManager + Avatar, avatar) || avatar == 0) continue;

                    if (!ReadZ(avatar + Avatar_IsVisible, isVisible)) {
                        continue;
                    }

                    uint32_t avatarData;
                    if (!ReadZ(avatar + Avatar_Data, avatarData) || avatarData == 0) continue;


                    bool isTeam;
                    if (ReadZ(avatarData + Avatar_Data_IsTeam, isTeam)) {
                        IsTeam = isTeam;
                        IsKnown = !isTeam;
                    }


                    ReadZ(entity + isBotOffs, isBot);

                    
                    if (ShowFovCircle) {
                        ImVec2 screenCenter(g_windowWidth / 2.0f, g_windowHeight / 2.0f);
                        POINT middlePos;
                        if (GetBluestacksMiddlePos(middlePos)) {
                            screenCenter = ImVec2(middlePos.x - 16, middlePos.y + 16);
                            DrawSmoothCircle(screenCenter, fovRadius, fovThickness);
                        }
                    }


                    // Enhanced Aimbot System (AI + Silent + Visible)
                    if ((AimbotAI || AimbotSilent || AimbotVisible) && !IsTeam) {

                        ImVec2 screenCenter(g_windowWidth / 2.0f, g_windowHeight / 2.0f);
                        POINT middlePos;

                        bool isKeyPressed = false;
                        if (selectedKey == AimKey::LeftMouseButton) {
                            isKeyPressed = (GetAsyncKeyState(VK_RBUTTON) & 0x8000) || (GetAsyncKeyState(VK_LBUTTON) & 0x8000);
                        }

                        if (isKeyPressed) {
                            ImVec2 targetPos = WorldToScreenImVec2(viewMatrix, bonePositions.HeadV3, g_windowWidth, g_windowHeight);

                            bool isOnScreen = targetPos.x > 0.0f && targetPos.y > 0.0f && targetPos.x < g_windowWidth && targetPos.y < g_windowHeight;

                            if (isOnScreen) {
                                float dx = targetPos.x - screenCenter.x;
                                float dy = targetPos.y - screenCenter.y;
                                float distanceSq = dx * dx + dy * dy;

                                if (distanceSq <= fov * fov) {
                                    uint32_t rHeadCollider = 0;
                                    if (ReadZ(entity + HeadCollider, rHeadCollider) && rHeadCollider) {

                                        // Silent Aimbot - Most Powerful & Invisible
                                        if (AimbotSilent) {
                                            WriteZ(entity + CurrentMatch, rHeadCollider);
                                            WriteZ(entity + 0x50, rHeadCollider);
                                            WriteZ(entity + 0x54, rHeadCollider);
                                        }
                                        // Visible Aimbot - Fast & Reliable
                                        else if (AimbotVisible) {
                                            for (int j = 0; j < 20; j++) {
                                                WriteZ(entity + CurrentMatch, rHeadCollider);
                                            }
                                        }
                                        // Legacy Aimbot - Basic & Stable
                                        else if (AimbotAI) {
                                            WriteZ(entity + CurrentMatch, rHeadCollider);
                                            std::this_thread::sleep_for(std::chrono::milliseconds(10));
                                        }
                                    }
                                }
                            }
                        }
                    }


                    if (ESPInfo) {
                        uint32_t dataPool;
                        if (ReadZ(entity + Player_Data, dataPool)) {
                            uint32_t poolObj;
                            if (ReadZ(dataPool + 0x8, poolObj)) {
                                uint32_t pool;
                                if (ReadZ(poolObj + 0x10, pool)) {
                                    int Health;
                                    if (ReadZ(pool + 0x10, Health) && Health) EspHealthInt = Health;
                                }
                            }
                        }
                    }

                    std::vector<std::pair<uint32_t, Vector3*>> bones = {
                        {Head, &bonePositions.HeadV3}, {Root, &bonePositions.RootV3},
                        {Neck, &bonePositions.NeckV3}, {Hip, &bonePositions.HipV3},
                        {RightShoulder, &bonePositions.RightShoulderV3}, {LeftShoulder, &bonePositions.LeftShoulderV3},
                        {RightElbow, &bonePositions.RightElbowV3}, {LeftElbow, &bonePositions.LeftElbowV3},
                        {RightWrist, &bonePositions.RightWristV3}, {LeftWrist, &bonePositions.LeftWristV3},
                        {RightFoot, &bonePositions.RightFootV3}, {LeftFoot, &bonePositions.LeftFootV3}
                    };

                    for (const auto& [boneID, bonePos] : bones) {
                        uint32_t bone;
                        if (ReadZ(entity + boneID, bone) && bone) GetNodePosition(bone, *bonePos);
                    }



                    // Simple Teleport Features (NATCHO Style)
                    if (TeleKill && !IsTeam) {
                        // Get local player root bone
                        uint32_t localRootBone;
                        if (ReadZ(localPlayer + Root, localRootBone) && localRootBone) {
                            uint32_t localTransform1, localTransform2, localMatrixPtr;
                            if (ReadZ(localRootBone + 0x8, localTransform1) && localTransform1 &&
                                ReadZ(localTransform1 + 0x8, localTransform2) && localTransform2 &&
                                ReadZ(localTransform2 + 0x20, localMatrixPtr) && localMatrixPtr) {

                                // Teleport to enemy position
                                Vector3 enemyPos = bonePositions.HipV3;
                                enemyPos.Y += 2.0f; // Slightly above enemy
                                WriteZ(localMatrixPtr + 0x80, enemyPos);
                            }
                        }
                    }

                    // UpPlayer Feature - Lift enemies up
                    if (UpPlayer && !IsTeam) {
                        uint32_t rootBone;
                        if (ReadZ(entity + Root, rootBone) && rootBone) {
                            uint32_t transform1, transform2, matrixPtr;
                            if (ReadZ(rootBone + 0x8, transform1) && transform1 &&
                                ReadZ(transform1 + 0x8, transform2) && transform2 &&
                                ReadZ(transform2 + 0x20, matrixPtr) && matrixPtr) {

                                Vector3 enemyPos;
                                if (ReadZ(matrixPtr + 0x80, enemyPos)) {
                                    enemyPos.Y += 0.1f; // Lift up
                                    WriteZ(matrixPtr + 0x80, enemyPos);
                                }
                            }
                        }
                    }

                    bool isKnocked = false;
                    uint32_t shadowBase;
                    if (ReadZ(entity + Player_ShadowBase, shadowBase) && shadowBase) {
                        int xpose;
                        if (ReadZ(shadowBase + XPose, xpose) && xpose == 8) isKnocked = true;
                    }

                    DistanceA = Vector3::Distance(mainPos, bonePositions.HeadV3);
                    if (DistanceA > maxDistance) continue;

                    auto headScreenPos = WorldToScreenImVec2(viewMatrix, bonePositions.HeadV3, width, height);
                    auto NeckScreenPos = WorldToScreenImVec2(viewMatrix, bonePositions.NeckV3, width, height);
                    auto HipScreenPos = WorldToScreenImVec2(viewMatrix, bonePositions.HipV3, width, height);
                    auto RightShoulderScreenPos = WorldToScreenImVec2(viewMatrix, bonePositions.RightShoulderV3, width, height);
                    auto RightElbowScreenPos = WorldToScreenImVec2(viewMatrix, bonePositions.RightElbowV3, width, height);
                    auto RightWristScreenPos = WorldToScreenImVec2(viewMatrix, bonePositions.RightWristV3, width, height);
                    auto LeftShoulderScreenPos = WorldToScreenImVec2(viewMatrix, bonePositions.LeftShoulderV3, width, height);
                    auto LeftElbowScreenPos = WorldToScreenImVec2(viewMatrix, bonePositions.LeftElbowV3, width, height);
                    auto LeftWristScreenPos = WorldToScreenImVec2(viewMatrix, bonePositions.LeftWristV3, width, height);
                    auto RightFootScreenPos = WorldToScreenImVec2(viewMatrix, bonePositions.RightFootV3, width, height);
                    auto LeftFootScreenPos = WorldToScreenImVec2(viewMatrix, bonePositions.LeftFootV3, width, height);
                    auto bottomScreenPos = WorldToScreenImVec2(viewMatrix, bonePositions.RootV3, width, height);
                    auto bottomScreenPosLine = WorldToScreenImVec2(viewMatrix, bonePositions.HeadV3, width, height);
                    ImVec2 rectTopLeft = ImVec2(x + headScreenPos.x - rectWidth / 2, y + headScreenPos.y - rectHeight - 10);
                    ImVec2 rectBottomRight = ImVec2(x + headScreenPos.x + rectWidth / 2, y + headScreenPos.y - 10);

                    Vector2 PlayerPosHead(x + headScreenPos.x, y + headScreenPos.y);
                    Vector2 PlayerPosBott(x + bottomScreenPos.x, y + bottomScreenPos.y);
                    float CornerHeight = std::abs(PlayerPosHead.Y - PlayerPosBott.Y);
                    float CornerWidth = CornerHeight * 0.65f;

                    if (ShowInvis == false && !isVisible) continue;

                    const char* windowTitles[] = { "MSI App Player", "BlueStacks App Player", nullptr };
                    HWND hwnd = nullptr;
                    for (int j = 0; windowTitles[j]; j++) {
                        if ((hwnd = FindWindowA(NULL, windowTitles[j]))) break;
                    }
                    if (!hwnd) return;

                    RECT clientRect, windowRect;
                    GetClientRect(hwnd, &clientRect);
                    GetWindowRect(hwnd, &windowRect);

                    int emulatorWidth = clientRect.right - clientRect.left;
                    int emulatorHeight = clientRect.bottom - clientRect.top;
                    int clientX = windowRect.left;
                    int clientY = windowRect.top + (windowRect.bottom - windowRect.top - emulatorHeight);

                    bool isOnScreen = (headScreenPos.x > 0 && headScreenPos.y > 0 &&
                        (x + headScreenPos.x) >= clientX && (x + headScreenPos.x) <= clientX + emulatorWidth &&
                        (y + headScreenPos.y) >= clientY && (y + headScreenPos.y) <= clientY + emulatorHeight);

                    if (!isOnScreen) continue;

                    auto vList = ImGui::GetBackgroundDrawList();

                    vList->PushClipRect(ImVec2(clientX, clientY), ImVec2(clientX + emulatorWidth, clientY + emulatorHeight), true);

                    ImVec2 screenCenter(g_windowWidth / 2.0f, g_windowHeight / 2.0f);
                    POINT middlePos;

                    if (ESPBoxV1 && !IsTeam) {
                        float XBox = PlayerPosHead.X - (CornerWidth / 2);
                        float YBox = PlayerPosHead.Y - 10.0f;
                        float WBox = CornerWidth;
                        float HBox = CornerHeight + 20.0f;
                        float thickness = 1.0f;
                        float rounding = 0.0f;    

                        ImU32 boxColorx = ImGui::ColorConvertFloat4ToU32(boxColornormalv1);
                        ImU32 shadowColor = IM_COL32(0, 0, 0, 0);

                        if (RgbMode) {
                            double time = getCurrentTime();
                            float frequency = rgbSpeed;
                            float red = 0.5f * (1.0f + std::sin(2.0 * M_PI * frequency * time));
                            float green = 0.5f * (1.0f + std::sin(2.0 * M_PI * frequency * time + 2.0 * M_PI / 3.0));
                            float blue = 0.5f * (1.0f + std::sin(2.0 * M_PI * frequency * time + 4.0 * M_PI / 3.0));

                            boxColorx = ImGui::ColorConvertFloat4ToU32(ImVec4(red, green, blue, 1.0f));
                            boxColor = ImVec4(red, green, blue, 1.0f);
                        }
                        else {
                            boxColorx = isKnocked ? ImGui::GetColorU32(IM_COL32(255, 0, 0, 255)) : ImGui::GetColorU32(boxColornormalv1);
                        }

                        vList->AddRect(ImVec2(XBox - 1, YBox - 1), ImVec2(XBox + WBox + 1, YBox + HBox + 1),
                            shadowColor, rounding, ImDrawFlags_RoundCornersNone, thickness + 1.5f);
                        vList->AddRect(ImVec2(XBox, YBox), ImVec2(XBox + WBox, YBox + HBox),
                            boxColorx, rounding, ImDrawFlags_RoundCornersNone, thickness);
                    }

                    if (ESPBoxV2 && !IsTeam) {
                        float XBox = PlayerPosHead.X - (CornerWidth / 2);
                        float YBox = PlayerPosHead.Y - 10.0f;
                        float WBox = CornerWidth;
                        float HBox = CornerHeight + 20.0f;

                        float lineLengthH = WBox * 0.30f;
                        float lineLengthV = HBox * 0.35f;
                        float thickness = 1.5f;

                        float shadowPad = 2.0f;
                        float shadowThickness = 10.0f;
                        ImVec2 shadowOffset = ImVec2(1.5f, 1.5f);

                        ImU32 boxColorx = ImGui::ColorConvertFloat4ToU32(boxColornormalv2);

                        if (RgbMode) {
                            double time = getCurrentTime();
                            float frequency = rgbSpeed;
                            float red = 0.5f * (1.0f + std::sin(2.0 * M_PI * frequency * time));
                            float green = 0.5f * (1.0f + std::sin(2.0 * M_PI * frequency * time + 2.0 * M_PI / 3.0));
                            float blue = 0.5f * (1.0f + std::sin(2.0 * M_PI * frequency * time + 4.0 * M_PI / 3.0));
                            boxColorx = ImGui::ColorConvertFloat4ToU32(ImVec4(red, green, blue, 1.0f));
                            boxColor = ImVec4(red, green, blue, 1.0f);
                        }
                        else {
                            boxColorx = isKnocked ? ImGui::GetColorU32(IM_COL32(255, 0, 0, 255)) : ImGui::GetColorU32(boxColornormalv2);
                        }

                        auto AddShadowLineH = [&](float x, float y, float len) {
                            float shadowYMin = y - (shadowPad + thickness) / 2.0f;
                            float shadowYMax = y + (shadowPad + thickness) / 2.0f;
                            vList->AddShadowRect(
                                ImVec2(x - shadowPad / 2.0f, shadowYMin),
                                ImVec2(x + len + shadowPad / 2.0f, shadowYMax),
                                boxColorx, shadowThickness, shadowOffset, 0, 5.0f);
                            vList->AddLine(ImVec2(x, y), ImVec2(x + len, y), boxColorx, thickness);
                            };

                        auto AddShadowLineV = [&](float x, float y, float len) {
                            float shadowXMin = x - (shadowPad + thickness) / 2.0f;
                            float shadowXMax = x + (shadowPad + thickness) / 2.0f;
                            vList->AddShadowRect(
                                ImVec2(shadowXMin, y - shadowPad / 2.0f),
                                ImVec2(shadowXMax, y + len + shadowPad / 2.0f),
                                boxColorx, shadowThickness, shadowOffset, 0, 5.0f);
                            vList->AddLine(ImVec2(x, y), ImVec2(x, y + len), boxColorx, thickness);
                            };

                        AddShadowLineH(XBox, YBox, lineLengthH);
                        AddShadowLineV(XBox, YBox, lineLengthV);

                        AddShadowLineH(XBox + WBox - lineLengthH, YBox, lineLengthH);
                        AddShadowLineV(XBox + WBox, YBox, lineLengthV);

                        AddShadowLineV(XBox, YBox + HBox - lineLengthV, lineLengthV);
                        AddShadowLineH(XBox, YBox + HBox, lineLengthH);

                        AddShadowLineV(XBox + WBox, YBox + HBox - lineLengthV, lineLengthV);
                        AddShadowLineH(XBox + WBox - lineLengthH, YBox + HBox, lineLengthH);
                    }


                    if (ESPLine && !IsTeam) {
                        ImVec2 startPoint;
                        switch (lineType) {
                        case 0: startPoint = ImVec2(clientX + (emulatorWidth / 2), clientY + 33); break;  
                        case 1: startPoint = ImVec2(clientX + (emulatorWidth / 2), clientY + (emulatorHeight / 2)); break;  
                        case 2: startPoint = ImVec2(clientX + (emulatorWidth / 2), clientY + (emulatorHeight - 20)); break;  
                        default: startPoint = ImVec2(clientX + (emulatorWidth / 2), clientY + 33); break;
                        }

                        ImVec2 endPoint = ImVec2(x + bottomScreenPosLine.x, y + bottomScreenPosLine.y);    

                        ImU32 lineColor;
                        if (RgbMode) {
                            double time = getCurrentTime();
                            float frequency = rgbSpeed;
                            float red = 0.5f * (1.0f + sin(2.0 * M_PI * frequency * time));
                            float green = 0.5f * (1.0f + sin(2.0 * M_PI * frequency * time + 2.0 * M_PI / 3.0));
                            float blue = 0.5f * (1.0f + sin(2.0 * M_PI * frequency * time + 4.0 * M_PI / 3.0));
                            lineColor = ImGui::ColorConvertFloat4ToU32(ImVec4(red, green, blue, 1.0f));
                        }
                        else {
                            lineColor = isKnocked ? ImGui::GetColorU32(IM_COL32(255, 0, 0, 255)) : ImGui::GetColorU32(lineColorEnemy);
                        }


                        if (lineType1 == 0) {
                            ImVec2 enemyPos = ImVec2(x + headScreenPos.x, y + headScreenPos.y);
                            for (float thickness = EsplineWidth + 2.0f; thickness > EsplineWidth; thickness -= 1.5f) {
                                vList->AddLine(startPoint, enemyPos,
                                    ImColor(ImGui::ColorConvertU32ToFloat4(lineColor).x,
                                        ImGui::ColorConvertU32ToFloat4(lineColor).y,
                                        ImGui::ColorConvertU32ToFloat4(lineColor).z, 0.0f),
                                    thickness);
                            }
                            vList->AddLine(startPoint, enemyPos, lineColor, EsplineWidth);
                        }

                        else if (lineType1 == 1) {

                            ImVec2 control1 = ImVec2(startPoint.x, (startPoint.y + endPoint.y) / 2 - 50);
                            ImVec2 control2 = ImVec2(endPoint.x, (startPoint.y + endPoint.y) / 2 + 50);
                            const int segments = 50;
                            double time = getCurrentTime();
                            float frequency = rgbSpeed;

                            for (int i = 0; i < segments; i++) {
                                float t1 = float(i) / segments;
                                float t2 = float(i + 1) / segments;
                                ImVec2 p1 = ImBezierCubicCalc(startPoint, control1, control2, endPoint, t1);
                                ImVec2 p2 = ImBezierCubicCalc(startPoint, control1, control2, endPoint, t2);

                                float red = 0.5f * (1.0f + sin(2.0 * M_PI * frequency * (time + t1)));
                                float green = 0.5f * (1.0f + sin(2.0 * M_PI * frequency * (time + t1) + 2.0 * M_PI / 3.0));
                                float blue = 0.5f * (1.0f + sin(2.0 * M_PI * frequency * (time + t1) + 4.0 * M_PI / 3.0));

                                ImU32 color = ImGui::ColorConvertFloat4ToU32(ImVec4(red, green, blue, 1.0f));
                                vList->AddLine(p1, p2, color, EsplineWidth);
                            }
                        }

                        else if (lineType1 == 2) {
                            ImVec2 enemyPos = ImVec2(x + headScreenPos.x, y + headScreenPos.y);

                            const int segments = 15;
                            float amplitude = 15.0f;

                            for (int j = 0; j < segments; j++) {
                                float t1 = float(j) / segments;
                                float t2 = float(j + 1) / segments;

                                ImVec2 p1(startPoint.x + t1 * (enemyPos.x - startPoint.x),
                                    startPoint.y + t1 * (enemyPos.y - startPoint.y) + ((j % 2 == 0) ? amplitude : -amplitude));
                                ImVec2 p2(startPoint.x + t2 * (enemyPos.x - startPoint.x),
                                    startPoint.y + t2 * (enemyPos.y - startPoint.y) + ((j % 2 == 0) ? -amplitude : amplitude));

                                ImU32 lineColor = RgbMode ?
                                    ImGui::ColorConvertFloat4ToU32(ImVec4(
                                        0.5f * (1.0f + std::sin(2.0 * 3.14159265 * 0.5f * getCurrentTime())),
                                        0.5f * (1.0f + std::sin(2.0 * 3.14159265 * 0.5f * getCurrentTime() + 2.0 * 3.14159265 / 3.0)),
                                        0.5f * (1.0f + std::sin(2.0 * 3.14159265 * 0.5f * getCurrentTime() + 4.0 * 3.14159265 / 3.0)),
                                        1.0f)) :
                                    (isKnocked ? IM_COL32(255, 0, 0, 255) : ImGui::GetColorU32(lineColorEnemy));

                                for (float thickness = EsplineWidth + 2.0f; thickness > EsplineWidth; thickness -= 1.5f) {
                                    vList->AddLine(p1, p2,
                                        ImColor(ImGui::ColorConvertU32ToFloat4(lineColor).x,
                                            ImGui::ColorConvertU32ToFloat4(lineColor).y,
                                            ImGui::ColorConvertU32ToFloat4(lineColor).z, 0.0f),
                                        thickness);
                                }

                                vList->AddLine(p1, p2, lineColor, EsplineWidth);
                            }
                        }
                    }

                    if (ESPBones && !IsTeam) {
                        ImU32 boneColor;

                        if (isKnocked) {
                            boneColor = IM_COL32(255, 0, 0, 255);
                        }
                        else if (RgbMode) {
                            auto getRGBColor = [&](float offset) -> ImU32 {
                                double time = getCurrentTime();
                                float frequency = rgbSpeed;        
                                float red = 0.5f * (1.0f + std::sin(2.0 * M_PI * frequency * (time + offset)));
                                float green = 0.5f * (1.0f + std::sin(2.0 * M_PI * frequency * (time + offset) + 2.0 * M_PI / 3.0));
                                float blue = 0.5f * (1.0f + std::sin(2.0 * M_PI * frequency * (time + offset) + 4.0 * M_PI / 3.0));
                                return IM_COL32(red * 255, green * 255, blue * 255, 255);
                                };

                            if (headScreenPos.x > 0 && NeckScreenPos.x > 0 && HipScreenPos.x > 0 && DistanceA > 1.0f) {
                                float baseCircleSize = 2.f;
                                float distanceFactor = std::clamp(100.0f / DistanceA, 0.5f, 1.5f);
                                float circleSize = baseCircleSize * distanceFactor;

                                vList->AddCircle(ImVec2(x + headScreenPos.x, y + headScreenPos.y), circleSize, getRGBColor(0.1f), 0, 2.f);
                                vList->AddLine(ImVec2(x + headScreenPos.x, y + headScreenPos.y), ImVec2(x + NeckScreenPos.x, y + NeckScreenPos.y), getRGBColor(0.2f), 2.f);
                                vList->AddLine(ImVec2(x + NeckScreenPos.x, y + NeckScreenPos.y), ImVec2(x + HipScreenPos.x, y + HipScreenPos.y), getRGBColor(0.3f), 2.f);
                                vList->AddLine(ImVec2(x + HipScreenPos.x, y + HipScreenPos.y), ImVec2(x + RightFootScreenPos.x, y + RightFootScreenPos.y), getRGBColor(0.4f), 2.f);
                                vList->AddLine(ImVec2(x + HipScreenPos.x, y + HipScreenPos.y), ImVec2(x + LeftFootScreenPos.x, y + LeftFootScreenPos.y), getRGBColor(0.5f), 2.f);
                                vList->AddLine(ImVec2(x + NeckScreenPos.x, y + NeckScreenPos.y), ImVec2(x + RightShoulderScreenPos.x, y + RightShoulderScreenPos.y), getRGBColor(0.6f), 2.f);
                                vList->AddLine(ImVec2(x + RightShoulderScreenPos.x, y + RightShoulderScreenPos.y), ImVec2(x + RightElbowScreenPos.x, y + RightElbowScreenPos.y), getRGBColor(0.7f), 2.f);
                                vList->AddLine(ImVec2(x + RightElbowScreenPos.x, y + RightElbowScreenPos.y), ImVec2(x + RightWristScreenPos.x, y + RightWristScreenPos.y), getRGBColor(0.8f), 2.f);
                                vList->AddLine(ImVec2(x + NeckScreenPos.x, y + NeckScreenPos.y), ImVec2(x + LeftShoulderScreenPos.x, y + LeftShoulderScreenPos.y), getRGBColor(0.9f), 2.f);
                                vList->AddLine(ImVec2(x + LeftShoulderScreenPos.x, y + LeftShoulderScreenPos.y), ImVec2(x + LeftElbowScreenPos.x, y + LeftElbowScreenPos.y), getRGBColor(1.0f), 2.f);
                                vList->AddLine(ImVec2(x + LeftElbowScreenPos.x, y + LeftElbowScreenPos.y), ImVec2(x + LeftWristScreenPos.x, y + LeftWristScreenPos.y), getRGBColor(1.1f), 2.f);
                            }
                        }
                        else {
                            boneColor = ImGui::GetColorU32(bonesColor);
                            if (headScreenPos.x > 0 && NeckScreenPos.x > 0 && HipScreenPos.x > 0 && DistanceA > 1.0f) {
                                float baseCircleSize = 2.f;
                                float distanceFactor = std::clamp(100.0f / DistanceA, 0.5f, 1.5f);
                                float circleSize = baseCircleSize * distanceFactor;

                                vList->AddCircle(ImVec2(x + headScreenPos.x, y + headScreenPos.y), circleSize, boneColor, 0, 2.f);
                                vList->AddLine(ImVec2(x + headScreenPos.x, y + headScreenPos.y), ImVec2(x + NeckScreenPos.x, y + NeckScreenPos.y), boneColor, 2.f);
                                vList->AddLine(ImVec2(x + NeckScreenPos.x, y + NeckScreenPos.y), ImVec2(x + HipScreenPos.x, y + HipScreenPos.y), boneColor, 2.f);
                                vList->AddLine(ImVec2(x + HipScreenPos.x, y + HipScreenPos.y), ImVec2(x + RightFootScreenPos.x, y + RightFootScreenPos.y), boneColor, 2.f);
                                vList->AddLine(ImVec2(x + HipScreenPos.x, y + HipScreenPos.y), ImVec2(x + LeftFootScreenPos.x, y + LeftFootScreenPos.y), boneColor, 2.f);
                                vList->AddLine(ImVec2(x + NeckScreenPos.x, y + NeckScreenPos.y), ImVec2(x + RightShoulderScreenPos.x, y + RightShoulderScreenPos.y), boneColor, 2.f);
                                vList->AddLine(ImVec2(x + RightShoulderScreenPos.x, y + RightShoulderScreenPos.y), ImVec2(x + RightElbowScreenPos.x, y + RightElbowScreenPos.y), boneColor, 2.f);
                                vList->AddLine(ImVec2(x + RightElbowScreenPos.x, y + RightElbowScreenPos.y), ImVec2(x + RightWristScreenPos.x, y + RightWristScreenPos.y), boneColor, 2.f);
                                vList->AddLine(ImVec2(x + NeckScreenPos.x, y + NeckScreenPos.y), ImVec2(x + LeftShoulderScreenPos.x, y + LeftShoulderScreenPos.y), boneColor, 2.f);
                                vList->AddLine(ImVec2(x + LeftShoulderScreenPos.x, y + LeftShoulderScreenPos.y), ImVec2(x + LeftElbowScreenPos.x, y + LeftElbowScreenPos.y), boneColor, 2.f);
                                vList->AddLine(ImVec2(x + LeftElbowScreenPos.x, y + LeftElbowScreenPos.y), ImVec2(x + LeftWristScreenPos.x, y + LeftWristScreenPos.y), boneColor, 2.f);
                            }
                        }
                    }

                    if (ESPBoxFILLED && !IsTeam) {
                        float XBox = PlayerPosHead.X - (CornerWidth / 2);
                        float YBox = PlayerPosHead.Y - 10.0f;
                        float WBox = CornerWidth;
                        float HBox = CornerHeight + 20.0f;
                        float rounding = 0.0f;    

                        if (RgbMode) {
                            double time = getCurrentTime();
                            float frequency = rgbSpeed;
                            auto getRGBColor = [&](float offset, int alpha) -> ImU32 {
                                float red = 0.5f * (1.0f + std::sin(2.0 * 3.14159265 * frequency * (time + offset)));
                                float green = 0.5f * (1.0f + std::sin(2.0 * 3.14159265 * frequency * (time + offset) + 2.0 * 3.14159265 / 3.0));
                                float blue = 0.5f * (1.0f + std::sin(2.0 * 3.14159265 * frequency * (time + offset) + 4.0 * 3.14159265 / 3.0));
                                return IM_COL32(red * 255, green * 255, blue * 255, alpha);
                                };

                            vList->AddRectFilled(ImVec2(XBox, YBox), ImVec2(XBox + WBox, YBox + HBox), getRGBColor(0.2f, 80), rounding, ImDrawFlags_RoundCornersNone);
                        }
                        else {
                            ImVec4 currentColor = isVisible ? boxColorFilled : boxColorFilled;
                            ImU32 boxFillColor = IM_COL32(currentColor.x * 255, currentColor.y * 255, currentColor.z * 255, 50);
                            vList->AddRectFilled(ImVec2(XBox, YBox), ImVec2(XBox + WBox, YBox + HBox), boxFillColor, rounding, ImDrawFlags_RoundCornersNone);
                        }
                    }

                    if (ESPInfo && !IsTeam) {

                        std::string strEspNameScan = "[BOT]";

                        uint32_t nameAddr;
                        if (ReadZ(entity + Player_Name, nameAddr)) {
                            int nameLen;
                            if (ReadZ(nameAddr + 0x8, nameLen) && nameLen > 0 && nameLen < 256) {
                                strEspNameScan = ReadStringZ2(nameAddr + 0xC, nameLen);
                            }
                        }

                        ImVec2 nameTextSize(0.0f, 0.0f);
                        float nameFontHeight = 0.0f;

                        for (size_t i = 0; i < strEspNameScan.size(); ) {
                            unsigned int c = 0;
                            int char_len = ImTextCharFromUtf8(&c, strEspNameScan.c_str() + i, strEspNameScan.c_str() + strEspNameScan.size());
                            if (char_len == 0) break;

                            ImFont* font = ImGui::GetFont();

                            char utf8_char[5] = {};
                            memcpy(utf8_char, strEspNameScan.c_str() + i, char_len);
                            ImVec2 charSize = font->CalcTextSizeA(font->FontSize, FLT_MAX, 0.0f, utf8_char);

                            nameTextSize.x += charSize.x;
                            nameFontHeight = ImMax(nameFontHeight, charSize.y);

                            i += char_len;
                        }
                        nameTextSize.y = nameFontHeight;

                        std::string distStr = std::to_string(static_cast<int>(DistanceA)) + "m";
                        ImVec2 distTextSize(0.0f, 0.0f);
                        float distFontHeight = 0.0f;

                        for (size_t i = 0; i < distStr.size(); ) {
                            unsigned int c = 0;
                            int char_len = ImTextCharFromUtf8(&c, distStr.c_str() + i, distStr.c_str() + distStr.size());
                            if (char_len == 0) break;

                            ImFont* font = ImGui::GetFont();

                            char utf8_char[5] = {};
                            memcpy(utf8_char, distStr.c_str() + i, char_len);
                            ImVec2 charSize = font->CalcTextSizeA(font->FontSize, FLT_MAX, 0.0f, utf8_char);

                            distTextSize.x += charSize.x;
                            distFontHeight = ImMax(distFontHeight, charSize.y);

                            i += char_len;
                        }
                        distTextSize.y = distFontHeight;

                        float circleDiameter = 10.0f * 2;
                        float paddingLeft = 10.0f;
                        float paddingRight = 25.0f;
                        float distanceRightMargin = 5.0f;
                        float barHeight = 3.0f;
                        float bgHeight = 18.0f;

                        float barWidth = circleDiameter + paddingLeft + nameTextSize.x + paddingRight + distTextSize.x + distanceRightMargin;

                        float barX = PlayerPosHead.X - (barWidth / 2.0f);
                        float barY = PlayerPosHead.Y - bgHeight - 4.0f;

                        float healthPercentage = std::clamp(EspHealthInt / 200.0f, 0.0f, 1.0f);
                        float filledWidth = barWidth * healthPercentage;

                        ImVec4 colorSalud = ImLerp(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), ImVec4(healthcolor), healthPercentage);
                        ImU32 colorSaludU32 = ImGui::ColorConvertFloat4ToU32(colorSalud);

                        vList->AddRectFilled(ImVec2(barX, barY), ImVec2(barX + barWidth, barY + bgHeight), IM_COL32(0, 0, 0, 192), 2.0f);

                        vList->AddShadowRect(ImVec2(barX, barY), ImVec2(barX + filledWidth, barY + barHeight), colorSaludU32, 20.f, ImVec2(0, 0), 0, 5.f);
                        vList->AddRectFilled(ImVec2(barX, barY), ImVec2(barX + filledWidth, barY + barHeight), colorSaludU32, 2.0f);

                        ImVec2 circleCenter = ImVec2(barX + 9, barY + bgHeight / 2.0f + 1.5f);
                        vList->AddCircleFilled(circleCenter, (bgHeight - barHeight) / 2.0f - 2.0f, colorSaludU32);
                        vList->AddShadowCircle(circleCenter, (bgHeight - barHeight) / 2.0f - 2.0f, colorSaludU32, 30.f, ImVec2(0, 0), 0, 20);

                        float textX = barX + circleDiameter + paddingLeft;
                        float textY = barY + (bgHeight - nameTextSize.y) / 2.0f + 3.0f;
                        ImVec2 cursor(textX, textY - 2.0f);

                        for (size_t i = 0; i < strEspNameScan.size(); ) {
                            unsigned int c = 0;
                            int char_len = ImTextCharFromUtf8(&c, strEspNameScan.c_str() + i, strEspNameScan.c_str() + strEspNameScan.size());
                            if (char_len == 0) break;

                            ImFont* font = ImGui::GetFont();
                            ImGui::PushFont(font);

                            char utf8_char[5] = {};
                            memcpy(utf8_char, strEspNameScan.c_str() + i, char_len);

                            ImVec2 charSize = font->CalcTextSizeA(font->FontSize, FLT_MAX, 0.0f, utf8_char);
                            vList->AddText(cursor, ImGui::GetColorU32(nameColor), utf8_char);
                            cursor.x += charSize.x;

                            ImGui::PopFont();
                            i += char_len;
                        }

                        ImVec2 distancePosition(barX + barWidth - distTextSize.x - distanceRightMargin, barY + 3.0f);

                        if (RgbMode) {
                            double time = getCurrentTime();
                            float frequency = rgbSpeed;
                            auto getRGBColor = [&](float offset, int alpha) -> ImU32 {
                                float red = 0.5f * (1.0f + std::sin(2.0 * 3.14159265 * frequency * (time + offset)));
                                float green = 0.5f * (1.0f + std::sin(2.0 * 3.14159265 * frequency * (time + offset) + 2.0 * 3.14159265 / 3.0));
                                float blue = 0.5f * (1.0f + std::sin(2.0 * 3.14159265 * frequency * (time + offset) + 4.0 * 3.14159265 / 3.0));
                                return IM_COL32(static_cast<int>(red * 255), static_cast<int>(green * 255), static_cast<int>(blue * 255), alpha);
                                };

                            ImGui::PushFont(font::second_font);
                            vList->AddText(distancePosition, getRGBColor(0.3f, 255), distStr.c_str());
                            ImGui::PopFont();
                        }
                        else {
                            ImGui::PushFont(font::second_font);
                            vList->AddText(distancePosition, ImGui::GetColorU32(distanciaColor), distStr.c_str());
                            ImGui::PopFont();
                        }
                    }



                    vList->PopClipRect();
                }
            }





            ImGuiStyle* style = &ImGui::GetStyle();

            style->WindowPadding = ImVec2(0, 0);
            style->ItemSpacing = ImVec2(10, 10);
            style->WindowBorderSize = 0;
            style->ScrollbarSize = 8.f;

            static float color[4] = { 0 / 255.f, 255 / 255.f, 0 / 255.f, 1.f };
            c::accent = { color[0], color[1], color[2], 1.f };

            if (!hide)
            {
                if (authed == false)
                {

                    ImGui::SetNextWindowSize(c::background::size2);

                    ImGui::Begin("Login", nullptr, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoBringToFrontOnFocus | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoScrollbar);
                    {
                        LoadCredentials();
                        ParticlesV();

                        static std::vector<std::string> captions = {
                            "WELCOME TO PICO'S MUSEUM",
                            "BEST CHEATS IN THE MARKET",
                            "100% UNDETECTABLE",
                            "DEVELOPED BY PICO </>"

                        };

                        const ImVec2& pos = ImGui::GetWindowPos();
                        const ImVec2& region = ImGui::GetContentRegionMax();
                        const ImVec2& spacing = style->ItemSpacing;

                        main_window = ImGui::GetCurrentWindow();
                        auto draw = ImGui::GetWindowDrawList();
                        const auto& p = ImGui::GetWindowPos();

                        static bool login_page = true;
                        static float login_page_offset = 0.f;
                        static float animation_speed = 0.1f;

                        login_page_offset = ImLerp(login_page_offset, login_page ? 40.f : -100.f, animation_speed);

                        ImGui::GetBackgroundDrawList()->AddRectFilled(pos, pos + c::background::size2, ImGui::GetColorU32(c::tab::border, 0.95f), c::background::rounding);
                        ImGui::GetBackgroundDrawList()->AddRectFilled(pos, pos + c::background::size2, ImGui::GetColorU32(c::accent, 0.08f), c::background::rounding);


                        ImGui::PushFont(font::ContiB);
                        draw->AddText(pos + ImVec2(c::background::size2.x / 2 - ImGui::CalcTextSize("PICO'S MUSEUM").x / 2, 50), ImGui::ColorConvertFloat4ToU32(c::white), "PICO'S ");
                        draw->AddText(pos + ImVec2(c::background::size2.x / 2 - ImGui::CalcTextSize("PICO'S MUSEUM").x / 2 + ImGui::CalcTextSize("PICO'S ").x, 50), ImGui::GetColorU32(c::accent), "MUSEUM");
                        ImGui::PopFont();

                        AnimatedCaption(captions, pos + ImVec2(c::background::size2.x / 2, 80));

                        ImGui::SetCursorPos(ImVec2(login_page_offset, 130));

                        ImGui::BeginChild("Inputs", ImVec2(255, 150), 0);

                        ImGui::InputTextExNew("v", "Username", KeyAuth_USER_user_char, IM_ARRAYSIZE(KeyAuth_USER_user_char), ImVec2(c::background::size2.x - 80, 40), 0, 0, 0);
                        ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 5);
                        ImGui::InputTextExNew("x", "Password", KeyAuth_USER_pass_char, IM_ARRAYSIZE(KeyAuth_USER_pass_char), ImVec2(c::background::size2.x - 80, 40), 0, 0, 0);
                        ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 5);

                        if (ImGui::Button(btn_txt.c_str(), ImVec2(255, 40)))
                        {
                            btn_txt = "Logging in...";

                            authed = true;
                        }


                        ImGui::EndChild();

                        ImGui::ShadowText(skCrypt("No Account? Join Discord Server"), text_color[0], text_color[0], 40.f, ImVec2((c::background::size2.x - ImGui::CalcTextSize("No Account? Join Discord Server").x) / 2, c::background::size2.y - 130));

                        ImGui::SetCursorPos(ImVec2(c::background::size2.x / 2 - 27, c::background::size2.y - 50));

                        if (ImGui::TextButton("C", ImVec2(35, 35), 0));
                        if (ImGui::IsItemClicked()) ShellExecute(NULL, "open", "https://discord.gg/SXXN9nKxR9", NULL, NULL, SW_SHOW);


                        ImGui::End();

                    }
                }


                if (authed == true)
                {

                    ImGui::SetNextWindowSize(c::background::size);

                    ImGui::Begin("Menu", nullptr, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoBringToFrontOnFocus | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoScrollbar);
                    {
                        ParticlesV();

                        const ImVec2& pos = ImGui::GetWindowPos();
                        const ImVec2& region = ImGui::GetContentRegionMax();
                        const ImVec2& spacing = style->ItemSpacing;

                        main_window = ImGui::GetCurrentWindow();
                        auto draw = ImGui::GetWindowDrawList();
                        const auto& p = ImGui::GetWindowPos();

                        ImGui::GetBackgroundDrawList()->AddRectFilled(pos, pos + c::background::size, ImGui::GetColorU32(c::background::filling, 0.95f), c::background::rounding);

                        ImGui::GetBackgroundDrawList()->AddRect(pos, pos + c::background::size, ImGui::GetColorU32(c::background::stroke), c::background::rounding);

                        ImGui::PushClipRect(pos, pos + c::background::size, true);
                        ImGui::GetWindowDrawList()->AddShadowCircle(pos + ImVec2(30, -120), 180.f, ImColor(c::accent.x, c::accent.y, c::accent.z, 0.8f), 400.f, ImVec2(0, 0), 0, 30);
                        ImGui::PopClipRect();

                        ImGui::GetBackgroundDrawList()->AddRectFilled(pos, pos + ImVec2(190, c::background::size.y), ImGui::GetColorU32(c::background::filling, 0.7f), c::background::rounding, ImDrawFlags_RoundCornersLeft);

                        ImGui::GetBackgroundDrawList()->AddCircleFilled(pos + ImVec2(c::background::size.x - 50, 35), 25.f, ImGui::GetColorU32(c::elements::filled_circle, 0.8f), 0);

                        ImGui::GetBackgroundDrawList()->AddCircle(pos + ImVec2(c::background::size.x - 50, 35), 25.f, ImGui::GetColorU32(c::elements::circle, 1.f), 0);

                        ImGui::GetBackgroundDrawList()->AddShadowCircle(pos + ImVec2(c::background::size.x - 50, 35), 30.f, ImColor(c::accent.x, c::accent.y, c::accent.z, 0.8f), 50.f, ImVec2(0, 0), 0, 15);

                        ImGui::SetCursorPos(ImVec2(c::background::size.x - 74, 10));

                        ImGui::CustomButton(1, texture::logo, ImVec2(40, 40), ImVec2(0, 0), ImVec2(1, 1), ImGui::GetColorU32(c::accent));


                        std::string currentUsername(KeyAuth_USER_user_char);

                        std::string formattedExpiry;
                        if (!KeyAuth_expiry.empty() && std::all_of(KeyAuth_expiry.begin(), KeyAuth_expiry.end(), ::isdigit)) {
                            formattedExpiry = FormatExpiryDate(KeyAuth_expiry);
                        }
                        else {
                            formattedExpiry = "Unknown";
                        }


                        std::string user = "Username: " + currentUsername;
                        std::string expdate = "Expiry: " + formattedExpiry;

                        ImGui::PushFont(font::lexend_bold);


                        auto userWidth = ImGui::CalcTextSize(user.c_str()).x;
                        auto expWidth = ImGui::CalcTextSize(expdate.c_str()).x;


                        ImGui::SetCursorPos(ImVec2(c::background::size.x - 100 - userWidth, 15));
                        ImGui::TextColored(c::white, "%s", user.c_str());

                        ImGui::SetCursorPos(ImVec2(c::background::size.x - 100 - expWidth, 35));
                        ImGui::TextColored(c::white - ImVec4(0.f, 0.f, 0.f, 0.6f), "%s", expdate.c_str());

                        ImGui::PopFont();


                        ImGui::PushFont(font::ContiM);

                        const char* title = "P I C O";
                        ImVec2 titlesz = ImGui::CalcTextSize(title);

                        draw->AddText(pos + ImVec2(190 / 2 - titlesz.x / 2, 10), ImGui::GetColorU32(ImVec4(1.f, 1.f, 1.f, 0.5f)), title, 0);

                        ImGui::PopFont();

                        ImGui::GradientSeparator(pos + ImVec2(10, ImGui::GetCursorPosY() + titlesz.y - 45), 170.f, 2.f);



                        ImGui::SetCursorPos(ImVec2(15, 80));

                        ImGui::BeginGroup();

                        if (edited::Tab(page == 0, "a", "Aimbot", ImVec2(170, 38))) {
                            page = 0;
                        }

                        if (edited::Tab(page == 1, "w", "ESP", ImVec2(170, 38))) {
                            page = 1;
                        }

                        if (edited::Tab(page == 2, "c", "Fake Lag", ImVec2(170, 38))) {
                            page = 2;
                        }


                        ImGui::GradientSeparator(pos + ImVec2(10, ImGui::GetCursorPosY()), 170.f, 2.f);


                        if (edited::Tab(page == 3, "d", "Key Binds", ImVec2(170, 38))) {
                            page = 3;

                        }


                        ImGui::GradientSeparator(pos + ImVec2(10, ImGui::GetCursorPosY()), 170.f, 2.f);


                        if (edited::Tab(page == 4, "e", "Settings", ImVec2(170, 38))) {
                            page = 4;
                        }

                        ImGui::EndGroup();

                        tab_alpha = ImLerp(tab_alpha, (page == active_tab) ? 1.f : 0.f, 15.f * ImGui::GetIO().DeltaTime);
                        if (tab_alpha < 0.01f && tab_add < 0.01f) active_tab = page;

                        ImGui::SetCursorPos(ImVec2(200, 70));

                        ImGui::PushStyleVar(ImGuiStyleVar_Alpha, tab_alpha * style->Alpha);
                        {

                            if (active_tab == 0)
                            {

                                edited::BeginChild("a", "Aim Functions", ImVec2((c::background::size.x - 220) / 2, c::background::size.y - 120), ImGuiChildFlags_None);
                                {

                                    // Enhanced Aimbot Section
                                    if (edited::Checkbox("Silent Aimbot [ STEALTH ]", "Most Powerful - Invisible", &AimbotSilent)) {
                                        if (AimbotSilent) {
                                            AimbotVisible = false;
                                            AimbotAI = false;
                                        }
                                    }

                                    if (edited::Checkbox("Visible Aimbot [ POWER ]", "Fast & Reliable", &AimbotVisible)) {
                                        if (AimbotVisible) {
                                            AimbotSilent = false;
                                            AimbotAI = false;
                                        }
                                    }

                                    ImGui::Separator();
                                    ImGui::Text("Enhanced Aimbot Features:");
                                    ImGui::BulletText("Silent: Invisible & Powerful");
                                    ImGui::BulletText("Visible: Fast & Reliable");
                                    ImGui::BulletText("Works with all key bindings");

                                    ImGui::Separator();
                                    ImGui::Text("Teleport Features (NATCHO Style):");

                                    if (edited::Checkbox("TeleKill [ INSTANT ]", "Teleport to closest enemy", &TeleKill)) {
                                        if (TeleKill) {
                                            FlyMe = false; // Disable fly when teleporting
                                        }
                                    }

                                    if (edited::Checkbox("Fly Mode [ HEIGHT ]", "Fly at custom height", &FlyMe)) {
                                        if (FlyMe) {
                                            TeleKill = false; // Disable teleport when flying
                                        }
                                    }

                                    if (FlyMe) {
                                        ImGui::SliderFloat("Fly Height", &FlyHeight, 10.0f, 200.0f, "%.1f");
                                    }

                                    edited::Checkbox("Up Player [ LIFT ]", "Lift enemies up", &UpPlayer);

                                    // Legacy Aimbot (kept for compatibility)
                                    static bool aimbot_h = false;
                                    if (edited::Checkbox("Aimbot In-Game [ Head ]", "Legacy - Basic", &aimbot_h)) {



                                    }

                                    static bool aimbot_n = false;
                                    if (edited::Checkbox("Aimbot In-Game [ Neck ]", "Legacy - Basic", &aimbot_n)) {



                                    }

                                    static bool aimbot_d = false;
                                    if (edited::Checkbox("Aimbot In-Game [ Drag ]", "Legacy - Basic", &aimbot_d)) {



                                    }

                                    static int selected = 0;
                                    static int previousSelect = -1;
                                    const char* items[3]{ "Rage", "AI", "Legit" };

                                    ImGui::Combo("Aimbot Type", &selected, items, IM_ARRAYSIZE(items), 3);
                                    if (selected != previousSelect) {

                                        if (selected == 0) {

                                            AimbotAI = false;

                                        }
                                        else if (selected == 1 && aimbot_h || aimbot_n || aimbot_d == true) {

                                            AimbotAI = true;

                                        }
                                        else if (selected == 2) {

                                            AimbotAI = false;

                                        }
                                        previousSelect = selected;
                                    }

                                    edited::Checkbox("FOV Circle", "", &ShowFovCircle);
                                    ImGui::SliderFloat("FOV Radius", &fovRadius, 10.0f, 350.0f, "%.1f");
                                    ImGui::SliderFloat("FOV Thickness", &fovThickness, 1.f, 50.f, "%.0f");
                                    ImGui::SliderFloat("RGB Speed", &rgbSpeed, 0.1f, 10.0f, "%.1f");
                                    ImGui::SliderFloat("ESP Distance", &maxDistance, 150.f, 350.f, "%.0f");

                                }
                                edited::EndChild();

                                ImGui::SameLine(0, 10);
                                ImGui::SetCursorPosY(ImGui::GetCursorPosY() - 40);

                                edited::BeginChild("f", "Aim Scope", ImVec2((c::background::size.x - 220) / 2, 130), NULL);
                                {


                                    static bool aimbot_2x = false;
                                    if (edited::Checkbox("Aimbot Scope [ 2X ]", "Works In-Game", &aimbot_2x)) {



                                    }

                                    static bool aimbot_4x = false;
                                    if (edited::Checkbox("Aimbot Scope [ 4X ]", "Works In-Game", &aimbot_4x)) {



                                    }

                                }
                                edited::EndChild();

                                ImGui::SetCursorPos(ImVec2(530, 250));

                                edited::BeginChild("m", "Advanced Aim Functions", ImVec2((c::background::size.x - 220) / 2, 200), NULL);
                                {


                                    static bool aimbot_2xt = false;
                                    if (edited::Checkbox("Aimbot Scope Tracking [ 2X ]", "Works In-Game", &aimbot_2xt)) {



                                    }

                                    static bool aimbot_4xt = false;
                                    if (edited::Checkbox("Aimbot Scope Tracking [ 4X ]", "Works In-Game", &aimbot_4xt)) {



                                    }

                                    static bool aim_fov = false;
                                    if (edited::Checkbox("Aim FOV", "Works In-Game", &aim_fov)) {



                                    }
                                }
                                edited::EndChild();


                            }
                            else if (active_tab == 1)
                            {
                                edited::BeginChild("b", "ESP Functions", ImVec2((c::background::size.x - 220) / 2, 380), NULL);
                                {

                                    static bool setup = false;
                                    if (edited::Checkbox("ADB Setup", "Complete Setting up ADB", &setup)) {

                                        if (setup)
                                        {
                                            std::thread taskThread([]()
                                                {



                                                    hdPlayerWindow = InjectADB();

                                                });
                                            taskThread.detach();
                                        }
                                        else
                                        {
                                            setup_done = false;
                                        }

                                    }

                                    if (setup && setup_done)
                                    {
                                        edited::Checkbox("ESP Line", "Drow Line And Locate The Enemy", &ESPLine);



                                        edited::Checkbox("ESP Box", "Draw Boxes On The Enemy", &ESPBoxV1);



                                        edited::Checkbox("ESP Cornered Box", "Draw Cornered Boxes On The Enemy", &ESPBoxV2);



                                        edited::Checkbox("ESP Information", "Show Name,Health,Distance", &ESPInfo);



                                        edited::Checkbox("ESP Fill Box", "Draw Fill Boxes On The Enemy", &ESPBoxFILLED);



                                        edited::Checkbox("ESP Bones", "Draw Bones On The Enemy", &ESPBones);

                                    }


                                }
                                edited::EndChild();

                                ImGui::SameLine(0, 10);
                                ImGui::SetCursorPosY(ImGui::GetCursorPosY() - 40);

                                edited::BeginChild("g", "ESP Settings", ImVec2((c::background::size.x - 220) / 2, 380), NULL);
                                {


                                    const char* items[3]{ "Top", "Centre", "Bottom" };
                                    ImGui::Combo("ESP Anchor Point", &lineType, items, IM_ARRAYSIZE(items), 3);

                                    const char* items1[3]{ "Line", "Curved", "Stairs" };
                                    ImGui::Combo("ESP Line Type", &lineType1, items1, IM_ARRAYSIZE(items1), 3);

                                    ImGui::SliderFloat("Update ESP Time", &clearesp, 0.0f, 20.0f, "%.1f");

                                    edited::Checkbox("ESP RGB", "ESP RGB Colors", &RgbMode);

                                    edited::ColorEdit4("ESP Line Color", "", (float*)&lineColorEnemy, picker_flags);
                                    edited::ColorEdit4("ESP Box Color", "", (float*)&boxColornormalv1, picker_flags);
                                    edited::ColorEdit4("ESP Cornered Box Color", "", (float*)&boxColornormalv2, picker_flags);
                                    edited::ColorEdit4("ESP Filled Box Color", "", (float*)&boxColorFilled, picker_flags);
                                    edited::ColorEdit4("ESP Name Color", "", (float*)&nameColor, picker_flags);
                                    edited::ColorEdit4("ESP Distance Color", "", (float*)&distanciaColor, picker_flags);
                                    edited::ColorEdit4("ESP Bones Color", "", (float*)&bonesColor, picker_flags);

                                }
                                edited::EndChild();

                            }
                            else if (active_tab == 2)
                            {
                                edited::BeginChild("c", "Fake Lag", ImVec2((c::background::size.x - 220) / 2, 190), NULL);
                                {

                                    if (edited::Checkbox("Fake Lag - Enable", "Active Anywhere", &fake_lag))
                                    {
                                        notificationSystem.AddNotification("Notification", "Fake Lag On", ImGui::GetColorU32(c::accent));


                                    }


                                    ImGui::SliderFloat("Delay", &fakelag2, 0.0f, 10.0f, "%0.01fS");
                                    static int select1 = 0;
                                    static int previousSelect4 = -1;
                                    const char* items1[2]{ "Automatic", "Manual" };
                                    ImGui::Combo("Mode", &select1, items1, IM_ARRAYSIZE(items1), 2);


                                    if (select1 != previousSelect4) {

                                        if (select1 == 0) {



                                        }
                                        else if (select1 == 1) {



                                        }

                                        previousSelect4 = select1;
                                    }

                                }
                                edited::EndChild();

                                ImGui::SameLine(0, 10);
                                ImGui::SetCursorPosY(ImGui::GetCursorPosY() - 40);

                                edited::BeginChild("d", "Fake Lag - Key Bind", ImVec2((c::background::size.x - 220) / 2, 70), NULL);
                                {

                                    edited::Keybind("Fake Lag - Key", "Assign Key For Fake Lag", &fakelag_key);


                                }
                                edited::EndChild();
                            }
                            else if (active_tab == 3)
                            {

                                edited::BeginChild("d", "Aimbot Key Binds", ImVec2((c::background::size.x - 220) / 2, 190), NULL);
                                {

                                    edited::Keybind("Aimbot Head - Key", "Assign Key For Aimbot Head", &aim_head);


                                    edited::Keybind("Aimbot Neck - Key", "Assign Key For Aimbot Neck", &aim_neck);


                                    edited::Keybind("Aimbot Drag - Key", "Assign Key For Aimbot Drag", &aim_drag);


                                }
                                edited::EndChild();


                                ImGui::SameLine(0, 10);
                                ImGui::SetCursorPosY(ImGui::GetCursorPosY() - 40);

                                edited::BeginChild("i", "Streamer Key Binds", ImVec2((c::background::size.x - 220) / 2, 130), NULL);
                                {

                                    edited::Keybind("Streamer Mode - Key", "Assign Key For Streamer Mode", &streamer_mode);

                                    ImGui::SetCursorPos(ImVec2(63.5, ImGui::GetCursorPosY() + 25 - ImGui::CalcTextSize("P").y / 2));
                                    ImGui::PushFont(font::tab_font);
                                    ImGui::TextColored(c::accent, "Press ");
                                    ImGui::PopFont();

                                    draw->AddRectFilled(pos + ImVec2(637.5, 197), pos + ImVec2(637.5 + ImGui::CalcTextSize("INS").x + 10, 215), ImGui::GetColorU32(c::accent, 0.4f), 3.f);

                                    ImGui::SetCursorPos(ImVec2(151.5, 85));
                                    ImGui::PushFont(font::tab_font);
                                    ImGui::TextColored(c::accent, "to Hide the Menu");
                                    ImGui::PopFont();

                                    ImGui::SetCursorPos(ImVec2(113, 85));
                                    ImGui::TextColored(ImVec4(1.f, 1.f, 1.f, 0.5f), "INS");

                                }
                                edited::EndChild();



                            }
                            else if (active_tab == 4)
                            {

                                edited::BeginChild("g", "Aimbot Status", ImVec2((c::background::size.x - 220) / 2, 380), NULL);
                                {
                                    ImGui::Text("Enhanced Aimbot System");
                                    ImGui::Separator();

                                    ImGui::Text("Current Status:");
                                    if (AimbotSilent) ImGui::TextColored(ImVec4(0, 1, 0, 1), "Silent Aimbot: ACTIVE");
                                    else if (AimbotVisible) ImGui::TextColored(ImVec4(1, 1, 0, 1), "Visible Aimbot: ACTIVE");
                                    else if (AimbotAI) ImGui::TextColored(ImVec4(0, 0, 1, 1), "Legacy Aimbot: ACTIVE");
                                    else ImGui::TextColored(ImVec4(1, 0, 0, 1), "All Aimbots: DISABLED");

                                    ImGui::Separator();
                                    ImGui::Text("Features:");
                                    ImGui::BulletText("Silent Aimbot: Invisible & Powerful");
                                    ImGui::BulletText("Visible Aimbot: Fast & Reliable");
                                    ImGui::BulletText("Legacy Aimbot: Basic & Stable");

                                    ImGui::Separator();
                                    ImGui::Text("Tips:");
                                    ImGui::BulletText("Use Silent for stealth gameplay");
                                    ImGui::BulletText("Use Visible for maximum power");
                                    ImGui::BulletText("Adjust FOV in Aimbot settings");

                                    ImGui::Separator();
                                    ImGui::Text("Teleport Status:");
                                    if (TeleKill) ImGui::TextColored(ImVec4(1, 0, 0, 1), "TeleKill: ACTIVE");
                                    if (FlyMe) ImGui::TextColored(ImVec4(0, 1, 1, 1), "Fly Mode: ACTIVE");
                                    if (UpPlayer) ImGui::TextColored(ImVec4(1, 1, 0, 1), "Up Player: ACTIVE");
                                    if (!TeleKill && !FlyMe && !UpPlayer)
                                        ImGui::TextColored(ImVec4(0.5f, 0.5f, 0.5f, 1), "All Teleports: DISABLED");


                                    if (edited::Checkbox("Streamer Mode", "Hides panel and cheats in stream", &streammode))
                                    {

                                        stream = !stream;
                                        if (stream) {
                                            notificationSystem.AddNotification("Done", "Stream Mode Enabled!", ImGui::GetColorU32(c::accent));

                                            SetWindowDisplayAffinity(GetActiveWindow(), WDA_EXCLUDEFROMCAPTURE);
                                            ITaskbarList* pTaskList = NULL;
                                            HRESULT initRet = CoInitialize(NULL);
                                            HRESULT createRet = CoCreateInstance(CLSID_TaskbarList,
                                                NULL,
                                                CLSCTX_INPROC_SERVER,
                                                IID_ITaskbarList,
                                                (LPVOID*)&pTaskList);

                                            if (createRet == S_OK)
                                            {

                                                pTaskList->DeleteTab(GetActiveWindow());

                                                pTaskList->Release();
                                            }

                                            CoUninitialize();


                                        }
                                        else {
                                            notificationSystem.AddNotification("Done", "Stream Mode Disabled!", ImGui::GetColorU32(c::accent));

                                            SetWindowDisplayAffinity(GetActiveWindow(), WDA_NONE);
                                            ITaskbarList* pTaskList = NULL;
                                            HRESULT initRet = CoInitialize(NULL);
                                            HRESULT createRet = CoCreateInstance(CLSID_TaskbarList,
                                                NULL,
                                                CLSCTX_INPROC_SERVER,
                                                IID_ITaskbarList,
                                                (LPVOID*)&pTaskList);

                                            if (createRet == S_OK)
                                            {
                                                pTaskList->AddTab(GetActiveWindow());

                                                pTaskList->Release();
                                            }


                                        }

                                    }

                                    ImGui::Spacing();
                                }
                                edited::EndChild();

                                ImGui::SameLine(0, 10);
                                ImGui::SetCursorPosY(ImGui::GetCursorPosY() - 40);

                                edited::BeginChild("p", "Design", ImVec2((c::background::size.x - 220) / 2, 380), NULL);
                                {

                                    edited::ColorEdit4("Main Color", "Set the main color of the menu.", color, picker_flags);


                                }
                                edited::EndChild();

                            }

                            ImGui::PopStyleVar();

                        }

                        ImGui::End();
                    }

                }

            }
            notificationSystem.DrawNotifications();
        }


        ImGui:: Render();

        const float clear_color_with_alpha[4] = { 0.f };

        g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, nullptr);
        g_pd3dDeviceContext->ClearRenderTargetView(g_mainRenderTargetView, clear_color_with_alpha);
        ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());

        g_pSwapChain->Present(1, 0);
    }

   
    ImGui_ImplDX11_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();

    CleanupDeviceD3D();
    ::DestroyWindow(hwnd);
    ::UnregisterClassW(wc.lpszClassName, wc.hInstance);

    return;
}

HMODULE hCurrentModule = nullptr;
HANDLE hCurrentUIThread = nullptr;
HANDLE hCurrentBgThread = nullptr;

#ifdef _WINDLL


BOOL WINAPI DllMain(HINSTANCE hinstDLL, DWORD fdwReason, LPVOID lpReserved, HMODULE hMod, DWORD reason)
{
    if (fdwReason == DLL_PROCESS_ATTACH)
    {
        DisableThreadLibraryCalls(hinstDLL);
        hCurrentModule = hinstDLL;
        hCurrentUIThread = CreateThread(nullptr, NULL, (LPTHREAD_START_ROUTINE)PicoMain, nullptr, NULL, nullptr);

    }

    if (fdwReason == DLL_PROCESS_DETACH)
        TerminateThread(hCurrentUIThread, 0);

    return TRUE;
}

#else

int APIENTRY WinMain(HINSTANCE, HINSTANCE, LPSTR, int)
{

    PicoMain();

    return 0;
}

#endif
