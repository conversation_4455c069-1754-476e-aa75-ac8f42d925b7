﻿C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.CppBuild.targets(524,5): warning MSB8004: Output Directory does not end with a trailing slash.  This build instance will add the slash as it is required to allow proper evaluation of the Output Directory.
  main.cpp
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_settings.h(149,13): warning C4189: 'screenWidth': local variable is initialized but not referenced
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_settings.h(168,20): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_settings.h(196,41): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(12,9): warning C4005: 'DXGI_STATUS_OCCLUDED': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(58984,9):
      see previous definition of 'DXGI_STATUS_OCCLUDED'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(13,9): warning C4005: 'DXGI_STATUS_CLIPPED': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(58993,9):
      see previous definition of 'DXGI_STATUS_CLIPPED'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(14,9): warning C4005: 'DXGI_STATUS_NO_REDIRECTION': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59002,9):
      see previous definition of 'DXGI_STATUS_NO_REDIRECTION'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(15,9): warning C4005: 'DXGI_STATUS_NO_DESKTOP_ACCESS': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59011,9):
      see previous definition of 'DXGI_STATUS_NO_DESKTOP_ACCESS'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(16,9): warning C4005: 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59020,9):
      see previous definition of 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(17,9): warning C4005: 'DXGI_STATUS_MODE_CHANGED': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59029,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGED'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(18,9): warning C4005: 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59038,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(21,9): warning C4005: 'DXGI_ERROR_INVALID_CALL': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59053,9):
      see previous definition of 'DXGI_ERROR_INVALID_CALL'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(22,9): warning C4005: 'DXGI_ERROR_NOT_FOUND': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59062,9):
      see previous definition of 'DXGI_ERROR_NOT_FOUND'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(23,9): warning C4005: 'DXGI_ERROR_MORE_DATA': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59071,9):
      see previous definition of 'DXGI_ERROR_MORE_DATA'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(24,9): warning C4005: 'DXGI_ERROR_UNSUPPORTED': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59080,9):
      see previous definition of 'DXGI_ERROR_UNSUPPORTED'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(25,9): warning C4005: 'DXGI_ERROR_DEVICE_REMOVED': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59089,9):
      see previous definition of 'DXGI_ERROR_DEVICE_REMOVED'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(26,9): warning C4005: 'DXGI_ERROR_DEVICE_HUNG': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59098,9):
      see previous definition of 'DXGI_ERROR_DEVICE_HUNG'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(27,9): warning C4005: 'DXGI_ERROR_DEVICE_RESET': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59108,9):
      see previous definition of 'DXGI_ERROR_DEVICE_RESET'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(28,9): warning C4005: 'DXGI_ERROR_WAS_STILL_DRAWING': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59117,9):
      see previous definition of 'DXGI_ERROR_WAS_STILL_DRAWING'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(29,9): warning C4005: 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59127,9):
      see previous definition of 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(30,9): warning C4005: 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59136,9):
      see previous definition of 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(31,9): warning C4005: 'DXGI_ERROR_DRIVER_INTERNAL_ERROR': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59146,9):
      see previous definition of 'DXGI_ERROR_DRIVER_INTERNAL_ERROR'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(32,9): warning C4005: 'DXGI_ERROR_NONEXCLUSIVE': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59155,9):
      see previous definition of 'DXGI_ERROR_NONEXCLUSIVE'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(33,9): warning C4005: 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59164,9):
      see previous definition of 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(34,9): warning C4005: 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59174,9):
      see previous definition of 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(35,9): warning C4005: 'DXGI_ERROR_REMOTE_OUTOFMEMORY': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59183,9):
      see previous definition of 'DXGI_ERROR_REMOTE_OUTOFMEMORY'
  
C:\Users\<USER>\Desktop\SDK\Include\d3d11.h(917,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59498,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
C:\Users\<USER>\Desktop\SDK\Include\d3d11.h(918,9): warning C4005: 'D3D11_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59507,9):
      see previous definition of 'D3D11_ERROR_FILE_NOT_FOUND'
  
C:\Users\<USER>\Desktop\SDK\Include\d3d11.h(919,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59517,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS'
  
C:\Users\<USER>\Desktop\SDK\Include\d3d11.h(920,9): warning C4005: 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59526,9):
      see previous definition of 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD'
  
C:\Users\<USER>\Desktop\SDK\Include\d3d10.h(608,9): warning C4005: 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59474,9):
      see previous definition of 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
C:\Users\<USER>\Desktop\SDK\Include\d3d10.h(609,9): warning C4005: 'D3D10_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59483,9):
      see previous definition of 'D3D10_ERROR_FILE_NOT_FOUND'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(1,1): warning C4828: The file contains a character starting at offset 0x407 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Unity.hh(1,1): warning C4828: The file contains a character starting at offset 0x3d1 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Unity.hh(1,1): warning C4828: The file contains a character starting at offset 0x701 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Unity.hh(1,1): warning C4828: The file contains a character starting at offset 0x1af9 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Unity.hh(1,1): warning C4828: The file contains a character starting at offset 0x1b11 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector2.hpp(12,9): warning C4201: nonstandard extension used: nameless struct/union
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector2.hpp(297,13): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector2.hpp(298,13): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector2.hpp(451,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector2.hpp(452,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0x58c that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0x59b that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0x5f0 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0x6d9 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0x70e that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0x9c2 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0x9c9 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0xa17 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0xa2f that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0xa8a that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0xabe that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(13,9): warning C4201: nonstandard extension used: nameless struct/union
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(341,13): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(342,13): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(494,72): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(494,66): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(494,60): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(522,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(523,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(540,13): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(541,13): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(596,21): warning C4312: 'type cast': conversion from 'int' to 'const char *' of greater size
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(597,21): warning C4312: 'type cast': conversion from 'int' to 'const char *' of greater size
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(598,21): warning C4312: 'type cast': conversion from 'int' to 'const char *' of greater size
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(94,9): warning C4201: nonstandard extension used: nameless struct/union
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(343,37): warning C4244: 'return': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(375,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(376,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(377,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(378,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(379,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(380,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(450,45): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(451,21): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(458,68): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(459,21): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(466,68): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(467,21): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(474,68): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(475,21): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(502,27): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(503,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(577,15): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(585,15): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(693,36): warning C4244: 'argument': conversion from 'double' to 'const float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\ADBZapi.hpp(72,27): warning C4127: conditional expression is constant
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\ADBZapi.hpp(72,27):
      consider using 'if constexpr' statement instead
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\ADBZapi.hpp(127,66): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\ADBZapi.hpp(198,66): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(6,8): error C2011: 'Quaternion': 'struct' type redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(25,23): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(28,16): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(32,18): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(33,25): error C2664: 'Vector3 MathUtils::QuaternionToEulerAngles(Quaternion)': cannot convert argument 1 from 'Quaternion' to 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(33,49):
      use of undefined type 'Quaternion'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(38,20):
      see declaration of 'MathUtils::QuaternionToEulerAngles'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(33,25):
      while trying to match the argument list '(Quaternion)'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(38,20): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(39,33): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(39,39): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(39,45): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(39,51): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(40,32): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(40,38): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(40,44): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(40,50): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(40,65): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(40,71): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(40,77): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(40,83): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(40,21): error C2661: 'atan2': no overloaded function takes 1 arguments
  (compiling source file 'main.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cmath(836,1):
      could be 'conditional<std::is_same_v<_Ty1,long double>||std::is_same_v<_Ty2,long double>,long double,conditional<std::is_same_v<_Ty1,float>&&std::is_same_v<_Ty2,float>,float,double>::type>::type atan2(_Ty1,_Ty2) noexcept'
          C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(40,21):
          'conditional<std::is_same_v<_Ty1,long double>||std::is_same_v<_Ty2,long double>,long double,conditional<std::is_same_v<_Ty1,float>&&std::is_same_v<_Ty2,float>,float,double>::type>::type atan2(_Ty1,_Ty2) noexcept': expects 2 arguments - 1 provided
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(40,21):
      while trying to match the argument list '()'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(40,19): warning C4244: 'initializing': conversion from '_T' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(40,19): warning C4244:         with
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(40,19): warning C4244:         [
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(40,19): warning C4244:             _T=double
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(40,19): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(41,33): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(41,39): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(41,45): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(41,51): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(41,66): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(41,72): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(41,78): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(41,84): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(41,22): error C2661: 'atan2': no overloaded function takes 1 arguments
  (compiling source file 'main.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cmath(836,1):
      could be 'conditional<std::is_same_v<_Ty1,long double>||std::is_same_v<_Ty2,long double>,long double,conditional<std::is_same_v<_Ty1,float>&&std::is_same_v<_Ty2,float>,float,double>::type>::type atan2(_Ty1,_Ty2) noexcept'
          C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(41,22):
          'conditional<std::is_same_v<_Ty1,long double>||std::is_same_v<_Ty2,long double>,long double,conditional<std::is_same_v<_Ty1,float>&&std::is_same_v<_Ty2,float>,float,double>::type>::type atan2(_Ty1,_Ty2) noexcept': expects 2 arguments - 1 provided
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(41,22):
      while trying to match the argument list '()'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(41,20): warning C4244: 'initializing': conversion from '_T' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(41,20): warning C4244:         with
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(41,20): warning C4244:         [
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(41,20): warning C4244:             _T=double
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(41,20): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(46,23): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(51,32): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(51,32): error C3861: 'Identity': identifier not found
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(54,20): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(59,20): error C2079: 'quaternion' uses undefined struct 'Quaternion'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(50,9): warning C4390: ';': empty controlled statement found; is this the intent?
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(53,9): warning C4390: ';': empty controlled statement found; is this the intent?
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(136,23): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(142,30): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(146,47): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(153,23): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(154,27): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(154,40): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(154,53): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(154,66): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(155,26): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(155,39): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(155,52): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(155,65): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(156,27): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(156,46): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(157,26): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(157,45): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\MathUtils.hpp(156,26): error C2027: use of undefined type 'Quaternion'
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(89,8):
      see declaration of 'Quaternion'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(1,1): warning C4828: The file contains a character starting at offset 0x407 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(1,11): error C2374: 'Il2Cpp': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(1,11):
      see declaration of 'Il2Cpp'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(1,11): error C2086: 'uintptr_t Il2Cpp': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(1,11):
      see declaration of 'Il2Cpp'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(2,11): error C2374: 'InitBase': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(2,11):
      see declaration of 'InitBase'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(2,11): error C2086: 'uintptr_t InitBase': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(2,11):
      see declaration of 'InitBase'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(3,11): error C2374: 'StaticClass': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(3,11):
      see declaration of 'StaticClass'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(3,11): error C2086: 'uintptr_t StaticClass': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(3,11):
      see declaration of 'StaticClass'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(4,11): error C2374: 'MatchStatus': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(4,11):
      see declaration of 'MatchStatus'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(4,11): error C2086: 'uintptr_t MatchStatus': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(4,11):
      see declaration of 'MatchStatus'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(5,11): error C2374: 'LocalPlayer': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(5,11):
      see declaration of 'LocalPlayer'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(5,11): error C2086: 'uintptr_t LocalPlayer': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(5,11):
      see declaration of 'LocalPlayer'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(6,11): error C2374: 'DictionaryEntities': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(6,11):
      see declaration of 'DictionaryEntities'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(6,11): error C2086: 'uintptr_t DictionaryEntities': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(6,11):
      see declaration of 'DictionaryEntities'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(9,11): error C2374: 'Player_IsDead': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(9,11):
      see declaration of 'Player_IsDead'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(9,11): error C2086: 'uintptr_t Player_IsDead': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(9,11):
      see declaration of 'Player_IsDead'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(10,11): error C2374: 'Player_Name': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(10,11):
      see declaration of 'Player_Name'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(10,11): error C2086: 'uintptr_t Player_Name': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(10,11):
      see declaration of 'Player_Name'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(11,11): error C2374: 'Player_Data': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(11,11):
      see declaration of 'Player_Data'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(11,11): error C2086: 'uintptr_t Player_Data': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(11,11):
      see declaration of 'Player_Data'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(12,11): error C2374: 'Player_ShadowBase': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(12,11):
      see declaration of 'Player_ShadowBase'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(12,11): error C2086: 'uintptr_t Player_ShadowBase': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(12,11):
      see declaration of 'Player_ShadowBase'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(13,11): error C2374: 'XPose': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(13,11):
      see declaration of 'XPose'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(13,11): error C2086: 'uintptr_t XPose': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(13,11):
      see declaration of 'XPose'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(14,11): error C2374: 'AvatarManager': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(14,11):
      see declaration of 'AvatarManager'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(14,11): error C2086: 'uintptr_t AvatarManager': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(14,11):
      see declaration of 'AvatarManager'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(15,11): error C2374: 'Avatar': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(15,11):
      see declaration of 'Avatar'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(15,11): error C2086: 'uintptr_t Avatar': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(15,11):
      see declaration of 'Avatar'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(16,11): error C2374: 'Avatar_IsVisible': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(16,11):
      see declaration of 'Avatar_IsVisible'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(16,11): error C2086: 'uintptr_t Avatar_IsVisible': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(16,11):
      see declaration of 'Avatar_IsVisible'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(17,11): error C2374: 'Avatar_Data': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(17,11):
      see declaration of 'Avatar_Data'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(17,11): error C2086: 'uintptr_t Avatar_Data': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(17,11):
      see declaration of 'Avatar_Data'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(18,11): error C2374: 'Avatar_Data_IsTeam': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(18,11):
      see declaration of 'Avatar_Data_IsTeam'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(18,11): error C2086: 'uintptr_t Avatar_Data_IsTeam': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(18,11):
      see declaration of 'Avatar_Data_IsTeam'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(19,11): error C2374: 'CurrentMatch': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(19,11):
      see declaration of 'CurrentMatch'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(19,11): error C2086: 'uintptr_t CurrentMatch': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(19,11):
      see declaration of 'CurrentMatch'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(21,11): error C2374: 'HeadCollider': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(21,11):
      see declaration of 'HeadCollider'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(21,11): error C2086: 'uintptr_t HeadCollider': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(21,11):
      see declaration of 'HeadCollider'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(24,11): error C2374: 'FollowCamera': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(24,11):
      see declaration of 'FollowCamera'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(24,11): error C2086: 'uintptr_t FollowCamera': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(24,11):
      see declaration of 'FollowCamera'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(25,11): error C2374: 'Camera': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(25,11):
      see declaration of 'Camera'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(25,11): error C2086: 'uintptr_t Camera': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(25,11):
      see declaration of 'Camera'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(26,11): error C2374: 'AimRotation': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(26,11):
      see declaration of 'AimRotation'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(26,11): error C2086: 'uintptr_t AimRotation': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(26,11):
      see declaration of 'AimRotation'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(27,11): error C2374: 'MainCameraTransform': redefinition; multiple initialization
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(27,11):
      see declaration of 'MainCameraTransform'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(27,11): error C2086: 'uintptr_t MainCameraTransform': redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(27,11):
      see declaration of 'MainCameraTransform'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(27,11): error C1003: error count exceeds 100; stopping compilation
  (compiling source file 'main.cpp')
  
