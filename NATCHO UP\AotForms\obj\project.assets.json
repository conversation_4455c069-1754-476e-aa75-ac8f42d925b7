{"version": 3, "targets": {"net7.0-windows7.0": {"ClickableTransparentOverlay/9.3.0": {"type": "package", "dependencies": {"ImGui.NET": "1.90.1.1", "SixLabors.ImageSharp": "3.1.2", "Vortice.D3DCompiler": "3.3.4", "Vortice.Direct3D11": "3.3.4"}, "compile": {"lib/net7.0/ClickableTransparentOverlay.dll": {}}, "runtime": {"lib/net7.0/ClickableTransparentOverlay.dll": {}}}, "DNNE/2.0.6": {"type": "package", "build": {"build/DNNE.props": {}, "build/DNNE.targets": {}}}, "GlobalHotKey/1.1.0": {"type": "package", "compile": {"lib/GlobalHotKey.dll": {}}, "runtime": {"lib/GlobalHotKey.dll": {}}}, "Guna.UI2.WinForms/2.0.4.6": {"type": "package", "dependencies": {"System.Management": "7.0.0"}, "compile": {"lib/net7.0-windows7.0/Guna.UI2.dll": {}}, "runtime": {"lib/net7.0-windows7.0/Guna.UI2.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WindowsForms"]}, "ImGui.NET/1.90.1.1": {"type": "package", "dependencies": {"System.Buffers": "4.4.0", "System.Numerics.Vectors": "4.4.0", "System.Runtime.CompilerServices.Unsafe": "4.4.0"}, "compile": {"lib/net6.0/ImGui.NET.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/ImGui.NET.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/linux-x64/native/libcimgui.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx/native/libcimgui.dylib": {"assetType": "native", "rid": "osx"}, "runtimes/win-arm64/native/cimgui.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/cimgui.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/cimgui.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.DotNet.ILCompiler/7.0.20": {"type": "package", "build": {"build/Microsoft.DotNet.ILCompiler.props": {}, "build/Microsoft.DotNet.ILCompiler.targets": {}}}, "Microsoft.NET.ILLink.Analyzers/7.0.100-1.23211.1": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Analyzers.props": {}}}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23211.1": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "MinHook.NET/1.1.1": {"type": "package", "compile": {"lib/net45/MinHook.NET.dll": {}}, "runtime": {"lib/net45/MinHook.NET.dll": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "SharpGen.Runtime/2.1.2-beta": {"type": "package", "compile": {"lib/net7.0/SharpGen.Runtime.dll": {}}, "runtime": {"lib/net7.0/SharpGen.Runtime.dll": {}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "SharpGen.Runtime.COM/2.1.2-beta": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.1.2-beta"}, "compile": {"lib/net7.0/SharpGen.Runtime.COM.dll": {}}, "runtime": {"lib/net7.0/SharpGen.Runtime.COM.dll": {}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "SixLabors.Fonts/2.1.2": {"type": "package", "compile": {"lib/net6.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/3.1.2": {"type": "package", "compile": {"lib/net6.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "build": {"build/_._": {}}}, "System.Buffers/4.4.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.CodeDom/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.Management/7.0.0": {"type": "package", "dependencies": {"System.CodeDom": "7.0.0"}, "compile": {"lib/net7.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Numerics.Vectors/4.4.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "Vortice.D3DCompiler/3.3.4": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.1.2-beta", "SharpGen.Runtime.COM": "2.1.2-beta", "Vortice.DirectX": "3.3.4", "Vortice.Mathematics": "1.7.2"}, "compile": {"lib/net7.0/Vortice.D3DCompiler.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Vortice.D3DCompiler.dll": {"related": ".pdb;.xml"}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Vortice.Direct3D11/3.3.4": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.1.2-beta", "SharpGen.Runtime.COM": "2.1.2-beta", "Vortice.DXGI": "3.3.4", "Vortice.Mathematics": "1.7.2"}, "compile": {"lib/net7.0/Vortice.Direct3D11.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Vortice.Direct3D11.dll": {"related": ".pdb;.xml"}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Vortice.DirectX/3.3.4": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.1.2-beta", "SharpGen.Runtime.COM": "2.1.2-beta", "Vortice.Mathematics": "1.7.2"}, "compile": {"lib/net7.0/Vortice.DirectX.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Vortice.DirectX.dll": {"related": ".pdb;.xml"}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Vortice.DXGI/3.3.4": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.1.2-beta", "SharpGen.Runtime.COM": "2.1.2-beta", "Vortice.DirectX": "3.3.4", "Vortice.Mathematics": "1.7.2"}, "compile": {"lib/net7.0/Vortice.DXGI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Vortice.DXGI.dll": {"related": ".pdb;.xml"}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Vortice.Mathematics/1.7.2": {"type": "package", "compile": {"lib/net7.0/Vortice.Mathematics.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Vortice.Mathematics.dll": {"related": ".pdb;.xml"}}}, "WinFormsComInterop/0.5.0": {"type": "package", "compile": {"lib/net7.0/WinFormsComInterop.dll": {}}, "runtime": {"lib/net7.0/WinFormsComInterop.dll": {}}}}, "net7.0-windows7.0/win-x64": {"ClickableTransparentOverlay/9.3.0": {"type": "package", "dependencies": {"ImGui.NET": "1.90.1.1", "SixLabors.ImageSharp": "3.1.2", "Vortice.D3DCompiler": "3.3.4", "Vortice.Direct3D11": "3.3.4"}, "compile": {"lib/net7.0/ClickableTransparentOverlay.dll": {}}, "runtime": {"lib/net7.0/ClickableTransparentOverlay.dll": {}}}, "DNNE/2.0.6": {"type": "package", "build": {"build/DNNE.props": {}, "build/DNNE.targets": {}}}, "GlobalHotKey/1.1.0": {"type": "package", "compile": {"lib/GlobalHotKey.dll": {}}, "runtime": {"lib/GlobalHotKey.dll": {}}}, "Guna.UI2.WinForms/2.0.4.6": {"type": "package", "dependencies": {"System.Management": "7.0.0"}, "compile": {"lib/net7.0-windows7.0/Guna.UI2.dll": {}}, "runtime": {"lib/net7.0-windows7.0/Guna.UI2.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WindowsForms"]}, "ImGui.NET/1.90.1.1": {"type": "package", "dependencies": {"System.Buffers": "4.4.0", "System.Numerics.Vectors": "4.4.0", "System.Runtime.CompilerServices.Unsafe": "4.4.0"}, "compile": {"lib/net6.0/ImGui.NET.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/ImGui.NET.dll": {"related": ".xml"}}, "native": {"runtimes/win-x64/native/cimgui.dll": {}}}, "Microsoft.DotNet.ILCompiler/7.0.20": {"type": "package", "dependencies": {"runtime.win-x64.Microsoft.DotNet.ILCompiler": "7.0.20"}, "build": {"build/Microsoft.DotNet.ILCompiler.props": {}, "build/Microsoft.DotNet.ILCompiler.targets": {}}}, "Microsoft.NET.ILLink.Analyzers/7.0.100-1.23211.1": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Analyzers.props": {}}}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23211.1": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "MinHook.NET/1.1.1": {"type": "package", "compile": {"lib/net45/MinHook.NET.dll": {}}, "runtime": {"lib/net45/MinHook.NET.dll": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "runtime.any.System.IO/4.3.0": {"type": "package", "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.5/System.IO.dll": {}}}, "runtime.any.System.Reflection/4.3.0": {"type": "package", "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.5/System.Reflection.dll": {}}}, "runtime.any.System.Reflection.Primitives/4.3.0": {"type": "package", "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.3/System.Reflection.Primitives.dll": {}}}, "runtime.any.System.Runtime/4.3.0": {"type": "package", "dependencies": {"System.Private.Uri": "4.3.0"}, "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.5/System.Runtime.dll": {}}}, "runtime.any.System.Text.Encoding/4.3.0": {"type": "package", "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.3/System.Text.Encoding.dll": {}}}, "runtime.any.System.Threading.Tasks/4.3.0": {"type": "package", "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.3/System.Threading.Tasks.dll": {}}}, "runtime.win-x64.Microsoft.DotNet.ILCompiler/7.0.20": {"type": "package"}, "SharpGen.Runtime/2.1.2-beta": {"type": "package", "compile": {"lib/net7.0/SharpGen.Runtime.dll": {}}, "runtime": {"lib/net7.0/SharpGen.Runtime.dll": {}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "SharpGen.Runtime.COM/2.1.2-beta": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.1.2-beta"}, "compile": {"lib/net7.0/SharpGen.Runtime.COM.dll": {}}, "runtime": {"lib/net7.0/SharpGen.Runtime.COM.dll": {}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "SixLabors.Fonts/2.1.2": {"type": "package", "compile": {"lib/net6.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/3.1.2": {"type": "package", "compile": {"lib/net6.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "build": {"build/_._": {}}}, "System.Buffers/4.4.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.CodeDom/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.any.System.IO": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.Management/7.0.0": {"type": "package", "dependencies": {"System.CodeDom": "7.0.0"}, "compile": {"lib/net7.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net7.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Numerics.Vectors/4.4.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Private.Uri/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard/_._": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Primitives": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.any.System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Text.Encoding": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "Vortice.D3DCompiler/3.3.4": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.1.2-beta", "SharpGen.Runtime.COM": "2.1.2-beta", "Vortice.DirectX": "3.3.4", "Vortice.Mathematics": "1.7.2"}, "compile": {"lib/net7.0/Vortice.D3DCompiler.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Vortice.D3DCompiler.dll": {"related": ".pdb;.xml"}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Vortice.Direct3D11/3.3.4": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.1.2-beta", "SharpGen.Runtime.COM": "2.1.2-beta", "Vortice.DXGI": "3.3.4", "Vortice.Mathematics": "1.7.2"}, "compile": {"lib/net7.0/Vortice.Direct3D11.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Vortice.Direct3D11.dll": {"related": ".pdb;.xml"}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Vortice.DirectX/3.3.4": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.1.2-beta", "SharpGen.Runtime.COM": "2.1.2-beta", "Vortice.Mathematics": "1.7.2"}, "compile": {"lib/net7.0/Vortice.DirectX.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Vortice.DirectX.dll": {"related": ".pdb;.xml"}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Vortice.DXGI/3.3.4": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.1.2-beta", "SharpGen.Runtime.COM": "2.1.2-beta", "Vortice.DirectX": "3.3.4", "Vortice.Mathematics": "1.7.2"}, "compile": {"lib/net7.0/Vortice.DXGI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Vortice.DXGI.dll": {"related": ".pdb;.xml"}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Vortice.Mathematics/1.7.2": {"type": "package", "compile": {"lib/net7.0/Vortice.Mathematics.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Vortice.Mathematics.dll": {"related": ".pdb;.xml"}}}, "WinFormsComInterop/0.5.0": {"type": "package", "compile": {"lib/net7.0/WinFormsComInterop.dll": {}}, "runtime": {"lib/net7.0/WinFormsComInterop.dll": {}}}}}, "libraries": {"ClickableTransparentOverlay/9.3.0": {"sha512": "DxhW1jh50MRyZZtSKmKUjxg8ik0rvCxnUMc22UjOHLlVQtFaxS8QOIYLjEed4X+CCsl0VXJybNlvBxJisZX83Q==", "type": "package", "path": "clickabletransparentoverlay/9.3.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "clickabletransparentoverlay.9.3.0.nupkg.sha512", "clickabletransparentoverlay.nuspec", "lib/net7.0/ClickableTransparentOverlay.dll"]}, "DNNE/2.0.6": {"sha512": "C9f/e0nlN1iIvIhmQFcr84KDuEQgkxGpAX4Cde+4ZaGegiSEGRV9M4vGDhfRiw7b8DW7/zObcFOkORcEAgoMng==", "type": "package", "path": "dnne/2.0.6", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/dnne-analyzers.dll", "build/DNNE.Analyzers.targets", "build/DNNE.props", "build/DNNE.targets", "build/net472/DNNE.BuildTasks.dll", "build/net472/DNNE.BuildTasks.pdb", "build/net472/Microsoft.Build.Framework.dll", "build/net472/Microsoft.Build.Utilities.Core.dll", "build/net472/Microsoft.VisualStudio.Setup.Configuration.Interop.dll", "build/net472/System.Collections.Immutable.dll", "build/netstandard2.1/DNNE.BuildTasks.deps.json", "build/netstandard2.1/DNNE.BuildTasks.dll", "build/netstandard2.1/DNNE.BuildTasks.pdb", "dnne.2.0.6.nupkg.sha512", "dnne.nuspec", "tools/dnne-gen.dll", "tools/dnne-gen.runtimeconfig.json", "tools/platform/dnne.h", "tools/platform/platform.c", "tools/platform/platform_v4.cpp"]}, "GlobalHotKey/1.1.0": {"sha512": "KkEHhyM+sSh27kF1d3YlfpXdYlkfu17XCiI3WfYy1cn65y3loR7f+GMkMwHTgf0xSyS3QdXTKCRfVLzb7jgJFQ==", "type": "package", "path": "globalhotkey/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "globalhotkey.1.1.0.nupkg.sha512", "globalhotkey.nuspec", "lib/GlobalHotKey.dll"]}, "Guna.UI2.WinForms/2.0.4.6": {"sha512": "NYzSJp7e47Cz3NgGCHvzadash8ycmxxUnpLg4Rr/MSYG9WDw0HOoTI4SkbDW+HbACrf6tsWYCuzWkgvjuM/O6w==", "type": "package", "path": "guna.ui2.winforms/2.0.4.6", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "guna.ui2.winforms.2.0.4.6.nupkg.sha512", "guna.ui2.winforms.nuspec", "icon.png", "lib/net40/Guna.UI2.dll", "lib/net45/Guna.UI2.dll", "lib/net461/Guna.UI2.dll", "lib/net472/Guna.UI2.dll", "lib/net48/Guna.UI2.dll", "lib/net6.0-windows7.0/Guna.UI2.dll", "lib/net7.0-windows7.0/Guna.UI2.dll", "lib/netcoreapp3.1/Guna.UI2.dll"]}, "ImGui.NET/1.90.1.1": {"sha512": "/VNgkRywtXV72cpxtBj09nZX9Yx+xG3NxiwEEXPVcK+pHKHA31cPJKy425tDGj68tgRbCS+C+DG8puBj3iJ4zA==", "type": "package", "path": "imgui.net/1.90.1.1", "files": [".nupkg.metadata", ".signature.p7s", "build/net40/ImGui.NET.targets", "imgui.net.1.90.1.1.nupkg.sha512", "imgui.net.nuspec", "lib/net6.0/ImGui.NET.dll", "lib/net6.0/ImGui.NET.xml", "lib/netstandard2.0/ImGui.NET.dll", "lib/netstandard2.0/ImGui.NET.xml", "runtimes/linux-x64/native/libcimgui.so", "runtimes/osx/native/libcimgui.dylib", "runtimes/win-arm64/native/cimgui.dll", "runtimes/win-x64/native/cimgui.dll", "runtimes/win-x86/native/cimgui.dll"]}, "Microsoft.DotNet.ILCompiler/7.0.20": {"sha512": "9gGpu74pc/FBQuHqTb0pnvTftfSpQIkOfic7z9czAPTlCxEvY8rsEsYcfIX7ExvcIEr35Rf5cpxqCH09gApW3Q==", "type": "package", "path": "microsoft.dotnet.ilcompiler/7.0.20", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "Sdk/Sdk.props", "THIRD-PARTY-NOTICES.TXT", "build/BuildFrameworkNativeObjects.proj", "build/Microsoft.DotNet.ILCompiler.SingleEntry.targets", "build/Microsoft.DotNet.ILCompiler.props", "build/Microsoft.DotNet.ILCompiler.targets", "build/Microsoft.NETCore.Native.Publish.targets", "build/Microsoft.NETCore.Native.Unix.targets", "build/Microsoft.NETCore.Native.Windows.targets", "build/Microsoft.NETCore.Native.targets", "build/NativeAOT.natvis", "build/WindowsAPIs.txt", "build/findvcvarsall.bat", "microsoft.dotnet.ilcompiler.7.0.20.nupkg.sha512", "microsoft.dotnet.ilcompiler.nuspec", "runtime.json", "tools/netstandard/ILCompiler.Build.Tasks.deps.json", "tools/netstandard/ILCompiler.Build.Tasks.dll", "tools/netstandard/ILCompiler.Build.Tasks.pdb", "tools/netstandard/System.Collections.Immutable.dll", "tools/netstandard/System.Reflection.Metadata.dll"]}, "Microsoft.NET.ILLink.Analyzers/7.0.100-1.23211.1": {"sha512": "0GvbEgDGcUQA9KuWcQU1WwYHXt1tBzNr1Nls/M57rM7NA/AndFwCaCEoJpJkmxRY7xLlPDBnmGp8h5+FNqUngg==", "type": "package", "path": "microsoft.net.illink.analyzers/7.0.100-1.23211.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/ILLink.CodeFixProvider.dll", "analyzers/dotnet/cs/ILLink.RoslynAnalyzer.dll", "build/Microsoft.NET.ILLink.Analyzers.props", "microsoft.net.illink.analyzers.7.0.100-1.23211.1.nupkg.sha512", "microsoft.net.illink.analyzers.nuspec"]}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23211.1": {"sha512": "tvG8XZYLjT0o3WicCyKBZysVWo1jC9HdCFmNRmddx3WbAz0UCsd0qKZqpiEo99VLA8Re+FzWK51OcRldQPbt2Q==", "type": "package", "path": "microsoft.net.illink.tasks/7.0.100-1.23211.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "Sdk/Sdk.props", "build/6.0_suppressions.xml", "build/Microsoft.NET.ILLink.Tasks.props", "build/Microsoft.NET.ILLink.targets", "microsoft.net.illink.tasks.7.0.100-1.23211.1.nupkg.sha512", "microsoft.net.illink.tasks.nuspec", "tools/net472/ILLink.Tasks.dll", "tools/net472/Mono.Cecil.dll", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net7.0/ILLink.Tasks.deps.json", "tools/net7.0/ILLink.Tasks.dll", "tools/net7.0/Mono.Cecil.Pdb.dll", "tools/net7.0/Mono.Cecil.dll", "tools/net7.0/illink.deps.json", "tools/net7.0/illink.dll", "tools/net7.0/illink.runtimeconfig.json"]}, "Microsoft.NETCore.Platforms/1.1.0": {"sha512": "kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "type": "package", "path": "microsoft.netcore.platforms/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.1.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "MinHook.NET/1.1.1": {"sha512": "k+cLk84AxAAiOB/qBLT80p6/y9g8jXGzhLtxRwfbyNR1opxOYOmQWv+v58hQ2xyT+xkNM8GVhY1VTLuPTwP8kw==", "type": "package", "path": "minhook.net/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/MinHook.NET.dll", "lib/net45/MinHook.NET.dll", "minhook.net.1.1.1.nupkg.sha512", "minhook.net.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "runtime.any.System.IO/4.3.0": {"sha512": "SDZ5AD1DtyRoxYtEcqQ3HDlcrorMYXZeCt7ZhG9US9I5Vva+gpIWDGMkcwa5XiKL0ceQKRZIX2x0XEjLX7PDzQ==", "type": "package", "path": "runtime.any.system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.IO.dll", "lib/netstandard1.5/System.IO.dll", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.io.4.3.0.nupkg.sha512", "runtime.any.system.io.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.any.System.Reflection/4.3.0": {"sha512": "hLC3A3rI8jipR5d9k7+f0MgRCW6texsAp0MWkN/ci18FMtQ9KH7E2vDn/DH2LkxsszlpJpOn9qy6Z6/69rH6eQ==", "type": "package", "path": "runtime.any.system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.dll", "lib/netstandard1.5/System.Reflection.dll", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.reflection.4.3.0.nupkg.sha512", "runtime.any.system.reflection.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.any.System.Reflection.Primitives/4.3.0": {"sha512": "Nrm1p3armp6TTf2xuvaa+jGTTmncALWFq22CpmwRvhDf6dE9ZmH40EbOswD4GnFLrMRS0Ki6Kx5aUPmKK/hZBg==", "type": "package", "path": "runtime.any.system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Primitives.dll", "lib/netstandard1.3/System.Reflection.Primitives.dll", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.reflection.primitives.4.3.0.nupkg.sha512", "runtime.any.system.reflection.primitives.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.any.System.Runtime/4.3.0": {"sha512": "fRS7zJgaG9NkifaAxGGclDDoRn9HC7hXACl52Or06a/fxdzDajWb5wov3c6a+gVSlekRoexfjwQSK9sh5um5LQ==", "type": "package", "path": "runtime.any.system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Runtime.dll", "lib/netstandard1.5/System.Runtime.dll", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.runtime.4.3.0.nupkg.sha512", "runtime.any.system.runtime.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.any.System.Text.Encoding/4.3.0": {"sha512": "+ihI5VaXFCMVPJNstG4O4eo1CfbrByLxRrQQTqOTp1ttK0kUKDqOdBSTaCB2IBk/QtjDrs6+x4xuezyMXdm0HQ==", "type": "package", "path": "runtime.any.system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Text.Encoding.dll", "lib/netstandard1.3/System.Text.Encoding.dll", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.text.encoding.4.3.0.nupkg.sha512", "runtime.any.system.text.encoding.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.any.System.Threading.Tasks/4.3.0": {"sha512": "OhBAVBQG5kFj1S+hCEQ3TUHBAEtZ3fbEMgZMRNdN8A0Pj4x+5nTELEqL59DU0TjKVE6II3dqKw4Dklb3szT65w==", "type": "package", "path": "runtime.any.system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.Tasks.dll", "lib/netstandard1.3/System.Threading.Tasks.dll", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.threading.tasks.4.3.0.nupkg.sha512", "runtime.any.system.threading.tasks.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.win-x64.Microsoft.DotNet.ILCompiler/7.0.20": {"sha512": "Xn/ZM3iUfLMdEKJeOJMJVLOEsIkH3jmasq8/ig9xKkk/3myJLpN9d8wj8fu48ztiRievi8n3Zpp5uDUZI1phmQ==", "type": "package", "path": "runtime.win-x64.microsoft.dotnet.ilcompiler/7.0.20", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/BuildFrameworkNativeObjects.proj", "build/Microsoft.DotNet.ILCompiler.SingleEntry.targets", "build/Microsoft.DotNet.ILCompiler.props", "build/Microsoft.DotNet.ILCompiler.targets", "build/Microsoft.NETCore.Native.Publish.targets", "build/Microsoft.NETCore.Native.Unix.targets", "build/Microsoft.NETCore.Native.Windows.targets", "build/Microsoft.NETCore.Native.targets", "build/NativeAOT.natvis", "build/WindowsAPIs.txt", "build/findvcvarsall.bat", "framework/Microsoft.CSharp.dll", "framework/Microsoft.CSharp.pdb", "framework/Microsoft.VisualBasic.Core.dll", "framework/Microsoft.VisualBasic.Core.pdb", "framework/Microsoft.VisualBasic.dll", "framework/Microsoft.VisualBasic.pdb", "framework/Microsoft.Win32.Primitives.dll", "framework/Microsoft.Win32.Primitives.pdb", "framework/Microsoft.Win32.Registry.dll", "framework/Microsoft.Win32.Registry.pdb", "framework/System.AppContext.dll", "framework/System.AppContext.pdb", "framework/System.Buffers.dll", "framework/System.Buffers.pdb", "framework/System.Collections.Concurrent.dll", "framework/System.Collections.Concurrent.pdb", "framework/System.Collections.Immutable.dll", "framework/System.Collections.Immutable.pdb", "framework/System.Collections.NonGeneric.dll", "framework/System.Collections.NonGeneric.pdb", "framework/System.Collections.Specialized.dll", "framework/System.Collections.Specialized.pdb", "framework/System.Collections.dll", "framework/System.Collections.pdb", "framework/System.ComponentModel.Annotations.dll", "framework/System.ComponentModel.Annotations.pdb", "framework/System.ComponentModel.DataAnnotations.dll", "framework/System.ComponentModel.DataAnnotations.pdb", "framework/System.ComponentModel.EventBasedAsync.dll", "framework/System.ComponentModel.EventBasedAsync.pdb", "framework/System.ComponentModel.Primitives.dll", "framework/System.ComponentModel.Primitives.pdb", "framework/System.ComponentModel.TypeConverter.dll", "framework/System.ComponentModel.TypeConverter.pdb", "framework/System.ComponentModel.dll", "framework/System.ComponentModel.pdb", "framework/System.Configuration.dll", "framework/System.Configuration.pdb", "framework/System.Console.dll", "framework/System.Console.pdb", "framework/System.Core.dll", "framework/System.Core.pdb", "framework/System.Data.Common.dll", "framework/System.Data.Common.pdb", "framework/System.Data.DataSetExtensions.dll", "framework/System.Data.DataSetExtensions.pdb", "framework/System.Data.dll", "framework/System.Data.pdb", "framework/System.Diagnostics.Contracts.dll", "framework/System.Diagnostics.Contracts.pdb", "framework/System.Diagnostics.Debug.dll", "framework/System.Diagnostics.Debug.pdb", "framework/System.Diagnostics.DiagnosticSource.dll", "framework/System.Diagnostics.DiagnosticSource.pdb", "framework/System.Diagnostics.FileVersionInfo.dll", "framework/System.Diagnostics.FileVersionInfo.pdb", "framework/System.Diagnostics.Process.dll", "framework/System.Diagnostics.Process.pdb", "framework/System.Diagnostics.StackTrace.dll", "framework/System.Diagnostics.StackTrace.pdb", "framework/System.Diagnostics.TextWriterTraceListener.dll", "framework/System.Diagnostics.TextWriterTraceListener.pdb", "framework/System.Diagnostics.Tools.dll", "framework/System.Diagnostics.Tools.pdb", "framework/System.Diagnostics.TraceSource.dll", "framework/System.Diagnostics.TraceSource.pdb", "framework/System.Diagnostics.Tracing.dll", "framework/System.Diagnostics.Tracing.pdb", "framework/System.Drawing.Primitives.dll", "framework/System.Drawing.Primitives.pdb", "framework/System.Drawing.dll", "framework/System.Drawing.pdb", "framework/System.Dynamic.Runtime.dll", "framework/System.Dynamic.Runtime.pdb", "framework/System.Formats.Asn1.dll", "framework/System.Formats.Asn1.pdb", "framework/System.Formats.Tar.dll", "framework/System.Formats.Tar.pdb", "framework/System.Globalization.Calendars.dll", "framework/System.Globalization.Calendars.pdb", "framework/System.Globalization.Extensions.dll", "framework/System.Globalization.Extensions.pdb", "framework/System.Globalization.dll", "framework/System.Globalization.pdb", "framework/System.IO.Compression.Brotli.dll", "framework/System.IO.Compression.Brotli.pdb", "framework/System.IO.Compression.FileSystem.dll", "framework/System.IO.Compression.FileSystem.pdb", "framework/System.IO.Compression.Native.dll", "framework/System.IO.Compression.Native.pdb", "framework/System.IO.Compression.ZipFile.dll", "framework/System.IO.Compression.ZipFile.pdb", "framework/System.IO.Compression.dll", "framework/System.IO.Compression.pdb", "framework/System.IO.FileSystem.AccessControl.dll", "framework/System.IO.FileSystem.AccessControl.pdb", "framework/System.IO.FileSystem.DriveInfo.dll", "framework/System.IO.FileSystem.DriveInfo.pdb", "framework/System.IO.FileSystem.Primitives.dll", "framework/System.IO.FileSystem.Primitives.pdb", "framework/System.IO.FileSystem.Watcher.dll", "framework/System.IO.FileSystem.Watcher.pdb", "framework/System.IO.FileSystem.dll", "framework/System.IO.FileSystem.pdb", "framework/System.IO.IsolatedStorage.dll", "framework/System.IO.IsolatedStorage.pdb", "framework/System.IO.MemoryMappedFiles.dll", "framework/System.IO.MemoryMappedFiles.pdb", "framework/System.IO.Pipes.AccessControl.dll", "framework/System.IO.Pipes.AccessControl.pdb", "framework/System.IO.Pipes.dll", "framework/System.IO.Pipes.pdb", "framework/System.IO.UnmanagedMemoryStream.dll", "framework/System.IO.UnmanagedMemoryStream.pdb", "framework/System.IO.dll", "framework/System.IO.pdb", "framework/System.Linq.Expressions.dll", "framework/System.Linq.Expressions.pdb", "framework/System.Linq.Parallel.dll", "framework/System.Linq.Parallel.pdb", "framework/System.Linq.Queryable.dll", "framework/System.Linq.Queryable.pdb", "framework/System.Linq.dll", "framework/System.Linq.pdb", "framework/System.Memory.dll", "framework/System.Memory.pdb", "framework/System.Net.Http.Json.dll", "framework/System.Net.Http.Json.pdb", "framework/System.Net.Http.dll", "framework/System.Net.Http.pdb", "framework/System.Net.HttpListener.dll", "framework/System.Net.HttpListener.pdb", "framework/System.Net.Mail.dll", "framework/System.Net.Mail.pdb", "framework/System.Net.NameResolution.dll", "framework/System.Net.NameResolution.pdb", "framework/System.Net.NetworkInformation.dll", "framework/System.Net.NetworkInformation.pdb", "framework/System.Net.Ping.dll", "framework/System.Net.Ping.pdb", "framework/System.Net.Primitives.dll", "framework/System.Net.Primitives.pdb", "framework/System.Net.Quic.dll", "framework/System.Net.Quic.pdb", "framework/System.Net.Requests.dll", "framework/System.Net.Requests.pdb", "framework/System.Net.Security.dll", "framework/System.Net.Security.pdb", "framework/System.Net.ServicePoint.dll", "framework/System.Net.ServicePoint.pdb", "framework/System.Net.Sockets.dll", "framework/System.Net.Sockets.pdb", "framework/System.Net.WebClient.dll", "framework/System.Net.WebClient.pdb", "framework/System.Net.WebHeaderCollection.dll", "framework/System.Net.WebHeaderCollection.pdb", "framework/System.Net.WebProxy.dll", "framework/System.Net.WebProxy.pdb", "framework/System.Net.WebSockets.Client.dll", "framework/System.Net.WebSockets.Client.pdb", "framework/System.Net.WebSockets.dll", "framework/System.Net.WebSockets.pdb", "framework/System.Net.dll", "framework/System.Net.pdb", "framework/System.Numerics.Vectors.dll", "framework/System.Numerics.Vectors.pdb", "framework/System.Numerics.dll", "framework/System.Numerics.pdb", "framework/System.ObjectModel.dll", "framework/System.ObjectModel.pdb", "framework/System.Private.DataContractSerialization.dll", "framework/System.Private.DataContractSerialization.pdb", "framework/System.Private.Uri.dll", "framework/System.Private.Uri.pdb", "framework/System.Private.Xml.Linq.dll", "framework/System.Private.Xml.Linq.pdb", "framework/System.Private.Xml.dll", "framework/System.Private.Xml.pdb", "framework/System.Reflection.DispatchProxy.dll", "framework/System.Reflection.DispatchProxy.pdb", "framework/System.Reflection.Emit.ILGeneration.dll", "framework/System.Reflection.Emit.ILGeneration.pdb", "framework/System.Reflection.Emit.Lightweight.dll", "framework/System.Reflection.Emit.Lightweight.pdb", "framework/System.Reflection.Emit.dll", "framework/System.Reflection.Emit.pdb", "framework/System.Reflection.Extensions.dll", "framework/System.Reflection.Extensions.pdb", "framework/System.Reflection.Metadata.dll", "framework/System.Reflection.Metadata.pdb", "framework/System.Reflection.Primitives.dll", "framework/System.Reflection.Primitives.pdb", "framework/System.Reflection.TypeExtensions.dll", "framework/System.Reflection.TypeExtensions.pdb", "framework/System.Reflection.dll", "framework/System.Reflection.pdb", "framework/System.Resources.Reader.dll", "framework/System.Resources.Reader.pdb", "framework/System.Resources.ResourceManager.dll", "framework/System.Resources.ResourceManager.pdb", "framework/System.Resources.Writer.dll", "framework/System.Resources.Writer.pdb", "framework/System.Runtime.CompilerServices.Unsafe.dll", "framework/System.Runtime.CompilerServices.Unsafe.pdb", "framework/System.Runtime.CompilerServices.VisualC.dll", "framework/System.Runtime.CompilerServices.VisualC.pdb", "framework/System.Runtime.Extensions.dll", "framework/System.Runtime.Extensions.pdb", "framework/System.Runtime.Handles.dll", "framework/System.Runtime.Handles.pdb", "framework/System.Runtime.InteropServices.JavaScript.dll", "framework/System.Runtime.InteropServices.JavaScript.pdb", "framework/System.Runtime.InteropServices.RuntimeInformation.dll", "framework/System.Runtime.InteropServices.RuntimeInformation.pdb", "framework/System.Runtime.InteropServices.dll", "framework/System.Runtime.InteropServices.pdb", "framework/System.Runtime.Intrinsics.dll", "framework/System.Runtime.Intrinsics.pdb", "framework/System.Runtime.Loader.dll", "framework/System.Runtime.Loader.pdb", "framework/System.Runtime.Numerics.dll", "framework/System.Runtime.Numerics.pdb", "framework/System.Runtime.Serialization.Formatters.dll", "framework/System.Runtime.Serialization.Formatters.pdb", "framework/System.Runtime.Serialization.Json.dll", "framework/System.Runtime.Serialization.Json.pdb", "framework/System.Runtime.Serialization.Primitives.dll", "framework/System.Runtime.Serialization.Primitives.pdb", "framework/System.Runtime.Serialization.Xml.dll", "framework/System.Runtime.Serialization.Xml.pdb", "framework/System.Runtime.Serialization.dll", "framework/System.Runtime.Serialization.pdb", "framework/System.Runtime.dll", "framework/System.Runtime.pdb", "framework/System.Security.AccessControl.dll", "framework/System.Security.AccessControl.pdb", "framework/System.Security.Claims.dll", "framework/System.Security.Claims.pdb", "framework/System.Security.Cryptography.Algorithms.dll", "framework/System.Security.Cryptography.Algorithms.pdb", "framework/System.Security.Cryptography.Cng.dll", "framework/System.Security.Cryptography.Cng.pdb", "framework/System.Security.Cryptography.Csp.dll", "framework/System.Security.Cryptography.Csp.pdb", "framework/System.Security.Cryptography.Encoding.dll", "framework/System.Security.Cryptography.Encoding.pdb", "framework/System.Security.Cryptography.OpenSsl.dll", "framework/System.Security.Cryptography.OpenSsl.pdb", "framework/System.Security.Cryptography.Primitives.dll", "framework/System.Security.Cryptography.Primitives.pdb", "framework/System.Security.Cryptography.X509Certificates.dll", "framework/System.Security.Cryptography.X509Certificates.pdb", "framework/System.Security.Cryptography.dll", "framework/System.Security.Cryptography.pdb", "framework/System.Security.Principal.Windows.dll", "framework/System.Security.Principal.Windows.pdb", "framework/System.Security.Principal.dll", "framework/System.Security.Principal.pdb", "framework/System.Security.SecureString.dll", "framework/System.Security.SecureString.pdb", "framework/System.Security.dll", "framework/System.Security.pdb", "framework/System.ServiceModel.Web.dll", "framework/System.ServiceModel.Web.pdb", "framework/System.ServiceProcess.dll", "framework/System.ServiceProcess.pdb", "framework/System.Text.Encoding.CodePages.dll", "framework/System.Text.Encoding.CodePages.pdb", "framework/System.Text.Encoding.Extensions.dll", "framework/System.Text.Encoding.Extensions.pdb", "framework/System.Text.Encoding.dll", "framework/System.Text.Encoding.pdb", "framework/System.Text.Encodings.Web.dll", "framework/System.Text.Encodings.Web.pdb", "framework/System.Text.Json.dll", "framework/System.Text.Json.pdb", "framework/System.Text.RegularExpressions.dll", "framework/System.Text.RegularExpressions.pdb", "framework/System.Threading.Channels.dll", "framework/System.Threading.Channels.pdb", "framework/System.Threading.Overlapped.dll", "framework/System.Threading.Overlapped.pdb", "framework/System.Threading.Tasks.Dataflow.dll", "framework/System.Threading.Tasks.Dataflow.pdb", "framework/System.Threading.Tasks.Extensions.dll", "framework/System.Threading.Tasks.Extensions.pdb", "framework/System.Threading.Tasks.Parallel.dll", "framework/System.Threading.Tasks.Parallel.pdb", "framework/System.Threading.Tasks.dll", "framework/System.Threading.Tasks.pdb", "framework/System.Threading.Thread.dll", "framework/System.Threading.Thread.pdb", "framework/System.Threading.ThreadPool.dll", "framework/System.Threading.ThreadPool.pdb", "framework/System.Threading.Timer.dll", "framework/System.Threading.Timer.pdb", "framework/System.Threading.dll", "framework/System.Threading.pdb", "framework/System.Transactions.Local.dll", "framework/System.Transactions.Local.pdb", "framework/System.Transactions.dll", "framework/System.Transactions.pdb", "framework/System.ValueTuple.dll", "framework/System.ValueTuple.pdb", "framework/System.Web.HttpUtility.dll", "framework/System.Web.HttpUtility.pdb", "framework/System.Web.dll", "framework/System.Web.pdb", "framework/System.Windows.dll", "framework/System.Windows.pdb", "framework/System.Xml.Linq.dll", "framework/System.Xml.Linq.pdb", "framework/System.Xml.ReaderWriter.dll", "framework/System.Xml.ReaderWriter.pdb", "framework/System.Xml.Serialization.dll", "framework/System.Xml.Serialization.pdb", "framework/System.Xml.XDocument.dll", "framework/System.Xml.XDocument.pdb", "framework/System.Xml.XPath.XDocument.dll", "framework/System.Xml.XPath.XDocument.pdb", "framework/System.Xml.XPath.dll", "framework/System.Xml.XPath.pdb", "framework/System.Xml.XmlDocument.dll", "framework/System.Xml.XmlDocument.pdb", "framework/System.Xml.XmlSerializer.dll", "framework/System.Xml.XmlSerializer.pdb", "framework/System.Xml.dll", "framework/System.Xml.pdb", "framework/System.dll", "framework/System.pdb", "framework/WindowsBase.dll", "framework/WindowsBase.pdb", "framework/mscorlib.dll", "framework/mscorlib.pdb", "framework/msquic.dll", "framework/msquic.pdb", "framework/netstandard.dll", "framework/netstandard.pdb", "mibc/DotNet_Adhoc.mibc", "mibc/DotNet_FirstTimeXP.mibc", "mibc/DotNet_HelloWorld.mibc", "mibc/DotNet_OrchardCore.mibc", "mibc/DotNet_TechEmpower.mibc", "runtime.win-x64.microsoft.dotnet.ilcompiler.7.0.20.nupkg.sha512", "runtime.win-x64.microsoft.dotnet.ilcompiler.nuspec", "sdk/Runtime.ServerGC.GuardCF.lib", "sdk/Runtime.ServerGC.GuardCF.pdb", "sdk/Runtime.ServerGC.lib", "sdk/Runtime.ServerGC.pdb", "sdk/Runtime.WorkstationGC.lib", "sdk/Runtime.WorkstationGC.pdb", "sdk/Sdk.props", "sdk/System.Globalization.Native.Aot.GuardCF.lib", "sdk/System.Globalization.Native.Aot.GuardCF.pdb", "sdk/System.Globalization.Native.Aot.lib", "sdk/System.Globalization.Native.Aot.pdb", "sdk/System.IO.Compression.Native.Aot.GuardCF.lib", "sdk/System.IO.Compression.Native.Aot.GuardCF.pdb", "sdk/System.IO.Compression.Native.Aot.lib", "sdk/System.IO.Compression.Native.Aot.pdb", "sdk/System.Private.CoreLib.dll", "sdk/System.Private.CoreLib.pdb", "sdk/System.Private.CoreLib.xml", "sdk/System.Private.DisabledReflection.dll", "sdk/System.Private.DisabledReflection.pdb", "sdk/System.Private.DisabledReflection.xml", "sdk/System.Private.Reflection.Execution.dll", "sdk/System.Private.Reflection.Execution.pdb", "sdk/System.Private.Reflection.Execution.xml", "sdk/System.Private.StackTraceMetadata.dll", "sdk/System.Private.StackTraceMetadata.pdb", "sdk/System.Private.StackTraceMetadata.xml", "sdk/System.Private.TypeLoader.dll", "sdk/System.Private.TypeLoader.pdb", "sdk/System.Private.TypeLoader.xml", "sdk/bootstrapper.GuardCF.lib", "sdk/bootstrapper.GuardCF.pdb", "sdk/bootstrapper.lib", "sdk/bootstrapper.pdb", "sdk/bootstrapperdll.GuardCF.lib", "sdk/bootstrapperdll.GuardCF.pdb", "sdk/bootstrapperdll.lib", "sdk/bootstrapperdll.pdb", "tools/ILCompiler.Compiler.pdb", "tools/ILCompiler.DependencyAnalysisFramework.pdb", "tools/ILCompiler.MetadataTransform.pdb", "tools/ILCompiler.RyuJit.pdb", "tools/ILCompiler.TypeSystem.pdb", "tools/clrjit_universal_arm64_x64.dll", "tools/clrjit_universal_arm_x64.dll", "tools/clrjit_unix_x64_x64.dll", "tools/clrjit_win_x64_x64.dll", "tools/clrjit_win_x86_x64.dll", "tools/ilc.exe", "tools/ilc.pdb", "tools/jitinterface_x64.dll", "tools/netstandard/ILCompiler.Build.Tasks.deps.json", "tools/netstandard/ILCompiler.Build.Tasks.dll", "tools/netstandard/ILCompiler.Build.Tasks.pdb", "tools/netstandard/System.Collections.Immutable.dll", "tools/netstandard/System.Reflection.Metadata.dll", "tools/objwriter.dll"]}, "SharpGen.Runtime/2.1.2-beta": {"sha512": "nqZAjfEG1jX1ivvdZLsi6Pkt0DiOJyuOgRgldNFsmjXFPhxUbXQibofLSwuDZidL2kkmtTF8qLoRIeqeVdXgYw==", "type": "package", "path": "sharpgen.runtime/2.1.2-beta", "files": [".nupkg.metadata", ".signature.p7s", "build/Mapping.xml", "build/SharpGen.Runtime.props", "buildMultiTargeting/SharpGen.Runtime.props", "lib/net461/SharpGen.Runtime.dll", "lib/net471/SharpGen.Runtime.dll", "lib/net7.0/SharpGen.Runtime.dll", "lib/net8.0/SharpGen.Runtime.dll", "lib/netcoreapp3.1/SharpGen.Runtime.dll", "lib/netstandard2.0/SharpGen.Runtime.dll", "lib/netstandard2.1/SharpGen.Runtime.dll", "sharpgen.runtime.2.1.2-beta.nupkg.sha512", "sharpgen.runtime.nuspec"]}, "SharpGen.Runtime.COM/2.1.2-beta": {"sha512": "HBCrb6HfnUWx9v5/GjJeBr5DuodZLnHlFQQYXPrQs1Hbe1c6Wd0uCXf+SJp4hW8fQNxjXEu0FgiyHGlA/SRzRw==", "type": "package", "path": "sharpgen.runtime.com/2.1.2-beta", "files": [".nupkg.metadata", ".signature.p7s", "build/SharpGen.Runtime.COM.BindMapping.xml", "build/SharpGen.Runtime.COM.props", "buildMultiTargeting/SharpGen.Runtime.COM.props", "lib/net461/SharpGen.Runtime.COM.dll", "lib/net471/SharpGen.Runtime.COM.dll", "lib/net7.0/SharpGen.Runtime.COM.dll", "lib/net8.0/SharpGen.Runtime.COM.dll", "lib/netcoreapp3.1/SharpGen.Runtime.COM.dll", "lib/netstandard2.0/SharpGen.Runtime.COM.dll", "lib/netstandard2.1/SharpGen.Runtime.COM.dll", "sharpgen.runtime.com.2.1.2-beta.nupkg.sha512", "sharpgen.runtime.com.nuspec"]}, "SixLabors.Fonts/2.1.2": {"sha512": "UGl99i9hCJ4MXLaoGw2aq8nklIL/31QcRU7oqQza4AqCg54XojtIIRczj9aO7zJPDGF+XoA3yJ6X1NOfhZOrWA==", "type": "package", "path": "sixlabors.fonts/2.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/net6.0/SixLabors.Fonts.dll", "lib/net6.0/SixLabors.Fonts.xml", "sixlabors.fonts.128.png", "sixlabors.fonts.2.1.2.nupkg.sha512", "sixlabors.fonts.nuspec"]}, "SixLabors.ImageSharp/3.1.2": {"sha512": "PYdR6GUI+gW6LBaAQKTik0Ai8oLpFAz3a/KrVusxoTg3kf7F3cuIqKMhJGsuQcmDHCF+iD81Pyn4cexyHrb1ZA==", "type": "package", "path": "sixlabors.imagesharp/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "build/SixLabors.ImageSharp.props", "lib/net6.0/SixLabors.ImageSharp.dll", "lib/net6.0/SixLabors.ImageSharp.xml", "sixlabors.imagesharp.128.png", "sixlabors.imagesharp.3.1.2.nupkg.sha512", "sixlabors.imagesharp.nuspec"]}, "System.Buffers/4.4.0": {"sha512": "AwarXzzoDwX6BgrhjoJsk6tUezZEozOT5Y9QKF94Gl4JK91I4PIIBkBco9068Y9/Dra8Dkbie99kXB8+1BaYKw==", "type": "package", "path": "system.buffers/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "system.buffers.4.4.0.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.CodeDom/7.0.0": {"sha512": "GLltyqEsE5/3IE+zYRP5sNa1l44qKl9v+bfdMcwg+M9qnQf47wK3H0SUR/T+3N4JEQXF3vV4CSuuo0rsg+nq2A==", "type": "package", "path": "system.codedom/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net6.0/System.CodeDom.dll", "lib/net6.0/System.CodeDom.xml", "lib/net7.0/System.CodeDom.dll", "lib/net7.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.7.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.Management/7.0.0": {"sha512": "A4jed4QUviDOm7fJNKAJObEAEkEUXmkGL/w0iyCYTzrl1rezTj8LGFHfsVst4Vb9JwFcTpboiDrvdST48avBpw==", "type": "package", "path": "system.management/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net6.0/System.Management.dll", "lib/net6.0/System.Management.xml", "lib/net7.0/System.Management.dll", "lib/net7.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net6.0/System.Management.dll", "runtimes/win/lib/net6.0/System.Management.xml", "runtimes/win/lib/net7.0/System.Management.dll", "runtimes/win/lib/net7.0/System.Management.xml", "system.management.7.0.0.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}, "System.Numerics.Vectors/4.4.0": {"sha512": "UiLzLW+Lw6HLed1Hcg+8jSRttrbuXv7DANVj0DkL9g6EnnzbL75EB7EWsw5uRbhxd/4YdG8li5XizGWepmG3PQ==", "type": "package", "path": "system.numerics.vectors/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.4.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Private.Uri/4.3.0": {"sha512": "I4SwANiUGho1esj4V4oSlPllXjzCZDE+5XXso2P03LW2vOda2Enzh8DWOxwN6hnrJyp314c7KuVu31QYhRzOGg==", "type": "package", "path": "system.private.uri/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "ref/netstandard/_._", "system.private.uri.4.3.0.nupkg.sha512", "system.private.uri.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.CompilerServices.Unsafe/4.4.0": {"sha512": "9dLLuBxr5GNmOfl2jSMcsHuteEg32BEfUotmmUkmZjpR3RpVHE8YQwt0ow3p6prwA1ME8WqDVZqrr8z6H8G+Kw==", "type": "package", "path": "system.runtime.compilerservices.unsafe/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.4.4.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "Vortice.D3DCompiler/3.3.4": {"sha512": "G0yAN1us091ulIzPfMVJeJf8JX3Tu/Wu0l4zip4HPLfVUWWxsBJF0eaEdwSPrUyz97LEG6zjHruL4sZoNRBHcA==", "type": "package", "path": "vortice.d3dcompiler/3.3.4", "files": [".nupkg.metadata", ".signature.p7s", "build/Vortice.D3DCompiler.BindMapping.xml", "build/Vortice.D3DCompiler.props", "buildMultiTargeting/Vortice.D3DCompiler.props", "lib/net7.0/Vortice.D3DCompiler.dll", "lib/net7.0/Vortice.D3DCompiler.pdb", "lib/net7.0/Vortice.D3DCompiler.xml", "lib/net8.0/Vortice.D3DCompiler.dll", "lib/net8.0/Vortice.D3DCompiler.pdb", "lib/net8.0/Vortice.D3DCompiler.xml", "vortice.d3dcompiler.3.3.4.nupkg.sha512", "vortice.d3dcompiler.nuspec"]}, "Vortice.Direct3D11/3.3.4": {"sha512": "ma8ysRo7WBmJwt+nNpFi0icHKIgNud6Nb0rz6XPN4V/X93nkVZiN3t96cqdy0kC0QtzYlpFccQ2d/JPrvHt6og==", "type": "package", "path": "vortice.direct3d11/3.3.4", "files": [".nupkg.metadata", ".signature.p7s", "build/Vortice.Direct3D11.BindMapping.xml", "build/Vortice.Direct3D11.props", "buildMultiTargeting/Vortice.Direct3D11.props", "lib/net7.0/Vortice.Direct3D11.dll", "lib/net7.0/Vortice.Direct3D11.pdb", "lib/net7.0/Vortice.Direct3D11.xml", "lib/net8.0/Vortice.Direct3D11.dll", "lib/net8.0/Vortice.Direct3D11.pdb", "lib/net8.0/Vortice.Direct3D11.xml", "vortice.direct3d11.3.3.4.nupkg.sha512", "vortice.direct3d11.nuspec"]}, "Vortice.DirectX/3.3.4": {"sha512": "HWvIGYz8XtWZpJW8xGYq5gex/HUPf5u3kDY7w/5WLCXzuR+hqLB7p6tInYePnuVLvfuI/rKMMhlzmnceoP3tIw==", "type": "package", "path": "vortice.directx/3.3.4", "files": [".nupkg.metadata", ".signature.p7s", "build/Vortice.DirectX.BindMapping.xml", "build/Vortice.DirectX.props", "buildMultiTargeting/Vortice.DirectX.props", "lib/net7.0/Vortice.DirectX.dll", "lib/net7.0/Vortice.DirectX.pdb", "lib/net7.0/Vortice.DirectX.xml", "lib/net8.0-windows10.0.19041/Vortice.DirectX.dll", "lib/net8.0-windows10.0.19041/Vortice.DirectX.pdb", "lib/net8.0-windows10.0.19041/Vortice.DirectX.xml", "lib/net8.0/Vortice.DirectX.dll", "lib/net8.0/Vortice.DirectX.pdb", "lib/net8.0/Vortice.DirectX.xml", "vortice.directx.3.3.4.nupkg.sha512", "vortice.directx.nuspec"]}, "Vortice.DXGI/3.3.4": {"sha512": "toya4AmKpcVJyaZzDJnrZlYsVSQxHTTrwWYee3G4Dzthww5HTgtGluN5ciQKl0KOChbR9xjgSK8+flkw6RNdTg==", "type": "package", "path": "vortice.dxgi/3.3.4", "files": [".nupkg.metadata", ".signature.p7s", "build/Vortice.DXGI.BindMapping.xml", "build/Vortice.DXGI.props", "buildMultiTargeting/Vortice.DXGI.props", "lib/net7.0/Vortice.DXGI.dll", "lib/net7.0/Vortice.DXGI.pdb", "lib/net7.0/Vortice.DXGI.xml", "lib/net8.0-windows10.0.19041/Vortice.DXGI.dll", "lib/net8.0-windows10.0.19041/Vortice.DXGI.pdb", "lib/net8.0-windows10.0.19041/Vortice.DXGI.xml", "lib/net8.0/Vortice.DXGI.dll", "lib/net8.0/Vortice.DXGI.pdb", "lib/net8.0/Vortice.DXGI.xml", "vortice.dxgi.3.3.4.nupkg.sha512", "vortice.dxgi.nuspec"]}, "Vortice.Mathematics/1.7.2": {"sha512": "WUUzB3MowHdOqNfSMAms5Ovra+SKgLC4ttH1w75ra3srDGctebSg+aywm7hLdvDn592ydgdbOJN11JKsyAcX+A==", "type": "package", "path": "vortice.mathematics/1.7.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net7.0/Vortice.Mathematics.dll", "lib/net7.0/Vortice.Mathematics.pdb", "lib/net7.0/Vortice.Mathematics.xml", "lib/net8.0-windows10.0.19041/Vortice.Mathematics.dll", "lib/net8.0-windows10.0.19041/Vortice.Mathematics.pdb", "lib/net8.0-windows10.0.19041/Vortice.Mathematics.xml", "lib/net8.0/Vortice.Mathematics.dll", "lib/net8.0/Vortice.Mathematics.pdb", "lib/net8.0/Vortice.Mathematics.xml", "vortice.mathematics.1.7.2.nupkg.sha512", "vortice.mathematics.nuspec"]}, "WinFormsComInterop/0.5.0": {"sha512": "afCh9mbBjXfkTansOWtmCDjdtzudIcFLxRUqrgL3CfaariduFMhAleKyp/QKWP+xSfSzE8FwQWNLqH+bUE0csw==", "type": "package", "path": "winformscominterop/0.5.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/WinFormsComInterop.dll", "lib/net7.0/WinFormsComInterop.dll", "lib/net8.0/WinFormsComInterop.dll", "winformscominterop.0.5.0.nupkg.sha512", "winformscominterop.nuspec"]}}, "projectFileDependencyGroups": {"net7.0-windows7.0": ["ClickableTransparentOverlay >= 9.3.0", "DNNE >= 2.0.6", "GlobalHotKey >= 1.1.0", "Guna.UI2.WinForms >= 2.0.4.6", "Microsoft.DotNet.ILCompiler >= 7.0.20", "Microsoft.NET.ILLink.Analyzers >= 7.0.100-1.23211.1", "Microsoft.NET.ILLink.Tasks >= 7.0.100-1.23211.1", "MinHook.NET >= 1.1.1", "Newtonsoft.Json >= 13.0.3", "SixLabors.Fonts >= 2.1.2", "System.Reflection >= 4.3.0", "WinFormsComInterop >= 0.5.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\NATCHO UP\\AotForms\\Client.csproj", "projectName": "Client", "projectPath": "C:\\Users\\<USER>\\Desktop\\NATCHO UP\\AotForms\\Client.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\NATCHO UP\\AotForms\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "dependencies": {"ClickableTransparentOverlay": {"target": "Package", "version": "[9.3.0, )"}, "DNNE": {"target": "Package", "version": "[2.0.6, )"}, "GlobalHotKey": {"target": "Package", "version": "[1.1.0, )"}, "Guna.UI2.WinForms": {"target": "Package", "version": "[2.0.4.6, )"}, "Microsoft.DotNet.ILCompiler": {"suppressParent": "All", "target": "Package", "version": "[7.0.20, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23211.1, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23211.1, )", "autoReferenced": true}, "MinHook.NET": {"target": "Package", "version": "[1.1.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "SixLabors.Fonts": {"target": "Package", "version": "[2.1.2, )"}, "System.Reflection": {"target": "Package", "version": "[4.3.0, )"}, "WinFormsComInterop": {"target": "Package", "version": "[0.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[7.0.20, 7.0.20]"}, {"name": "runtime.win-x64.Microsoft.DotNet.ILCompiler", "version": "[7.0.20, 7.0.20]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}, "logs": [{"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "Package 'GlobalHotKey 1.1.0' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net7.0-windows7.0'. This package may not be fully compatible with your project.", "libraryId": "GlobalHotKey", "targetGraphs": ["net7.0-windows7.0"]}, {"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "Package 'MinHook.NET 1.1.1' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net7.0-windows7.0'. This package may not be fully compatible with your project.", "libraryId": "MinHook.NET", "targetGraphs": ["net7.0-windows7.0"]}]}