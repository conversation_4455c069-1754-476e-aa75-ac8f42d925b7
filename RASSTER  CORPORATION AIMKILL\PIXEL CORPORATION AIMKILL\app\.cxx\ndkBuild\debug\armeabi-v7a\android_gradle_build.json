{"buildFiles": ["E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Android.mk", "E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Application.mk"], "cleanCommands": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\ndk-build.cmd NDK_PROJECT_PATH=null APP_BUILD_SCRIPT=E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Android.mk NDK_APPLICATION_MK=E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Application.mk APP_ABI=armeabi-v7a NDK_ALL_ABIS=armeabi-v7a NDK_DEBUG=1 APP_PLATFORM=android-21 NDK_OUT=E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj NDK_LIBS_OUT=E:\\aimkill_apk\\BROKEN\\app\\build\\intermediates\\ndkBuild\\debug\\lib clean"], "buildTargetsCommand": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\ndk-build.cmd NDK_PROJECT_PATH=null APP_BUILD_SCRIPT=E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Android.mk NDK_APPLICATION_MK=E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Application.mk APP_ABI=armeabi-v7a NDK_ALL_ABIS=armeabi-v7a NDK_DEBUG=1 APP_PLATFORM=android-21 NDK_OUT=E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj NDK_LIBS_OUT=E:\\aimkill_apk\\BROKEN\\app\\build\\intermediates\\ndkBuild\\debug\\lib {LIST_OF_TARGETS_TO_BUILD}", "libraries": {"hawdawdawdawda-debug-armeabi-v7a": {"buildCommand": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\ndk-build.cmd NDK_PROJECT_PATH=null APP_BUILD_SCRIPT=E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Android.mk NDK_APPLICATION_MK=E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Application.mk APP_ABI=armeabi-v7a NDK_ALL_ABIS=armeabi-v7a NDK_DEBUG=1 APP_PLATFORM=android-21 NDK_OUT=E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj NDK_LIBS_OUT=E:\\aimkill_apk\\BROKEN\\app\\build\\intermediates\\ndkBuild\\debug\\lib E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/libhawdawdawdawda.so", "toolchain": "toolchain-armeabi-v7a", "abi": "armeabi-v7a", "artifactName": "hawdaw<PERSON><PERSON><PERSON><PERSON>da", "files": [{"src": "E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Client.cpp", "flags": "-target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni -DANDROID -Wno-error=format-security -fvisibility=hidden -ffunction-sections -fdata-sections -w -fno-rtti -fno-exceptions -fpermissive -Wformat -Werror=format-security -Wno-error=format-security -fvisibility=hidden -ffunction-sections -fdata-sections -w -Werror -s -std=c++17 -Wno-error=c++11-narrowing -fms-extensions -fno-rtti -fno-exceptions -fpermissive"}, {"src": "E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Tools\\SOCKET\\client.cpp", "flags": "-target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni -DANDROID -Wno-error=format-security -fvisibility=hidden -ffunction-sections -fdata-sections -w -fno-rtti -fno-exceptions -fpermissive -Wformat -Werror=format-security -Wno-error=format-security -fvisibility=hidden -ffunction-sections -fdata-sections -w -Werror -s -std=c++17 -Wno-error=c++11-narrowing -fms-extensions -fno-rtti -fno-exceptions -fpermissive"}], "output": "E:\\aimkill_apk\\BROKEN\\app\\build\\intermediates\\ndkBuild\\debug\\obj\\local\\armeabi-v7a\\libhawdawdawdawda.so"}, "jifjf-debug-armeabi-v7a": {"buildCommand": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\ndk-build.cmd NDK_PROJECT_PATH=null APP_BUILD_SCRIPT=E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Android.mk NDK_APPLICATION_MK=E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Application.mk APP_ABI=armeabi-v7a NDK_ALL_ABIS=armeabi-v7a NDK_DEBUG=1 APP_PLATFORM=android-21 NDK_OUT=E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj NDK_LIBS_OUT=E:\\aimkill_apk\\BROKEN\\app\\build\\intermediates\\ndkBuild\\debug\\lib E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/libjifjf.so", "toolchain": "toolchain-armeabi-v7a", "abi": "armeabi-v7a", "artifactName": "jifjf", "files": [{"src": "E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Server.cpp", "flags": "-target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -mthumb -Oz -DNDEBUG -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni -DANDROID -Wformat -Werror=format-security"}, {"src": "E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Tools\\KittyMemory\\KittyArm64.cpp", "flags": "-target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -mthumb -Oz -DNDEBUG -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni -DANDROID -Wformat -Werror=format-security"}, {"src": "E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Tools\\KittyMemory\\KittyMemory.cpp", "flags": "-target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -mthumb -Oz -DNDEBUG -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni -DANDROID -Wformat -Werror=format-security"}, {"src": "E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Tools\\KittyMemory\\KittyScanner.cpp", "flags": "-target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -mthumb -Oz -DNDEBUG -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni -DANDROID -Wformat -Werror=format-security"}, {"src": "E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Tools\\KittyMemory\\KittyUtils.cpp", "flags": "-target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -mthumb -Oz -DNDEBUG -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni -DANDROID -Wformat -Werror=format-security"}, {"src": "E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Tools\\KittyMemory\\MemoryBackup.cpp", "flags": "-target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -mthumb -Oz -DNDEBUG -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni -DANDROID -Wformat -Werror=format-security"}, {"src": "E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Tools\\KittyMemory\\MemoryPatch.cpp", "flags": "-target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -mthumb -Oz -DNDEBUG -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni -DANDROID -Wformat -Werror=format-security"}, {"src": "E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Tools\\SOCKET\\server.cpp", "flags": "-target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -mthumb -Oz -DNDEBUG -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni -DANDROID -Wformat -Werror=format-security"}], "output": "E:\\aimkill_apk\\BROKEN\\app\\build\\intermediates\\ndkBuild\\debug\\obj\\local\\armeabi-v7a\\libjifjf.so"}}, "toolchains": {"toolchain-armeabi-v7a": {"cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\build\\..\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}