uintptr_t Il2Cpp = 0x0;
uintptr_t InitBase = 0x8CA88B4;
uintptr_t StaticClass = 0x5c;
uintptr_t MatchStatus = 0x3c;
uintptr_t LocalPlayer = 0x44;
uintptr_t DictionaryEntities = 0x68;

//Player
uintptr_t Player_IsDead = 0x4c;
uintptr_t Player_Name = 0x224;
uintptr_t Player_Data = 0x44;
uintptr_t Player_ShadowBase = 0x11d0;
uintptr_t XPose = 0x78;
uintptr_t AvatarManager = 0x3f4;
uintptr_t Avatar = 0x94;
uintptr_t Avatar_IsVisible = 0x7c;
uintptr_t Avatar_Data = 0x10;
uintptr_t Avatar_Data_IsTeam = 0x51;
uintptr_t CurrentMatch = 0x50;

uintptr_t HeadCollider = 0x3d8;

//Camera
uintptr_t FollowCamera = 0x384;
uintptr_t Camera = 0x14;
uintptr_t AimRotation = 0x33c;
uintptr_t MainCameraTransform = 0x194;

//Weapon
uintptr_t Weapon = 0x330;
uintptr_t WeaponData = 0x44;
uintptr_t WeaponRecoil = 0xc;
uintptr_t ViewMatrix = 0xbc;

uintptr_t isBotOffs = 0x1e8;

//VISUAL
uintptr_t Diamantes = 0x30b6e74;
uintptr_t Oro = 0x30b6e64;

//HACK
uintptr_t fastreviveZ = 0x173bd0c;

// Definici�n del enum para los estados de la partida
enum MatchStatusEnum {
    MATCH_NOT_STARTED = 0,
    MATCH_RUNNING = 1,
    MATCH_PAUSED = 2,
    MATCH_ENDED = 3
};