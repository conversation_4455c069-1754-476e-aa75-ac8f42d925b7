﻿using AotForms;
using ImGuiNET;
using System.Collections.Generic;
using System.Diagnostics;
using System.Numerics;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using static AotForms.WinAPI;
using System;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using ImGuiNET;
using ImGuiNET;
using System;
using System.IO;
using Memory;


namespace AotForms
{

    internal class ESP : ClickableTransparentOverlay.Overlay
    {
        IntPtr hWnd;
        IntPtr HDPlayer;
        private const short DefaultMaxHealth = 200; // Default maximum health
                                                    // At the start of your application, maybe inside Init() or LoadFonts():
      


        protected override unsafe void Render()
        {
            if (!Core.HaveMatrix) return;

            CreateHandle();

            var drawList = ImGui.GetForegroundDrawList();


         


            if (Config.Aimfovc)
            {
                DrawSmoothCircle(Config.Aimfov, ColorToUint32(Config.Aimfovcolor), 1.0f);
            }


            // Handle window styles
            string windowName = "Overlay";
            hWnd = FindWindow(null, windowName);
            HDPlayer = FindWindow("BlueStacksApp", null);

            if (hWnd != IntPtr.Zero)
            {
                long extendedStyle = GetWindowLong(hWnd, GWL_EXSTYLE);
                SetWindowLong(hWnd, GWL_EXSTYLE, (extendedStyle | WS_EX_TOOLWINDOW) & ~WS_EX_APPWINDOW);
            }
            else
            {
                Console.WriteLine("The window was not found.");
            }
            var tmp = Core.Entities;
            foreach (var entity in tmp.Values)
            {
                if (entity.IsDead || !entity.IsKnown)
                {
                    continue;
                }
                var dist = Vector3.Distance(Core.LocalMainCamera, entity.Head);

                if (dist > Config.espran) continue;

                var headScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.Head, Core.Width, Core.Height);
                var bottomScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.Root, Core.Width, Core.Height);

                if (headScreenPos.X < 1 || headScreenPos.Y < 1) continue;
                if (bottomScreenPos.X < 1 || bottomScreenPos.Y < 1) continue;

                float CornerHeight = Math.Abs(headScreenPos.Y - bottomScreenPos.Y);
                float CornerWidth = (float)(CornerHeight * 0.65);

               

                if (Config.ESPLine)
                {
                    if (Config.espcfx == false)
                    {
                        Vector2 lineStartPos = Vector2.Zero;

                        // Determine the line starting position based on Config.linePosition
                        switch (Config.linePosition)
                        {
                            case "Up":
                                lineStartPos = new Vector2(Core.Width / 2f, 0f);  // Top-center of the screen
                                break;

                            case "Bottom":
                                lineStartPos = new Vector2(Core.Width / 2f, Core.Height - 0f);  // Bottom-center of the screen
                                break;

                            case "Left":
                                lineStartPos = new Vector2(0f, Core.Height / 2f);  // Left-center of the screen
                                break;

                            case "Right":
                                lineStartPos = new Vector2(Core.Width - 0f, Core.Height / 2f);  // Right-center of the screen
                                break;
                        }

                        // Draw the line from lineStartPos to headScreenPos
                        if (!entity.IsKnocked)
                        {
                            ImGui.GetBackgroundDrawList().AddLine(lineStartPos, headScreenPos, ColorToUint32(Config.ESPLineColor), 1f);
                        }
                    }
                    else
                    {
                        Vector2 lineStartPos = Vector2.Zero;

                        // Same logic as above for line position based on Config.linePosition
                        switch (Config.linePosition)
                        {
                            case "Up":
                                lineStartPos = new Vector2(Core.Width / 2f, 0f);  // Top-center of the screen
                                break;

                            case "Bottom":
                                lineStartPos = new Vector2(Core.Width / 2f, Core.Height - 0f);  // Bottom-center of the screen
                                break;

                            case "Left":
                                lineStartPos = new Vector2(0f, Core.Height / 2f);  // Left-center of the screen
                                break;

                            case "Right":
                                lineStartPos = new Vector2(Core.Width - 0f, Core.Height / 2f);  // Right-center of the screen
                                break;
                        }

                        // Draw the line from lineStartPos to headScreenPos
                        if (!entity.IsKnocked)
                        {
                            ImGui.GetBackgroundDrawList().AddLine(lineStartPos, headScreenPos, ColorToUint32(Config.ESPLineColor), 1f);
                        }
                    }
                }

                if (Config.ESPBox)
                {
                    uint boxColor = ColorToUint32(Config.ESPBoxColor);
                    DrawCorneredBox(headScreenPos.X - (CornerWidth / 2), headScreenPos.Y, CornerWidth, CornerHeight, boxColor, 1f);
                }

                if (Config.ESPFillBox)
                {
                    uint boxColor = ColorToUint32(Color.FromArgb((int)(0.2f * 255), Config.ESPFillBoxColor));
                    DrawFilledBox(headScreenPos.X - (CornerWidth / 2), headScreenPos.Y, CornerWidth, CornerHeight, boxColor);
                }


                // Fonts already loaded in initialization
                // espFont is globally available

                if (Config.ESPBox2)
                {
                    uint boxColor = ColorToUint32(Config.ESPBoxColor);
                    Draw3dBox(headScreenPos.X - (CornerWidth / 2), headScreenPos.Y, CornerWidth, CornerHeight, boxColor, 1f);
                }

                // Determine the name text (upper-cased; fallback to "BOT" if empty)
                var nameText = string.IsNullOrWhiteSpace(entity.Name) ? "BOT" : entity.Name.ToUpper();
                var namePosition = new Vector2(headScreenPos.X - (CornerWidth / 2), headScreenPos.Y - 55);

                // Fixed dimensions for ESP background
                float fixedBgWidth = 136.0f;
                float fixedBgHeight = 18.0f;

                if (Config.espbg)
                {
                    // Define background boundaries
                    Vector2 bgTopLeft = namePosition - new Vector2(8, 3);
                    Vector2 bgBottomRight = namePosition + new Vector2(fixedBgWidth, fixedBgHeight);

                    // Draw the background with black 30% opacity
                    drawList.AddRectFilled(
                        bgTopLeft,
                        bgBottomRight,
                        ColorToUint32(Color.FromArgb(128, 0, 0, 0)) // 50% opacity
                    );

                    // ESP Outline: Soft dark edge
                    drawList.AddRect(
                        bgTopLeft,
                        bgBottomRight,
                        ColorToUint32(Color.FromArgb(220, 30, 20, 10)), // dark brown/blackish outline
                        0.0f,
                        ImDrawFlags.None,
                        0.5f // Slightly more visible
                    );
                }

                if (Config.ESPName)
                {
                    // Prepare text strings.
                    string distanceText = $"{MathF.Round(dist)}M".ToUpper();
                    string healthText = $"{entity.Health}HP";  // Health text appended with "HP"

                    // Calculate text sizes for positioning.
                    Vector2 distanceSize = ImGui.CalcTextSize(distanceText);
                    Vector2 calculatedNameSize = ImGui.CalcTextSize(nameText);
                    Vector2 healthSize = ImGui.CalcTextSize(healthText);

                    // Calculate the background boundaries.
                    float bgLeft = namePosition.X - 8;
                    float bgRight = bgLeft + fixedBgWidth;
                    float bgCenterX = (bgLeft + bgRight) / 2;

                    // For vertical centering within the background.
                    float centerY = namePosition.Y;

                    // Use a horizontal padding value.
                    float paddingX = 4;

                    // Set positions relative to the background:
                    Vector2 distancePosition = new Vector2(bgLeft + paddingX, centerY);               // Left edge
                    Vector2 namePositionAdjusted = new Vector2(bgCenterX - (calculatedNameSize.X / 2), centerY); // Centered
                    Vector2 healthPosition = new Vector2(bgRight - healthSize.X - paddingX, centerY);     // Right edge

                    // Define text colors:
                    uint pureYellow = ColorToUint32(Color.FromArgb(255, 255, 255, 0));  // For distance text
                    uint pureWhite = ColorToUint32(Color.FromArgb(255, 255, 255, 255)); // For name text
                    uint greenText = ColorToUint32(Color.FromArgb(255, 0, 255, 0));       // For health text

                    // Draw the texts:
                    drawList.AddText(distancePosition, pureYellow, distanceText); // Distance text on the left
                    drawList.AddText(namePositionAdjusted, pureWhite, nameText);    // Name text in the center
                    drawList.AddText(healthPosition, greenText, healthText);        // Health text (green) on the right
                }


                if (Config.ESPSkeleton)
                {
                    DrawSkeleton(entity);
                }

                if (Config.ESPHealth)
                {
                    float healthBarHeight = 4;

                    // Health bar width should match the background outline width (with a slight extension)
                    float healthBarWidth = fixedBgWidth + 8;

                    // Position the health bar exactly below the ESP background
                    var healthBarPosition = new Vector2(namePosition.X - 8, namePosition.Y + fixedBgHeight);

                    // Draw the health bar based on whether the entity is knocked or not.
                    if (!entity.IsKnocked)
                    {
                        DrawHealthBar(entity.Health, DefaultMaxHealth, healthBarPosition.X, healthBarPosition.Y, healthBarHeight, healthBarWidth);
                    }
                    else
                    {
                        DrawHealthBar(entity.Health, DefaultMaxHealth, healthBarPosition.X, healthBarPosition.Y, healthBarHeight, healthBarWidth);
                    }
                }
            }
        }


        public void DrawFilledBox(float X, float Y, float W, float H, uint color)
        {
            var vList = ImGui.GetForegroundDrawList();
            vList.AddRectFilled(new Vector2(X, Y), new Vector2(X + W, Y + H), color);
        }

        public void DrawFilledCircle(float centerY, float radius, int numSegments = 64)
        {
            var vList = ImGui.GetBackgroundDrawList();

            // Set the center of the circle at the middle of the screen horizontally (Core.Width / 2f)

            float centerX = Core.Width / 2f;

            uint colorR = ColorToUint32(Color.FromArgb((int)(1f * 255), 225, 0, 0)); // Red color with full opacity
            uint colorG = ColorToUint32(Color.FromArgb((int)(1f * 255), 0, 255, 0)); // LimeGreen color with full opacity

            // Shadow parameters
            float shadowOffset = 1.5f; // The subtle offset of the shadow from the circle
            uint shadowColor = ImGui.ColorConvertFloat4ToU32(new Vector4(0f, 0f, 0f, 1f)); // Semi-transparent black for a soft shadow

            // Draw shadow (a larger circle slightly offset behind the main one)
            vList.AddCircleFilled(new Vector2(centerX, centerY), radius + shadowOffset, shadowColor, numSegments);

            if (Config.AimBot)
            {
                // Draw main circle
                vList.AddCircleFilled(new Vector2(centerX, centerY), radius, colorR, numSegments);
            }
            else
            {
                // Draw main circle
                vList.AddCircleFilled(new Vector2(centerX, centerY), radius, colorG, numSegments);
            }
        }
        public void DrawCorneredBox(float X, float Y, float W, float H, uint color, float thickness)
        {
            var vList = ImGui.GetForegroundDrawList();

            float lineW = W / 3;
            float lineH = H / 3;

            vList.AddLine(new Vector2(X, Y - thickness / 2), new Vector2(X, Y + lineH), color, thickness);
            vList.AddLine(new Vector2(X - thickness / 2, Y), new Vector2(X + lineW, Y), color, thickness);
            vList.AddLine(new Vector2(X + W - lineW, Y), new Vector2(X + W + thickness / 2, Y), color, thickness);
            vList.AddLine(new Vector2(X + W, Y - thickness / 2), new Vector2(X + W, Y + lineH), color, thickness);
            vList.AddLine(new Vector2(X, Y + H - lineH), new Vector2(X, Y + H + thickness / 2), color, thickness);
            vList.AddLine(new Vector2(X - thickness / 2, Y + H), new Vector2(X + lineW, Y + H), color, thickness);
            vList.AddLine(new Vector2(X + W - lineW, Y + H), new Vector2(X + W + thickness / 2, Y + H), color, thickness);
            vList.AddLine(new Vector2(X + W, Y + H - lineH), new Vector2(X + W, Y + H + thickness / 2), color, thickness);
        }

        public void Draw3dBox(float X, float Y, float W, float H, uint color, float thickness)
        {

            var vList = ImGui.GetForegroundDrawList();

            Vector3[] screentions = new Vector3[]
            {
        new Vector3(X, Y, 0),                // Top-left front
        new Vector3(X, Y + H, 0),            // Bottom-left front
        new Vector3(X + W, Y + H, 0),        // Bottom-right front
        new Vector3(X + W, Y, 0),            // Top-right front
        new Vector3(X, Y, -W),               // Top-left back
        new Vector3(X, Y + H, -W),           // Bottom-left back
        new Vector3(X + W, Y + H, -W),       // Bottom-right back
        new Vector3(X + W, Y, -W)            // Top-right back
            };

            // Draw front face
            vList.AddLine(new Vector2(screentions[0].X, screentions[0].Y), new Vector2(screentions[1].X, screentions[1].Y), color, thickness);
            vList.AddLine(new Vector2(screentions[1].X, screentions[1].Y), new Vector2(screentions[2].X, screentions[2].Y), color, thickness);
            vList.AddLine(new Vector2(screentions[2].X, screentions[2].Y), new Vector2(screentions[3].X, screentions[3].Y), color, thickness);
            vList.AddLine(new Vector2(screentions[3].X, screentions[3].Y), new Vector2(screentions[0].X, screentions[0].Y), color, thickness);

            // Draw back face
            vList.AddLine(new Vector2(screentions[4].X, screentions[4].Y), new Vector2(screentions[5].X, screentions[5].Y), color, thickness);
            vList.AddLine(new Vector2(screentions[5].X, screentions[5].Y), new Vector2(screentions[6].X, screentions[6].Y), color, thickness);
            vList.AddLine(new Vector2(screentions[6].X, screentions[6].Y), new Vector2(screentions[7].X, screentions[7].Y), color, thickness);
            vList.AddLine(new Vector2(screentions[7].X, screentions[7].Y), new Vector2(screentions[4].X, screentions[4].Y), color, thickness);

            // Draw connecting lines
            vList.AddLine(new Vector2(screentions[0].X, screentions[0].Y), new Vector2(screentions[4].X, screentions[4].Y), color, thickness);
            vList.AddLine(new Vector2(screentions[1].X, screentions[1].Y), new Vector2(screentions[5].X, screentions[5].Y), color, thickness);
            vList.AddLine(new Vector2(screentions[2].X, screentions[2].Y), new Vector2(screentions[6].X, screentions[6].Y), color, thickness);
            vList.AddLine(new Vector2(screentions[3].X, screentions[3].Y), new Vector2(screentions[7].X, screentions[7].Y), color, thickness);

        }


        private void DrawSkeleton(Entity entity)
        {
            var drawList = ImGui.GetForegroundDrawList();
            uint lineColor = ColorToUint32(Config.ESPSkeletonColor); // Color for the skeleton lines
            uint circleColor = ColorToUint32(Color.Red); // Color for the circle around the head

            // Convert entity positions to screen space
            var headScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.Head, Core.Width, Core.Height);
            var leftWristScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.RightWrist, Core.Width, Core.Height); // Adjust as per actual mapping
            var spineScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.Spine, Core.Width, Core.Height);
            var hipScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.Hip, Core.Width, Core.Height); // Adjust as per actual mapping
            var rootScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.Root, Core.Width, Core.Height);
            var rightCalfScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.RightCalf, Core.Width, Core.Height);
            var leftCalfScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.LeftCalf, Core.Width, Core.Height);
            var rightFootScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.RightFoot, Core.Width, Core.Height);
            var leftFootScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.LeftFoot, Core.Width, Core.Height);
            var rightWristScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.RightWrist, Core.Width, Core.Height);
            var leftHandScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.LeftHand, Core.Width, Core.Height);
            var leftShoulderScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.LeftSholder, Core.Width, Core.Height);
            var rightShoulderScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.RightSholder, Core.Width, Core.Height);
            var rightWristJointScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.RightWristJoint, Core.Width, Core.Height);
            var leftWristJointScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.LeftWristJoint, Core.Width, Core.Height);
            var leftElbowScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.LeftElbow, Core.Width, Core.Height);
            var rightElbowScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.RightElbow, Core.Width, Core.Height); // Adjust if needed

            // Draw skeleton lines


            DrawLine(drawList, spineScreenPos, rightShoulderScreenPos, lineColor); // Spine to Right Shoulder
            DrawLine(drawList, spineScreenPos, hipScreenPos, lineColor);// Spine to hip


            DrawLine(drawList, spineScreenPos, leftShoulderScreenPos, lineColor); // Spine to Left Shoulder
            DrawLine(drawList, leftShoulderScreenPos, rightElbowScreenPos, lineColor); // Left Shoulder to Left Elbow
            DrawLine(drawList, leftElbowScreenPos, rightWristJointScreenPos, lineColor); // Left Elbow to Left Wrist Joint
            // Left Wrist Joint to Left Wrist

            DrawLine(drawList, rightShoulderScreenPos, leftElbowScreenPos, lineColor); // Right Shoulder to Left Elbow
                                                                                       //  DrawLine(drawList, rightElbowScreenPos, leftWristJointScreenPos, lineColor); // Right Elbow to Left Wrist Joint
                                                                                       // Right Wrist Joint to Left Wrist

            DrawLine(drawList, hipScreenPos, rightFootScreenPos, lineColor);// Hip to Right Calf
            DrawLine(drawList, hipScreenPos, leftFootScreenPos, lineColor);// Hip to Left Calf


            // Draw a small circle around the head
            float distance = entity.Distance; // Assume entity.Distance is the distance to the player in game units

            // Calculate the circle radius based on distance (e.g., closer = larger, farther = smaller)
            float baseRadius = 50.0f; // Adjust this base value as needed
            float circleRadius = baseRadius / distance;

            // Draw the circle on the head if the head is visible on screen
            if (headScreenPos.X > 0 && headScreenPos.Y > 0)
            {
                drawList.AddCircle(headScreenPos, circleRadius, circleColor, 30); // 30 segments for the circle
            }

            // Add additional code here to draw the rest of the skeleton using the updated bone positions
        }
        private void DrawLine(ImDrawListPtr drawList, Vector2 startPos, Vector2 endPos, uint color)
        {
            if (startPos.X > 0 && startPos.Y > 0 && endPos.X > 0 && endPos.Y > 0)
            {
                drawList.AddLine(startPos, endPos, color, 1.5f); // Adjust thickness as needed
            }
        }

        public void DrawSmoothCircle(float radius, uint color, float thickness, int segments = 64)
        {
            var vList = ImGui.GetForegroundDrawList();
            var io = ImGui.GetIO();
            float centerX = io.DisplaySize.X / 2;
            float centerY = io.DisplaySize.Y / 2;

            vList.AddCircle(new Vector2(centerX, centerY), radius, color, segments, thickness);
        }

        public void DrawHealthBar(short health, short maxHealth, float X, float Y, float height, float width)
        {
            var vList = ImGui.GetForegroundDrawList();

            // Prevent division by zero and ensure healthPercentage is between 0 and 1
            if (maxHealth <= 0) maxHealth = 100; // Fallback to a default max health
            float healthPercentage = Math.Clamp((float)health / maxHealth, 0f, 1f);
            float healthWidth = width * healthPercentage;

            // Fixed color to green
            Color healthColor = Color.FromArgb(255, 40, 204, 52);

            // Define positions
            Vector2 topLeft = new Vector2(X, Y - height);
            Vector2 bottomRight = new Vector2(X + width, Y);

            // Draw a bright black outline (thicker for visibility)
            vList.AddRectFilled(new Vector2(X - 2, Y - height - 2), new Vector2(X + width + 2, Y + 2), ColorToUint32(Color.Black));

            // Draw the full health bar background (unfilled part)
            vList.AddRectFilled(topLeft, bottomRight, ColorToUint32(Color.FromArgb(50, 0, 0, 0))); // BLACK 1% BG

            // Draw the health portion representing current health
            vList.AddRectFilled(topLeft, new Vector2(X + healthWidth, Y), ColorToUint32(healthColor));
        }


        static uint ColorToUint32(Color color)
        {
            return ImGui.ColorConvertFloat4ToU32(new Vector4(
                color.R / 255.0f,
                color.G / 255.0f,
                color.B / 255.0f,
                color.A / 255.0f));
        }



        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool SetWindowDisplayAffinity(IntPtr hWnd, uint dwAffinity);


        const uint WDA_NONE = 0x00000000;
        const uint WDA_MONITOR = 0x00000001;
        const uint WDA_EXCLUDEFROMCAPTURE = 0x00000011;
        void CreateHandle()
        {
            RECT rect;
            GetWindowRect(Core.Handle, out rect);
            int x = rect.Left;
            int y = rect.Top;
            int width = rect.Right - rect.Left;
            int height = rect.Bottom - rect.Top;
            ImGui.SetWindowSize(new Vector2((float)width, (float)height));
            ImGui.SetWindowPos(new Vector2((float)x, (float)y));
            Size = new Size(width, height);
            Position = new Point(x, y);

            Core.Width = width;
            Core.Height = height;
            if (Config.StreamMode)
            {
                SetWindowDisplayAffinity(hWnd, WDA_EXCLUDEFROMCAPTURE);
            }
            else
            {
                SetWindowDisplayAffinity(hWnd, WDA_NONE);
            }
        }


        public static class ImGuiFontManager
        {
            public static unsafe void LoadFullUnicodeFont()
            {
                var io = ImGui.GetIO();

                // Change this to your font path. This font supports all languages.
                string fontPath = @"C:\Windows\Fonts\NotoSans-Regular.ttf"; // ✅ Google font, best for full Unicode

                if (!File.Exists(fontPath))
                {
                    Console.WriteLine("Font not found at: " + fontPath);
                    return;
                }

                ImFontConfigPtr fontConfig = ImGuiNative.ImFontConfig_ImFontConfig();
                fontConfig.MergeMode = false;
                fontConfig.PixelSnapH = true;

                // Null = full glyph range (supports all languages)
                io.Fonts.AddFontFromFileTTF(fontPath, 18.0f, fontConfig, nint.Zero);

                // Rebuild font atlas
                io.Fonts.Build();
            }
        }
    }
}