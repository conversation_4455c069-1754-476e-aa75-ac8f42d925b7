[{"level": "INFO", "message": "Start JSON generation. Platform version: 21 min SDK version: armeabi-v7a", "file": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "rebuilding JSON C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\.cxx\\ndkBuild\\release\\armeabi-v7a\\android_gradle_build.json due to:", "file": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "- expected json C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\.cxx\\ndkBuild\\release\\armeabi-v7a\\android_gradle_build.json file is not present, will remove stale json folder", "file": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "- missing previous command file C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\.cxx\\ndkBuild\\release\\armeabi-v7a\\build_command.txt, will remove stale json folder", "file": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "- command changed from previous, will remove stale json folder", "file": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "removing stale contents from 'C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\.cxx\\ndkBuild\\release\\armeabi-v7a'", "file": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "created folder 'C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\.cxx\\ndkBuild\\release\\armeabi-v7a'", "file": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "executing ndkBuild Executable : D:\\AIMKILL\\Android\\Sdk\\ndk\\26.1.10909125\\ndk-build.cmd\narguments : \nNDK_PROJECT_PATH=null\nAPP_BUILD_SCRIPT=C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\src\\main\\jni\\Android.mk\nNDK_APPLICATION_MK=C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\src\\main\\jni\\Application.mk\nAPP_ABI=armeabi-v7a\nNDK_ALL_ABIS=armeabi-v7a\nNDK_DEBUG=0\nAPP_PLATFORM=android-21\nNDK_OUT=C:/Users/<USER>/Downloads/BROKEN/app/build/intermediates/ndkBuild/release/obj\nNDK_LIBS_OUT=C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\build\\intermediates\\ndkBuild\\release\\lib\nAPP_SHORT_COMMANDS=false\nLOCAL_SHORT_COMMANDS=false\n-B\n-n\njvmArgs : \n\n", "file": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "Executable : D:\\AIMKILL\\Android\\Sdk\\ndk\\26.1.10909125\\ndk-build.cmd\narguments : \nNDK_PROJECT_PATH=null\nAPP_BUILD_SCRIPT=C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\src\\main\\jni\\Android.mk\nNDK_APPLICATION_MK=C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\src\\main\\jni\\Application.mk\nAPP_ABI=armeabi-v7a\nNDK_ALL_ABIS=armeabi-v7a\nNDK_DEBUG=0\nAPP_PLATFORM=android-21\nNDK_OUT=C:/Users/<USER>/Downloads/BROKEN/app/build/intermediates/ndkBuild/release/obj\nNDK_LIBS_OUT=C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\build\\intermediates\\ndkBuild\\release\\lib\nAPP_SHORT_COMMANDS=false\nLOCAL_SHORT_COMMANDS=false\n-B\n-n\njvmArgs : \n\n", "file": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "JSON generation completed with problem. Exception: Build command failed.\nError while executing process D:\\AIMKILL\\Android\\Sdk\\ndk\\26.1.10909125\\ndk-build.cmd with arguments {NDK_PROJECT_PATH=null APP_BUILD_SCRIPT=C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\src\\main\\jni\\Android.mk NDK_APPLICATION_MK=C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\src\\main\\jni\\Application.mk APP_ABI=armeabi-v7a NDK_ALL_ABIS=armeabi-v7a NDK_DEBUG=0 APP_PLATFORM=android-21 NDK_OUT=C:/Users/<USER>/Downloads/BROKEN/app/build/intermediates/ndkBuild/release/obj NDK_LIBS_OUT=C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\build\\intermediates\\ndkBuild\\release\\lib APP_SHORT_COMMANDS=false LOCAL_SHORT_COMMANDS=false -B -n}\n\nC:\\Users\\<USER>", "file": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}]