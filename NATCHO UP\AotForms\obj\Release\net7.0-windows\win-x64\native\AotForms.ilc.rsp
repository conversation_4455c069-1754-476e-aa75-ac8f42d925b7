obj\Release\net7.0-windows\win-x64\AotForms.dll
-o:obj\Release\net7.0-windows\win-x64\native\AotForms.obj
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\Accessibility.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\DirectWriteForwarder.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\Microsoft.VisualBasic.Forms.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\Microsoft.Win32.Registry.AccessControl.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\Microsoft.Win32.SystemEvents.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\PresentationCore.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\PresentationFramework-SystemCore.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\PresentationFramework-SystemData.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\PresentationFramework-SystemDrawing.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\PresentationFramework-SystemXml.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\PresentationFramework-SystemXmlLinq.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\PresentationFramework.Aero.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\PresentationFramework.Aero2.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\PresentationFramework.AeroLite.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\PresentationFramework.Classic.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\PresentationFramework.Luna.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\PresentationFramework.Royale.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\PresentationFramework.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\PresentationUI.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\ReachFramework.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.CodeDom.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Configuration.ConfigurationManager.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Design.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Diagnostics.EventLog.Messages.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Diagnostics.EventLog.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Diagnostics.PerformanceCounter.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.DirectoryServices.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Drawing.Common.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Drawing.Design.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.IO.Packaging.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Printing.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Resources.Extensions.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Security.Cryptography.Pkcs.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Security.Cryptography.ProtectedData.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Security.Cryptography.Xml.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Security.Permissions.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Threading.AccessControl.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Windows.Controls.Ribbon.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Windows.Extensions.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Windows.Forms.Design.Editors.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Windows.Forms.Design.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Windows.Forms.Primitives.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Windows.Forms.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Windows.Input.Manipulations.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Windows.Presentation.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\System.Xaml.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\UIAutomationClient.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\UIAutomationClientSideProviders.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\UIAutomationProvider.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\UIAutomationTypes.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\WindowsBase.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.windowsdesktop.app.runtime.win-x64\7.0.20\runtimes\win-x64\lib\net7.0\WindowsFormsIntegration.dll
-r:C:\Users\<USER>\.nuget\packages\clickabletransparentoverlay\9.3.0\lib\net7.0\ClickableTransparentOverlay.dll
-r:C:\Users\<USER>\.nuget\packages\guna.ui2.winforms\2.0.4.6\lib\net7.0-windows7.0\Guna.UI2.dll
-r:C:\Users\<USER>\.nuget\packages\imgui.net\1.90.1.1\lib\net6.0\ImGui.NET.dll
-r:C:\Users\<USER>\.nuget\packages\sharpgen.runtime\2.1.2-beta\lib\net7.0\SharpGen.Runtime.dll
-r:C:\Users\<USER>\.nuget\packages\sharpgen.runtime.com\2.1.2-beta\lib\net7.0\SharpGen.Runtime.COM.dll
-r:C:\Users\<USER>\.nuget\packages\sixlabors.imagesharp\3.1.2\lib\net6.0\SixLabors.ImageSharp.dll
-r:C:\Users\<USER>\.nuget\packages\system.management\7.0.0\runtimes\win\lib\net7.0\System.Management.dll
-r:C:\Users\<USER>\.nuget\packages\vortice.d3dcompiler\3.3.4\lib\net7.0\Vortice.D3DCompiler.dll
-r:C:\Users\<USER>\.nuget\packages\vortice.direct3d11\3.3.4\lib\net7.0\Vortice.Direct3D11.dll
-r:C:\Users\<USER>\.nuget\packages\vortice.directx\3.3.4\lib\net7.0\Vortice.DirectX.dll
-r:C:\Users\<USER>\.nuget\packages\vortice.dxgi\3.3.4\lib\net7.0\Vortice.DXGI.dll
-r:C:\Users\<USER>\.nuget\packages\vortice.mathematics\1.7.2\lib\net7.0\Vortice.Mathematics.dll
-r:C:\Users\<USER>\.nuget\packages\winformscominterop\0.5.0\lib\net7.0\WinFormsComInterop.dll
-r:Memory.dll
-r:Memory.dll
-r:Memory.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\Microsoft.CSharp.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\Microsoft.VisualBasic.Core.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\Microsoft.VisualBasic.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\Microsoft.Win32.Primitives.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\Microsoft.Win32.Registry.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\mscorlib.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\netstandard.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.AppContext.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Buffers.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Collections.Concurrent.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Collections.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Collections.Immutable.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Collections.NonGeneric.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Collections.Specialized.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.ComponentModel.Annotations.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.ComponentModel.DataAnnotations.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.ComponentModel.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.ComponentModel.EventBasedAsync.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.ComponentModel.Primitives.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.ComponentModel.TypeConverter.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Configuration.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Console.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Core.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Data.Common.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Data.DataSetExtensions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Data.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Diagnostics.Contracts.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Diagnostics.Debug.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Diagnostics.DiagnosticSource.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Diagnostics.FileVersionInfo.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Diagnostics.Process.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Diagnostics.StackTrace.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Diagnostics.TextWriterTraceListener.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Diagnostics.Tools.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Diagnostics.TraceSource.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Diagnostics.Tracing.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Drawing.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Drawing.Primitives.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Dynamic.Runtime.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Formats.Asn1.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Formats.Tar.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Globalization.Calendars.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Globalization.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Globalization.Extensions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.IO.Compression.Brotli.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.IO.Compression.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.IO.Compression.FileSystem.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.IO.Compression.ZipFile.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.IO.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.IO.FileSystem.AccessControl.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.IO.FileSystem.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.IO.FileSystem.DriveInfo.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.IO.FileSystem.Primitives.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.IO.FileSystem.Watcher.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.IO.IsolatedStorage.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.IO.MemoryMappedFiles.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.IO.Pipes.AccessControl.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.IO.Pipes.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.IO.UnmanagedMemoryStream.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Linq.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Linq.Expressions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Linq.Parallel.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Linq.Queryable.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Memory.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.Http.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.Http.Json.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.HttpListener.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.Mail.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.NameResolution.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.NetworkInformation.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.Ping.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.Primitives.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.Quic.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.Requests.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.Security.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.ServicePoint.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.Sockets.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.WebClient.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.WebHeaderCollection.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.WebProxy.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.WebSockets.Client.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Net.WebSockets.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Numerics.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Numerics.Vectors.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.ObjectModel.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Private.DataContractSerialization.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Private.Uri.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Private.Xml.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Private.Xml.Linq.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Reflection.DispatchProxy.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Reflection.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Reflection.Emit.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Reflection.Emit.ILGeneration.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Reflection.Emit.Lightweight.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Reflection.Extensions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Reflection.Metadata.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Reflection.Primitives.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Reflection.TypeExtensions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Resources.Reader.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Resources.ResourceManager.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Resources.Writer.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Runtime.CompilerServices.Unsafe.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Runtime.CompilerServices.VisualC.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Runtime.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Runtime.Extensions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Runtime.Handles.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Runtime.InteropServices.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Runtime.InteropServices.JavaScript.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Runtime.InteropServices.RuntimeInformation.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Runtime.Intrinsics.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Runtime.Loader.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Runtime.Numerics.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Runtime.Serialization.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Runtime.Serialization.Formatters.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Runtime.Serialization.Json.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Runtime.Serialization.Primitives.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Runtime.Serialization.Xml.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Security.AccessControl.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Security.Claims.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Security.Cryptography.Algorithms.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Security.Cryptography.Cng.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Security.Cryptography.Csp.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Security.Cryptography.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Security.Cryptography.Encoding.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Security.Cryptography.OpenSsl.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Security.Cryptography.Primitives.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Security.Cryptography.X509Certificates.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Security.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Security.Principal.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Security.Principal.Windows.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Security.SecureString.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.ServiceModel.Web.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.ServiceProcess.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Text.Encoding.CodePages.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Text.Encoding.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Text.Encoding.Extensions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Text.Encodings.Web.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Text.Json.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Text.RegularExpressions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Threading.Channels.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Threading.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Threading.Overlapped.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Threading.Tasks.Dataflow.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Threading.Tasks.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Threading.Tasks.Extensions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Threading.Tasks.Parallel.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Threading.Thread.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Threading.ThreadPool.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Threading.Timer.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Transactions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Transactions.Local.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.ValueTuple.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Web.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Web.HttpUtility.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Windows.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Xml.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Xml.Linq.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Xml.ReaderWriter.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Xml.Serialization.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Xml.XDocument.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Xml.XmlDocument.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Xml.XmlSerializer.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Xml.XPath.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\System.Xml.XPath.XDocument.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\framework\WindowsBase.dll
-r:Memory.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\sdk\System.Private.CoreLib.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\sdk\System.Private.DisabledReflection.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\sdk\System.Private.Reflection.Execution.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\sdk\System.Private.StackTraceMetadata.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\sdk\System.Private.TypeLoader.dll
--targetarch:x64
-O
-g
--rdxml:H:\Downloads\Compressed\Source External_2\Source External\Source External\AotForms\rd.xml
--nativelib
--exportsfile:obj\Release\net7.0-windows\win-x64\native\AotForms.def
--initassembly:System.Private.CoreLib
--initassembly:System.Private.StackTraceMetadata
--initassembly:System.Private.TypeLoader
--initassembly:System.Private.Reflection.Execution
--appcontextswitch:Microsoft.Extensions.DependencyInjection.VerifyOpenGenericServiceTrimmability=true
--appcontextswitch:System.ComponentModel.TypeConverter.EnableUnsafeBinaryFormatterInDesigntimeLicenseContextSerialization=false
--appcontextswitch:System.Diagnostics.Tracing.EventSource.IsSupported=false
--appcontextswitch:System.Reflection.Metadata.MetadataUpdater.IsSupported=false
--appcontextswitch:System.Resources.ResourceManager.AllowCustomResourceTypes=true
--appcontextswitch:System.Runtime.InteropServices.BuiltInComInterop.IsSupported=true
--appcontextswitch:System.Runtime.InteropServices.EnableConsumingManagedCodeFromNativeHosting=false
--appcontextswitch:System.Runtime.InteropServices.EnableCppCLIHostActivation=false
--appcontextswitch:System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization=true
--appcontextswitch:System.StartupHookProvider.IsSupported=false
--appcontextswitch:System.Text.Encoding.EnableUnsafeUTF7Encoding=false
--appcontextswitch:System.Threading.Thread.EnableAutoreleasePool=false
--appcontextswitch:RUNTIME_IDENTIFIER=win-x64
--directpinvoke:System.Globalization.Native
--directpinvoke:System.IO.Compression.Native
--directpinvokelist:C:\Users\<USER>\.nuget\packages\microsoft.dotnet.ilcompiler\7.0.20\build\WindowsAPIs.txt
--feature:Microsoft.Extensions.DependencyInjection.VerifyOpenGenericServiceTrimmability=true
--feature:System.ComponentModel.TypeConverter.EnableUnsafeBinaryFormatterInDesigntimeLicenseContextSerialization=false
--feature:System.Diagnostics.Tracing.EventSource.IsSupported=false
--feature:System.Reflection.Metadata.MetadataUpdater.IsSupported=false
--feature:System.Resources.ResourceManager.AllowCustomResourceTypes=true
--feature:System.Runtime.InteropServices.BuiltInComInterop.IsSupported=true
--feature:System.Runtime.InteropServices.EnableConsumingManagedCodeFromNativeHosting=false
--feature:System.Runtime.InteropServices.EnableCppCLIHostActivation=false
--feature:System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization=true
--feature:System.StartupHookProvider.IsSupported=false
--feature:System.Text.Encoding.EnableUnsafeUTF7Encoding=false
--feature:System.Threading.Thread.EnableAutoreleasePool=false
--stacktracedata
--scanreflection
--nowarn:"1701;1702;IL2121;1701;1702"
--singlewarn
--notrimwarn
--root:obj\Release\net7.0-windows\win-x64\AotForms.dll
--nosinglewarnassembly:AotForms
--resilient
--feature:System.Linq.Expressions.CanCompileToIL=false
--feature:System.Linq.Expressions.CanEmitObjectArrayDelegate=false
--feature:System.Linq.Expressions.CanCreateArbitraryDelegates=false
