﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net7.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <PublishAot>true</PublishAot>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
	<BuiltInComInteropSupport>true</BuiltInComInteropSupport>
	<_SuppressWinFormsTrimError>true</_SuppressWinFormsTrimError>
	<Platforms>AnyCPU;x64</Platforms>
	<Product />
  </PropertyGroup>
	    
  <ItemGroup>
    <PackageReference Include="ClickableTransparentOverlay" Version="9.3.0" />
    <PackageReference Include="DNNE" Version="2.0.6" />
    <PackageReference Include="GlobalHotKey" Version="1.1.0" />
    <PackageReference Include="Guna.UI2.WinForms" Version="2.0.4.6" />
    <PackageReference Include="MinHook.NET" Version="1.1.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="SixLabors.Fonts" Version="2.1.2" />
    <PackageReference Include="System.Reflection" Version="4.3.0" />
    <PackageReference Include="WinFormsComInterop" Version="0.5.0" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <None Remove="Properties\n8ow9e.dll" />
    <None Remove="Properties\qm0lnv.dll" />
    <None Remove="rd.xml" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="rd.xml" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Properties\n8ow9e.dll" />
    <EmbeddedResource Include="Properties\qm0lnv.dll" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Helper">
      <HintPath>F:\DLLS ESP LINE PROJECT\Helper.dll</HintPath>
    </Reference>
    <Reference Include="Memory">
      <HintPath>..\..\INSANE BYPASS V2\packages\Memory.dll.x64.1.2.27\lib\netstandard2.0\Memory.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>