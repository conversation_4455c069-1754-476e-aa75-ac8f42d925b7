package com.lkteam;

import android.app.ActionBar;
import android.app.Activity;
import android.os.Bundle;
import android.widget.Toast;

public class MainActivity extends Activity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            Runtime.getRuntime().exec("su");
        } catch (Exception e) {
            Toast.makeText(this, "Root not found!", Toast.LENGTH_SHORT).show();
        }

        ActionBar actionBar = getActionBar();
        actionBar.hide();

        new Login(this);

    }
}