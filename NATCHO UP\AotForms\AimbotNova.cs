﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading.Tasks;
using AotForms;

namespace AotForms
{
    internal static class AimbotNova
    {
        internal static void Work()
        {
            while (true)
            {
                if (!Config.AimbotNova)
                {
                    Thread.Sleep(Config.BuriedMods1);
                    continue;
                }
                if (!Config.AimBotLeft)
                {
                    Thread.Sleep(Config.BuriedMods1);
                    continue;
                }

                if (Core.Width == -1 || Core.Height == -1 || !Core.HaveMatrix)
                {
                    Thread.Sleep(Config.BuriedMods1);
                    continue;
                }

                Entity target = FindBestTarget();
                if (target != null)
                {
                    AimAtTarget(target);
                }
            }
        }

        private static Entity FindBestTarget()
        {
            Entity bestTarget = null;
            float closestDistance = float.MaxValue;
            var screenCenter = new Vector2(Core.Width / 2f, Core.Height / 2f);

            foreach (var entity in Core.Entities.Values)
            {
                if (entity.IsDead) continue;

                if (Config.IgnoreKnocked && entity.IsKnocked) continue;

                var head2D = W2S.WorldToScreen(Core.CameraMatrix, entity.Head, Core.Width, Core.Height);
                if (head2D.X < 1 || head2D.Y < 1) continue;

                float playerDistance = Vector3.Distance(Core.LocalMainCamera, entity.Head);
                if (playerDistance > Config.AimBotMaxDistance) continue;

                var crosshairDistance = Vector2.Distance(screenCenter, head2D);
                if (crosshairDistance < closestDistance && crosshairDistance <= Config.Aimfov)
                {
                    closestDistance = crosshairDistance;
                    bestTarget = entity;
                }
            }

            return bestTarget;
        }

        private static void AimAtTarget(Entity target)
        {
            var playerLook = MathUtils.GetRotationToLocation(target.Head, Config.aimlegit, Core.LocalMainCamera);
            InternalMemory.Write(Core.LocalPlayer + Offsets.AimRotation, playerLook);
            Thread.Sleep(Config.BuriedMods3);
        }
    }
}
