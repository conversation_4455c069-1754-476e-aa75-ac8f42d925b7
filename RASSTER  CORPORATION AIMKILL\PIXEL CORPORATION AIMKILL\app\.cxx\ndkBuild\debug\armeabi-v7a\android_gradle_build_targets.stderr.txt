In file included from E:/aimkill_apk/BROKEN/app/src/main/jni/Server.cpp:14:
E:/aimkill_apk/BROKEN/app/src/main/jni/Tools/Includes/Utils.h:11:10: warning: non-portable path to file '"Unity/Unity.h"'; specified path differs in case from file name on disk [-Wnonportable-include-path]
#include "Unity/unity.h"
         ^~~~~~~~~~~~~~~
         "Unity/Unity.h"
In file included from E:/aimkill_apk/BROKEN/app/src/main/jni/Server.cpp:20:
E:/aimkill_apk/BROKEN/app/src/main/jni/Hack/il2cpp.h:184814:2: warning: declaration does not declare anything [-Wmissing-declarations]
        bool thread_local;
        ^~~~~~~~~~~~~~~~~
E:/aimkill_apk/BROKEN/app/src/main/jni/Hack/il2cpp.h:543667:2: warning: declaration does not declare anything [-Wmissing-declarations]
        float const;
        ^~~~~~~~~~~
In file included from E:/aimkill_apk/BROKEN/app/src/main/jni/Server.cpp:23:
In file included from E:/aimkill_apk/BROKEN/app/src/main/jni/Tools/Includes/Macros.h:24:
E:/aimkill_apk/BROKEN/app/src/main/jni/Tools/Substrate/CydiaSubstrate.h:46:9: warning: '_extern' macro redefined [-Wmacro-redefined]
#define _extern \
        ^
E:/aimkill_apk/BROKEN/app/src/main/jni/Tools/Substrate/SubstrateHook.h:7:9: note: previous definition is here
#define _extern extern "C" __attribute__((__visibility__("hidden")))
        ^
4 warnings generated.
