apply plugin: 'com.android.application'

android {
    compileSdkVersion 33
    defaultConfig {
        applicationId "com.lkteam"
        minSdkVersion 19
        targetSdkVersion 29
        versionCode 1
        versionName "1.0"
        ndk {
            abiFilters 'armeabi-v7a'
        }
    }
    buildTypes {
        release {
            shrinkResources false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            shrinkResources false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    externalNativeBuild {
        ndkBuild {
            path file('src/main/jni/Android.mk')
        }
    }
    ndkVersion '26.1.10909125' // Ajuste conforme necessário
}

//dependencies must be placed below 'android' brackets to get it work on AIDE
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
}
