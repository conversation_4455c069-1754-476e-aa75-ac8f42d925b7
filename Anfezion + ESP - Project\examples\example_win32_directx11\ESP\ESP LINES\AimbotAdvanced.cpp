#include "AimbotAdvanced.hpp"

// Static member definitions
std::vector<AimbotTarget> AimbotAdvanced::validTargets;
AimbotTarget* AimbotAdvanced::currentTarget = nullptr;
std::chrono::steady_clock::time_point AimbotAdvanced::lastTargetTime;

// Aimbot settings
bool AimbotAdvanced::enableSilentAim = false;
bool AimbotAdvanced::enableVisibleAim = false;
bool AimbotAdvanced::enableNovaAim = false;
bool AimbotAdvanced::ignoreKnocked = true;
bool AimbotAdvanced::visibleOnly = true;
float AimbotAdvanced::maxDistance = 300.0f;
float AimbotAdvanced::fovRadius = 60.0f;
float AimbotAdvanced::smoothness = 0.05f;
float AimbotAdvanced::yBias = 0.0f;
int AimbotAdvanced::targetBone = 0;
