1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.lkteam"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="19"
8-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="29" />
9-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.ACCESS_SUPERUSER" />
11-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:5:5-75
11-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:5:22-72
12    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
12-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:6:5-78
12-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:6:22-75
13    <uses-permission android:name="android.permission.INTERNET" />
13-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:7:5-67
13-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:7:22-64
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:8:5-81
14-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:8:22-78
15
16    <application
16-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:9:5-20:19
17        android:allowBackup="true"
17-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:10:9-35
18        android:debuggable="true"
19        android:icon="@mipmap/ic_launcher"
19-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:11:9-43
20        android:label="@string/app_name"
20-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:12:9-41
21        android:supportsRtl="true" >
21-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:13:9-35
22        <activity android:name="com.lkteam.MainActivity" >
22-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:14:9-19:20
22-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:14:19-57
23            <intent-filter>
23-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:15:13-18:29
24                <action android:name="android.intent.action.MAIN" />
24-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:16:17-69
24-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:16:25-66
25
26                <category android:name="android.intent.category.LAUNCHER" />
26-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:17:17-77
26-->E:\aimkill_apk\BROKEN\app\src\main\AndroidManifest.xml:17:27-74
27            </intent-filter>
28        </activity>
29    </application>
30
31</manifest>
