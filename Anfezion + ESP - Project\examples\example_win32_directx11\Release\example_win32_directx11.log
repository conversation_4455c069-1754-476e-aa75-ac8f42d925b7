﻿C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.CppBuild.targets(524,5): warning MSB8004: Output Directory does not end with a trailing slash.  This build instance will add the slash as it is required to allow proper evaluation of the Output Directory.
  imgui.cpp
  imgui_draw.cpp
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_settings.h(149,13): warning C4189: 'screenWidth': local variable is initialized but not referenced
  (compiling source file '../../imgui.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_settings.h(168,20): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '../../imgui.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_settings.h(196,41): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '../../imgui.cpp')
  
  imgui_edited.cpp
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_settings.h(149,13): warning C4189: 'screenWidth': local variable is initialized but not referenced
  (compiling source file '../../imgui_edited.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_settings.h(168,20): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '../../imgui_edited.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_settings.h(196,41): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '../../imgui_edited.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(252,140): warning C4305: 'argument': truncation from 'double' to 'float'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(254,138): warning C4305: 'argument': truncation from 'double' to 'float'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(333,27): warning C4189: 'style': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(377,27): warning C4189: 'style': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(495,132): warning C4305: 'argument': truncation from 'double' to 'float'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(501,123): warning C4305: 'argument': truncation from 'double' to 'float'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(417,37): warning C4100: 'selected': unreferenced parameter
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(424,27): warning C4189: 'style': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(584,21): warning C4189: 'icon_radius': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(891,139): warning C4305: 'argument': truncation from 'double' to 'float'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(892,140): warning C4305: 'argument': truncation from 'double' to 'float'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(815,27): warning C4189: 'style': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(933,140): warning C4305: 'argument': truncation from 'double' to 'float'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(951,138): warning C4305: 'argument': truncation from 'double' to 'float'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(925,83): warning C4189: 'pressed': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(949,21): warning C4189: 'value_buf_end': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(926,14): warning C4189: 'temp_input_is_active': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1002,17): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1002,31): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1051,136): warning C4305: 'argument': truncation from 'double' to 'float'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1064,134): warning C4305: 'argument': truncation from 'double' to 'float'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1016,129): warning C4100: 'flags': unreferenced parameter
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1038,29): warning C4189: 'pressed': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1087,14): warning C4189: 'ret': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1106,23): warning C4189: 'g': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1620,140): warning C4305: 'argument': truncation from 'double' to 'float'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1631,138): warning C4305: 'argument': truncation from 'double' to 'float'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1628,21): warning C4189: 'value_buf_end': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1699,222): warning C4244: 'argument': conversion from 'float' to 'ImDrawFlags', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1702,222): warning C4244: 'argument': conversion from 'float' to 'ImDrawFlags', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1691,21): warning C4189: 'value_buf_end1': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1684,20): warning C4189: 'value_changed1': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1668,29): warning C4189: 'pressed': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1690,21): warning C4189: 'value_buf_end0': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1670,14): warning C4189: 'temp_input_is_active': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1771,24): warning C4189: 'state': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1763,27): warning C4189: 'style': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1802,110): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1803,130): warning C4244: 'argument': conversion from 'float' to 'ImDrawFlags', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1805,125): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1800,24): warning C4189: 'state': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1791,27): warning C4189: 'style': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1967,28): warning C4456: declaration of 'col_v4' hides previous local declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1930,26):
      see declaration of 'col_v4'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2016,125): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2060,30): warning C4456: declaration of 'i' hides previous local declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(1871,13):
      see declaration of 'i'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2092,172): warning C4305: 'argument': truncation from 'double' to 'float'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2093,170): warning C4305: 'argument': truncation from 'double' to 'float'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2025,33): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2027,33): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2281,95): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2302,101): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2120,96): warning C4100: 'ref_col': unreferenced parameter
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2153,15): warning C4189: 'square_sz': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2158,15): warning C4189: 'bars_triangles_half_sz': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2257,21): warning C4189: 'col_midgrey': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2316,103): warning C4100: 'size_arg': unreferenced parameter
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2323,21): warning C4189: 'default_size': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2321,23): warning C4189: 'g': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2446,79): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2363,288): warning C4100: 'hit': unreferenced parameter
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2363,253): warning C4100: 'money': unreferenced parameter
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2363,224): warning C4100: 'c4': unreferenced parameter
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2363,191): warning C4100: 'bomb': unreferenced parameter
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2363,158): warning C4100: 'zoom': unreferenced parameter
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2363,93): warning C4100: 'weapon': unreferenced parameter
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2363,56): warning C4100: 'nickname': unreferenced parameter
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_edited.cpp(2103,17): warning C4505: 'edited::RenderArrowsForVerticalBar': unreferenced function with internal linkage has been removed
  imgui_freetype.cpp
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_freetype.cpp(1,1): warning C4828: The file contains a character starting at offset 0xf69 that is illegal in the current source character set (codepage 65001).
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_freetype.cpp(1,1): warning C4828: The file contains a character starting at offset 0xf6e that is illegal in the current source character set (codepage 65001).
  imgui_tables.cpp
  imgui_widgets.cpp
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_settings.h(149,13): warning C4189: 'screenWidth': local variable is initialized but not referenced
  (compiling source file '../../imgui_widgets.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_settings.h(168,20): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '../../imgui_widgets.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_settings.h(196,41): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '../../imgui_widgets.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_widgets.cpp(305,168): warning C4244: 'argument': conversion from 'float' to 'ImDrawFlags', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_widgets.cpp(293,19): warning C4189: 'id': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_widgets.cpp(1110,17): warning C4189: 'grab_col': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_widgets.cpp(1572,19): warning C4189: 'g': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_widgets.cpp(1832,97): warning C4100: 'value': unreferenced parameter
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_widgets.cpp(1832,86): warning C4100: 'flags': unreferenced parameter
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_widgets.cpp(1866,10): warning C4189: 'pressed': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_widgets.cpp(1837,30): warning C4189: 'backup_next_window_data_flags': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_widgets.cpp(3176,17): warning C4189: 'value_buf_end': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_widgets.cpp(4154,13): warning C4505: 'InputTextFilterCharacter2': unreferenced function with internal linkage has been removed
  imgui_impl_dx11.cpp
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(12,9): warning C4005: 'DXGI_STATUS_OCCLUDED': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(58984,9):
      see previous definition of 'DXGI_STATUS_OCCLUDED'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(13,9): warning C4005: 'DXGI_STATUS_CLIPPED': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(58993,9):
      see previous definition of 'DXGI_STATUS_CLIPPED'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(14,9): warning C4005: 'DXGI_STATUS_NO_REDIRECTION': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59002,9):
      see previous definition of 'DXGI_STATUS_NO_REDIRECTION'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(15,9): warning C4005: 'DXGI_STATUS_NO_DESKTOP_ACCESS': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59011,9):
      see previous definition of 'DXGI_STATUS_NO_DESKTOP_ACCESS'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(16,9): warning C4005: 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59020,9):
      see previous definition of 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(17,9): warning C4005: 'DXGI_STATUS_MODE_CHANGED': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59029,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGED'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(18,9): warning C4005: 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59038,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(21,9): warning C4005: 'DXGI_ERROR_INVALID_CALL': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59053,9):
      see previous definition of 'DXGI_ERROR_INVALID_CALL'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(22,9): warning C4005: 'DXGI_ERROR_NOT_FOUND': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59062,9):
      see previous definition of 'DXGI_ERROR_NOT_FOUND'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(23,9): warning C4005: 'DXGI_ERROR_MORE_DATA': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59071,9):
      see previous definition of 'DXGI_ERROR_MORE_DATA'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(24,9): warning C4005: 'DXGI_ERROR_UNSUPPORTED': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59080,9):
      see previous definition of 'DXGI_ERROR_UNSUPPORTED'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(25,9): warning C4005: 'DXGI_ERROR_DEVICE_REMOVED': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59089,9):
      see previous definition of 'DXGI_ERROR_DEVICE_REMOVED'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(26,9): warning C4005: 'DXGI_ERROR_DEVICE_HUNG': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59098,9):
      see previous definition of 'DXGI_ERROR_DEVICE_HUNG'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(27,9): warning C4005: 'DXGI_ERROR_DEVICE_RESET': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59108,9):
      see previous definition of 'DXGI_ERROR_DEVICE_RESET'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(28,9): warning C4005: 'DXGI_ERROR_WAS_STILL_DRAWING': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59117,9):
      see previous definition of 'DXGI_ERROR_WAS_STILL_DRAWING'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(29,9): warning C4005: 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59127,9):
      see previous definition of 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(30,9): warning C4005: 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59136,9):
      see previous definition of 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(31,9): warning C4005: 'DXGI_ERROR_DRIVER_INTERNAL_ERROR': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59146,9):
      see previous definition of 'DXGI_ERROR_DRIVER_INTERNAL_ERROR'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(32,9): warning C4005: 'DXGI_ERROR_NONEXCLUSIVE': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59155,9):
      see previous definition of 'DXGI_ERROR_NONEXCLUSIVE'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(33,9): warning C4005: 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59164,9):
      see previous definition of 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(34,9): warning C4005: 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59174,9):
      see previous definition of 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(35,9): warning C4005: 'DXGI_ERROR_REMOTE_OUTOFMEMORY': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59183,9):
      see previous definition of 'DXGI_ERROR_REMOTE_OUTOFMEMORY'
  
C:\Users\<USER>\Desktop\SDK\Include\d3d11.h(917,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59498,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
C:\Users\<USER>\Desktop\SDK\Include\d3d11.h(918,9): warning C4005: 'D3D11_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59507,9):
      see previous definition of 'D3D11_ERROR_FILE_NOT_FOUND'
  
C:\Users\<USER>\Desktop\SDK\Include\d3d11.h(919,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59517,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS'
  
C:\Users\<USER>\Desktop\SDK\Include\d3d11.h(920,9): warning C4005: 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59526,9):
      see previous definition of 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD'
  
C:\Users\<USER>\Desktop\SDK\Include\d3d10.h(608,9): warning C4005: 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59474,9):
      see previous definition of 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
C:\Users\<USER>\Desktop\SDK\Include\d3d10.h(609,9): warning C4005: 'D3D10_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file '../../backends/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59483,9):
      see previous definition of 'D3D10_ERROR_FILE_NOT_FOUND'
  
  imgui_impl_win32.cpp
  wnetwrap.cpp
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(41,25): warning C4244: 'argument': conversion from 'int' to '_Elem', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(41,25): warning C4244:         with
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(41,25): warning C4244:         [
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(41,25): warning C4244:             _Elem=char
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(41,25): warning C4244:         ]
  (compiling source file 'AuthDLL/wnetwrap.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(45,25): warning C4244: 'argument': conversion from 'int' to '_Elem', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(45,25): warning C4244:         with
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(45,25): warning C4244:         [
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(45,25): warning C4244:             _Elem=char
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(45,25): warning C4244:         ]
  (compiling source file 'AuthDLL/wnetwrap.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(68,144): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(69,144): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(113,108): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(113,61): warning C4245: 'argument': conversion from 'long' to 'DWORD', signed/unsigned mismatch
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(146,44): warning C4244: '=': conversion from 'int' to '_Elem', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(146,44): warning C4244:         with
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(146,44): warning C4244:         [
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(146,44): warning C4244:             _Elem=char
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(146,44): warning C4244:         ]
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(323,20): warning C4245: 'initializing': conversion from 'int' to 'DWORD', signed/unsigned mismatch
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(415,23): warning C4244: '=': conversion from 'int' to '_Elem', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(415,23): warning C4244:         with
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(415,23): warning C4244:         [
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(415,23): warning C4244:             _Elem=char
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(415,23): warning C4244:         ]
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(752,128): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(752,81): warning C4245: 'argument': conversion from 'long' to 'DWORD', signed/unsigned mismatch
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(768,7): warning C4189: 'store': local variable is initialized but not referenced
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(839,28): warning C4996: 'std::codecvt_utf8_utf16<wchar_t,1114111,(std::codecvt_mode)0>': warning STL4017: std::wbuffer_convert, std::wstring_convert, and the <codecvt> header (containing std::codecvt_mode, std::codecvt_utf8, std::codecvt_utf16, and std::codecvt_utf8_utf16) are deprecated in C++17. (The std::codecvt class template is NOT deprecated.) The C++ Standard doesn't provide equivalent non-deprecated functionality; consider using MultiByteToWideChar() and WideCharToMultiByte() from <Windows.h> instead. You can define _SILENCE_CXX17_CODECVT_HEADER_DEPRECATION_WARNING or _SILENCE_ALL_CXX17_DEPRECATION_WARNINGS to suppress this warning.
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\codecvt(442,39):
      see declaration of 'std::codecvt_utf8_utf16'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(839,7): warning C4996: 'std::wstring_convert<std::codecvt_utf8_utf16<wchar_t,1114111,(std::codecvt_mode)0>,wchar_t,std::allocator<wchar_t>,std::allocator<char>>': warning STL4017: std::wbuffer_convert, std::wstring_convert, and the <codecvt> header (containing std::codecvt_mode, std::codecvt_utf8, std::codecvt_utf16, and std::codecvt_utf8_utf16) are deprecated in C++17. (The std::codecvt class template is NOT deprecated.) The C++ Standard doesn't provide equivalent non-deprecated functionality; consider using MultiByteToWideChar() and WideCharToMultiByte() from <Windows.h> instead. You can define _SILENCE_CXX17_CODECVT_HEADER_DEPRECATION_WARNING or _SILENCE_ALL_CXX17_DEPRECATION_WARNINGS to suppress this warning.
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xlocbuf(303,39):
      see declaration of 'std::wstring_convert'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(839,57): warning C4996: 'std::wstring_convert<std::codecvt_utf8_utf16<wchar_t,1114111,(std::codecvt_mode)0>,wchar_t,std::allocator<wchar_t>,std::allocator<char>>::wstring_convert': warning STL4017: std::wbuffer_convert, std::wstring_convert, and the <codecvt> header (containing std::codecvt_mode, std::codecvt_utf8, std::codecvt_utf16, and std::codecvt_utf8_utf16) are deprecated in C++17. (The std::codecvt class template is NOT deprecated.) The C++ Standard doesn't provide equivalent non-deprecated functionality; consider using MultiByteToWideChar() and WideCharToMultiByte() from <Windows.h> instead. You can define _SILENCE_CXX17_CODECVT_HEADER_DEPRECATION_WARNING or _SILENCE_ALL_CXX17_DEPRECATION_WARNINGS to suppress this warning.
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(841,31): warning C4996: 'std::wstring_convert<std::codecvt_utf8_utf16<wchar_t,1114111,(std::codecvt_mode)0>,wchar_t,std::allocator<wchar_t>,std::allocator<char>>::from_bytes': warning STL4017: std::wbuffer_convert, std::wstring_convert, and the <codecvt> header (containing std::codecvt_mode, std::codecvt_utf8, std::codecvt_utf16, and std::codecvt_utf8_utf16) are deprecated in C++17. (The std::codecvt class template is NOT deprecated.) The C++ Standard doesn't provide equivalent non-deprecated functionality; consider using MultiByteToWideChar() and WideCharToMultiByte() from <Windows.h> instead. You can define _SILENCE_CXX17_CODECVT_HEADER_DEPRECATION_WARNING or _SILENCE_ALL_CXX17_DEPRECATION_WARNINGS to suppress this warning.
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.cpp(851,34): warning C4996: 'std::wstring_convert<std::codecvt_utf8_utf16<wchar_t,1114111,(std::codecvt_mode)0>,wchar_t,std::allocator<wchar_t>,std::allocator<char>>::to_bytes': warning STL4017: std::wbuffer_convert, std::wstring_convert, and the <codecvt> header (containing std::codecvt_mode, std::codecvt_utf8, std::codecvt_utf16, and std::codecvt_utf8_utf16) are deprecated in C++17. (The std::codecvt class template is NOT deprecated.) The C++ Standard doesn't provide equivalent non-deprecated functionality; consider using MultiByteToWideChar() and WideCharToMultiByte() from <Windows.h> instead. You can define _SILENCE_CXX17_CODECVT_HEADER_DEPRECATION_WARNING or _SILENCE_ALL_CXX17_DEPRECATION_WARNINGS to suppress this warning.
  Discord.cpp
  connection_win.cpp
  discord_register_win.cpp
  discord_rpc.cpp
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1955,31): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/discord_rpc.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1955,31):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\serialization.h(160,29):
          see reference to class template instantiation 'rapidjson::GenericDocument<UTF8,PoolAllocator,StackAllocator>' being compiled
          C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(2154,32):
          see reference to class template instantiation 'rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>>' being compiled
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1956,33): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/discord_rpc.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1957,38): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/discord_rpc.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1958,39): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/discord_rpc.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1959,40): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/discord_rpc.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1960,41): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/discord_rpc.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1961,41): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/discord_rpc.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1962,38): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/discord_rpc.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1963,40): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/discord_rpc.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1964,39): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/discord_rpc.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1965,40): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/discord_rpc.cpp')
  
  rpc_connection.cpp
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1955,31): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/rpc_connection.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1955,31):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\serialization.h(160,29):
          see reference to class template instantiation 'rapidjson::GenericDocument<UTF8,PoolAllocator,StackAllocator>' being compiled
          C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(2154,32):
          see reference to class template instantiation 'rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>>' being compiled
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1956,33): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/rpc_connection.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1957,38): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/rpc_connection.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1958,39): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/rpc_connection.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1959,40): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/rpc_connection.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1960,41): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/rpc_connection.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1961,41): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/rpc_connection.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1962,38): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/rpc_connection.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1963,40): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/rpc_connection.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1964,39): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/rpc_connection.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1965,40): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/rpc_connection.cpp')
  
  serialization.cpp
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1955,31): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/serialization.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1955,31):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\serialization.h(160,29):
          see reference to class template instantiation 'rapidjson::GenericDocument<UTF8,PoolAllocator,StackAllocator>' being compiled
          C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(2154,32):
          see reference to class template instantiation 'rapidjson::GenericValue<rapidjson::UTF8<char>,rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>>' being compiled
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1956,33): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/serialization.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1957,38): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/serialization.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1958,39): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/serialization.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1959,40): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/serialization.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1960,41): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/serialization.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1961,41): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/serialization.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1962,38): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/serialization.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1963,40): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/serialization.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1964,39): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/serialization.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\DiscordSDK\src\rapidjson\document.h(1965,40): warning C5054: operator '|': deprecated between enumerations of different types
  (compiling source file 'DiscordSDK/src/serialization.cpp')
  
  main.cpp
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_settings.h(149,13): warning C4189: 'screenWidth': local variable is initialized but not referenced
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_settings.h(168,20): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_settings.h(196,41): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(12,9): warning C4005: 'DXGI_STATUS_OCCLUDED': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(58984,9):
      see previous definition of 'DXGI_STATUS_OCCLUDED'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(13,9): warning C4005: 'DXGI_STATUS_CLIPPED': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(58993,9):
      see previous definition of 'DXGI_STATUS_CLIPPED'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(14,9): warning C4005: 'DXGI_STATUS_NO_REDIRECTION': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59002,9):
      see previous definition of 'DXGI_STATUS_NO_REDIRECTION'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(15,9): warning C4005: 'DXGI_STATUS_NO_DESKTOP_ACCESS': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59011,9):
      see previous definition of 'DXGI_STATUS_NO_DESKTOP_ACCESS'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(16,9): warning C4005: 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59020,9):
      see previous definition of 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(17,9): warning C4005: 'DXGI_STATUS_MODE_CHANGED': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59029,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGED'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(18,9): warning C4005: 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59038,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(21,9): warning C4005: 'DXGI_ERROR_INVALID_CALL': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59053,9):
      see previous definition of 'DXGI_ERROR_INVALID_CALL'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(22,9): warning C4005: 'DXGI_ERROR_NOT_FOUND': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59062,9):
      see previous definition of 'DXGI_ERROR_NOT_FOUND'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(23,9): warning C4005: 'DXGI_ERROR_MORE_DATA': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59071,9):
      see previous definition of 'DXGI_ERROR_MORE_DATA'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(24,9): warning C4005: 'DXGI_ERROR_UNSUPPORTED': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59080,9):
      see previous definition of 'DXGI_ERROR_UNSUPPORTED'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(25,9): warning C4005: 'DXGI_ERROR_DEVICE_REMOVED': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59089,9):
      see previous definition of 'DXGI_ERROR_DEVICE_REMOVED'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(26,9): warning C4005: 'DXGI_ERROR_DEVICE_HUNG': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59098,9):
      see previous definition of 'DXGI_ERROR_DEVICE_HUNG'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(27,9): warning C4005: 'DXGI_ERROR_DEVICE_RESET': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59108,9):
      see previous definition of 'DXGI_ERROR_DEVICE_RESET'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(28,9): warning C4005: 'DXGI_ERROR_WAS_STILL_DRAWING': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59117,9):
      see previous definition of 'DXGI_ERROR_WAS_STILL_DRAWING'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(29,9): warning C4005: 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59127,9):
      see previous definition of 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(30,9): warning C4005: 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59136,9):
      see previous definition of 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(31,9): warning C4005: 'DXGI_ERROR_DRIVER_INTERNAL_ERROR': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59146,9):
      see previous definition of 'DXGI_ERROR_DRIVER_INTERNAL_ERROR'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(32,9): warning C4005: 'DXGI_ERROR_NONEXCLUSIVE': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59155,9):
      see previous definition of 'DXGI_ERROR_NONEXCLUSIVE'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(33,9): warning C4005: 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59164,9):
      see previous definition of 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(34,9): warning C4005: 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59174,9):
      see previous definition of 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED'
  
C:\Users\<USER>\Desktop\SDK\Include\dxgitype.h(35,9): warning C4005: 'DXGI_ERROR_REMOTE_OUTOFMEMORY': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59183,9):
      see previous definition of 'DXGI_ERROR_REMOTE_OUTOFMEMORY'
  
C:\Users\<USER>\Desktop\SDK\Include\d3d11.h(917,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59498,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
C:\Users\<USER>\Desktop\SDK\Include\d3d11.h(918,9): warning C4005: 'D3D11_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59507,9):
      see previous definition of 'D3D11_ERROR_FILE_NOT_FOUND'
  
C:\Users\<USER>\Desktop\SDK\Include\d3d11.h(919,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59517,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS'
  
C:\Users\<USER>\Desktop\SDK\Include\d3d11.h(920,9): warning C4005: 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59526,9):
      see previous definition of 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD'
  
C:\Users\<USER>\Desktop\SDK\Include\d3d10.h(608,9): warning C4005: 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59474,9):
      see previous definition of 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
C:\Users\<USER>\Desktop\SDK\Include\d3d10.h(609,9): warning C4005: 'D3D10_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59483,9):
      see previous definition of 'D3D10_ERROR_FILE_NOT_FOUND'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Offset.hpp(1,1): warning C4828: The file contains a character starting at offset 0x407 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Unity.hh(1,1): warning C4828: The file contains a character starting at offset 0x3d1 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Unity.hh(1,1): warning C4828: The file contains a character starting at offset 0x701 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Unity.hh(1,1): warning C4828: The file contains a character starting at offset 0x1af9 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Unity.hh(1,1): warning C4828: The file contains a character starting at offset 0x1b11 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector2.hpp(12,9): warning C4201: nonstandard extension used: nameless struct/union
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector2.hpp(297,13): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector2.hpp(298,13): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector2.hpp(451,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector2.hpp(452,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0x58c that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0x59b that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0x5f0 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0x6d9 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0x70e that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0x9c2 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0x9c9 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0xa17 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0xa2f that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0xa8a that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(1,1): warning C4828: The file contains a character starting at offset 0xabe that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(13,9): warning C4201: nonstandard extension used: nameless struct/union
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(341,13): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(342,13): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(494,72): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(494,66): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(494,60): warning C4305: 'argument': truncation from 'double' to 'float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(522,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(523,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(540,13): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(541,13): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(596,21): warning C4312: 'type cast': conversion from 'int' to 'const char *' of greater size
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(597,21): warning C4312: 'type cast': conversion from 'int' to 'const char *' of greater size
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Vector3.hpp(598,21): warning C4312: 'type cast': conversion from 'int' to 'const char *' of greater size
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(94,9): warning C4201: nonstandard extension used: nameless struct/union
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(343,37): warning C4244: 'return': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(375,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(376,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(377,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(378,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(379,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(380,14): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(450,45): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(451,21): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(458,68): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(459,21): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(466,68): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(467,21): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(474,68): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(475,21): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(502,27): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(503,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(577,15): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(585,15): warning C4305: '=': truncation from 'double' to 'float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Quaternion.hpp(693,36): warning C4244: 'argument': conversion from 'double' to 'const float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\ADBZapi.hpp(72,27): warning C4127: conditional expression is constant
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\ADBZapi.hpp(72,27):
      consider using 'if constexpr' statement instead
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\ADBZapi.hpp(127,66): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\ADBZapi.hpp(198,66): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(1,1): warning C4828: The file contains a character starting at offset 0x27a that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(1,1): warning C4828: The file contains a character starting at offset 0x2cc that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(1,1): warning C4828: The file contains a character starting at offset 0x42a that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(1,1): warning C4828: The file contains a character starting at offset 0x44c that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(1,1): warning C4828: The file contains a character starting at offset 0x4b9 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(1,1): warning C4828: The file contains a character starting at offset 0x4bd that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(1,1): warning C4828: The file contains a character starting at offset 0x4cb that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(92,24): warning C4459: declaration of 'pVM' hides global declaration
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(90,7):
      see declaration of 'pVM'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(196,13): warning C4189: 'currentCacheValue': local variable is initialized but not referenced
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\PicoMem.h(117,36): warning C4100: 'procname': unreferenced parameter
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\PicoMem.h(130,25): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\PicoMem.h(163,25): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\PicoMem.h(171,19): warning C4101: 'OldProtect': unreferenced local variable
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\PicoMem.h(180,19): warning C4101: 'OldProtect': unreferenced local variable
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\PicoMem.h(267,24): warning C4244: 'return': conversion from 'DWORD_PTR' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\PicoMem.h(285,27): warning C4459: declaration of 'search' hides global declaration
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\imgui_settings.h(52,13):
      see declaration of 'search'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(41,25): warning C4244: 'argument': conversion from 'int' to '_Elem', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(41,25): warning C4244:         with
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(41,25): warning C4244:         [
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(41,25): warning C4244:             _Elem=char
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(41,25): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(45,25): warning C4244: 'argument': conversion from 'int' to '_Elem', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(45,25): warning C4244:         with
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(45,25): warning C4244:         [
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(45,25): warning C4244:             _Elem=char
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\wnetwrap.h(45,25): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\json.hpp(16259,1): warning C4828: The file contains a character starting at offset 0x9fba3 that is illegal in the current source character set (codepage 65001).
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(123,13): warning C4996: 'strncpy': This function or variable may be unsafe. Consider using strncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(126,13): warning C4996: 'strncpy': This function or variable may be unsafe. Consider using strncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(168,30): warning C4459: declaration of 'name' hides global declaration
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\Header.h(11,13):
      see declaration of 'name'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(178,30): warning C4100: 'p': unreferenced parameter
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(264,60): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(266,34): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(267,40): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(268,38): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(270,46): warning C4244: '=': conversion from 'int' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(389,24): warning C4996: 'localtime': This function or variable may be unsafe. Consider using localtime_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(401,22): warning C4459: declaration of 'hwnd' hides global declaration
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(77,6):
      see declaration of 'hwnd'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(508,21): warning C4459: declaration of 'name' hides global declaration
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\AuthDLL\Header.h(11,13):
      see declaration of 'name'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(539,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(549,15): warning C4189: 'color2': local variable is initialized but not referenced
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(564,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(574,15): warning C4189: 'color2': local variable is initialized but not referenced
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(743,17): warning C4189: 'backup': local variable is initialized but not referenced
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(76,12): error C2011: 'AimKey': 'enum' type redefinition
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(32,12):
      see declaration of 'AimKey'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(85,15): error C2370: 'selectedKey': redefinition; different storage class
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(33,8):
      see declaration of 'selectedKey'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(85,37): error C2027: use of undefined type 'AimKey'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(32,12):
      see declaration of 'AimKey'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(85,37): error C2065: 'RightMouseButton': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(88,20): error C2065: 'AimbotTarget': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(88,13): error C2923: 'std::vector': 'AimbotTarget' is not a valid template type argument for parameter '_Ty'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(88,20):
      see declaration of 'AimbotTarget'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(88,13): error C2976: 'std::vector': too few template arguments
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(442,7):
      see declaration of 'std::vector'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(88,34): error C2641: cannot deduce template arguments for 'std::vector'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(88,34): error C2783: 'std::vector<_Ty,_Alloc> std::vector(void) noexcept(<expr>)': could not deduce template argument for '_Ty'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(670,5):
      see declaration of 'std::vector'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(88,34): error C2780: 'std::vector<_Ty,_Alloc> std::vector(std::vector<_Ty,_Alloc>)': expects 1 arguments - 0 provided
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(442,7):
      see declaration of 'std::vector'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(89,8): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(89,21): error C2146: syntax error: missing ';' before identifier 'bestTarget'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(143,8): error C2374: 'lineColorEnemy': redefinition; multiple initialization
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(5,8):
      see declaration of 'lineColorEnemy'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(143,8): error C2086: 'ImVec4 lineColorEnemy': redefinition
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(5,8):
      see declaration of 'lineColorEnemy'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(144,8): error C2374: 'boxColor': redefinition; multiple initialization
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(3,8):
      see declaration of 'boxColor'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(144,8): error C2086: 'ImVec4 boxColor': redefinition
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(3,8):
      see declaration of 'boxColor'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(145,8): error C2374: 'bonesColor': redefinition; multiple initialization
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(8,8):
      see declaration of 'bonesColor'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(145,8): error C2086: 'ImVec4 bonesColor': redefinition
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(8,8):
      see declaration of 'bonesColor'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(146,8): error C2374: 'boxColorFilled': redefinition; multiple initialization
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(11,8):
      see declaration of 'boxColorFilled'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(146,8): error C2086: 'ImVec4 boxColorFilled': redefinition
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(11,8):
      see declaration of 'boxColorFilled'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(147,8): error C2374: 'nameColor': redefinition; multiple initialization
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(6,8):
      see declaration of 'nameColor'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(147,8): error C2086: 'ImVec4 nameColor': redefinition
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(6,8):
      see declaration of 'nameColor'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(196,10): warning C4459: declaration of 'rc' hides global declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(78,6):
      see declaration of 'rc'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(217,45): warning C4838: conversion from 'int' to 'ImWchar' requires a narrowing conversion
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(217,45): warning C4305: 'initializing': truncation from 'int' to 'ImWchar'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(217,45): warning C4309: 'initializing': truncation of constant value
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(311,18): warning C4459: declaration of 'hwnd' hides global declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(77,6):
      see declaration of 'hwnd'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(396,65): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(396,50): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(448,26): warning C4459: declaration of 'entitiesCount' hides global declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(24,5):
      see declaration of 'entitiesCount'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(452,30): error C2662: 'void std::vector<_Ty,_Alloc>::clear(void) noexcept': cannot convert 'this' pointer from 'std::vector' to 'std::vector<_Ty,_Alloc> &'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(452,17):
      Reason: cannot convert from 'std::vector' to 'std::vector<_Ty,_Alloc>'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(452,17):
      Conversion requires a second user-defined-conversion operator or constructor
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(1807,23):
      see declaration of 'std::vector<_Ty,_Alloc>::clear'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(452,30):
      while trying to match the argument list '()'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(453,17): error C2065: 'bestTarget': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(453,30): error C2064: term does not evaluate to a function taking 0 arguments
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(456,35): warning C4018: '<': signed/unsigned mismatch
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(494,32): warning C4456: declaration of 'screenCenter' hides previous local declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(396,25):
      see declaration of 'screenCenter'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(497,81): warning C4244: 'argument': conversion from 'LONG' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(497,63): warning C4244: 'argument': conversion from 'LONG' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(515,32): warning C4456: declaration of 'screenCenter' hides previous local declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(396,25):
      see declaration of 'screenCenter'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(518,30): warning C4456: declaration of 'isKeyPressed' hides previous local declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(355,21):
      see declaration of 'isKeyPressed'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(522,42): error C2027: use of undefined type 'AimKey'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(32,12):
      see declaration of 'AimKey'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(522,42): error C2065: 'LeftMouseButton': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(522,42): error C2131: expression did not evaluate to a constant
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(522,42):
      a non-constant (sub-)expression was encountered
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(525,42): error C2027: use of undefined type 'AimKey'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(32,12):
      see declaration of 'AimKey'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(525,42): error C2065: 'RightMouseButton': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(525,42): error C2131: expression did not evaluate to a constant
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(525,42):
      a non-constant (sub-)expression was encountered
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(528,42): error C2027: use of undefined type 'AimKey'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(32,12):
      see declaration of 'AimKey'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(528,42): error C2065: 'MiddleMouseButton': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(528,42): error C2131: expression did not evaluate to a constant
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(528,42):
      a non-constant (sub-)expression was encountered
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(531,42): error C2027: use of undefined type 'AimKey'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(32,12):
      see declaration of 'AimKey'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(531,42): error C2065: 'ShiftKey': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(531,42): error C2131: expression did not evaluate to a constant
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(531,42):
      a non-constant (sub-)expression was encountered
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(534,42): error C2027: use of undefined type 'AimKey'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(32,12):
      see declaration of 'AimKey'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(534,42): error C2065: 'ControlKey': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(534,42): error C2131: expression did not evaluate to a constant
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(534,42):
      a non-constant (sub-)expression was encountered
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(537,42): error C2027: use of undefined type 'AimKey'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(32,12):
      see declaration of 'AimKey'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(537,42): error C2065: 'AltKey': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(537,42): error C2131: expression did not evaluate to a constant
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(537,42):
      a non-constant (sub-)expression was encountered
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(572,47): warning C4459: declaration of 'smoothFactor' hides global declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(17,7):
      see declaration of 'smoothFactor'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(609,42): error C2146: syntax error: missing ';' before identifier 'target'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(609,42): error C2065: 'target': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(610,29): error C2065: 'target': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(611,29): error C2065: 'target': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(612,29): error C2065: 'target': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(613,29): error C2065: 'target': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(614,29): error C2065: 'target': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(615,29): error C2065: 'target': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(616,29): error C2065: 'target': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(619,36): warning C4456: declaration of 'screenCenter' hides previous local declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(396,25):
      see declaration of 'screenCenter'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(626,29): error C2065: 'target': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(629,33): error C2065: 'target': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(630,56): error C2065: 'target': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(697,26): warning C4459: declaration of 'hwnd' hides global declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.h(77,6):
      see declaration of 'hwnd'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(720,107): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(720,82): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(720,57): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(720,48): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(722,28): warning C4456: declaration of 'screenCenter' hides previous local declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(396,25):
      see declaration of 'screenCenter'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(739,39): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(740,41): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(741,40): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(775,39): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(776,41): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(777,40): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(822,92): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(822,61): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(823,92): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(823,61): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(824,92): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(824,61): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(825,93): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(825,62): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(830,31): warning C4459: declaration of 'lineColor' hides global declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(431,13):
      see declaration of 'lineColor'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(834,39): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(835,41): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(836,40): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(864,38): warning C4456: declaration of 'i' hides previous local declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(456,26):
      see declaration of 'i'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(870,43): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(871,45): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(872,44): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(883,35): warning C4459: declaration of 'amplitude' hides global declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(426,7):
      see declaration of 'amplitude'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(894,39): warning C4456: declaration of 'lineColor' hides previous local declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(830,31):
      see declaration of 'lineColor'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(898,46): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(897,46): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(896,46): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(925,43): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(926,45): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(927,44): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(982,43): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(983,45): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(984,44): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1012,37): warning C4456: declaration of 'i' hides previous local declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(456,26):
      see declaration of 'i'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1034,37): warning C4456: declaration of 'i' hides previous local declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(456,26):
      see declaration of 'i'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1083,37): warning C4456: declaration of 'i' hides previous local declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(456,26):
      see declaration of 'i'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1108,43): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1109,45): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1110,44): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1131,47): error C2662: 'bool std::vector<_Ty,_Alloc>::empty(void) noexcept const': cannot convert 'this' pointer from 'std::vector' to 'const std::vector<_Ty,_Alloc> &'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1131,34):
      Reason: cannot convert from 'std::vector' to 'const std::vector<_Ty,_Alloc>'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1131,34):
      Conversion requires a second user-defined-conversion operator or constructor
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(1908,47):
      see declaration of 'std::vector<_Ty,_Alloc>::empty'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1131,47):
      while trying to match the argument list '()'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1132,26): warning C4456: declaration of 'isKeyPressed' hides previous local declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(355,21):
      see declaration of 'isKeyPressed'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1133,48): error C2027: use of undefined type 'AimKey'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(32,12):
      see declaration of 'AimKey'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1133,48): error C2065: 'LeftMouseButton': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1139,30): warning C4456: declaration of 'currentTime' hides previous local declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(349,14):
      see declaration of 'currentTime'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1146,59): error C2663: 'std::vector<_Ty,_Alloc>::_Unchecked_begin': no overloaded function has valid conversion for 'this' pointer
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(1896,40):
      could be 'const _Ty *std::vector<_Ty,_Alloc>::_Unchecked_begin(void) noexcept const'
          C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1146,59):
          'const _Ty *std::vector<_Ty,_Alloc>::_Unchecked_begin(void) noexcept const': cannot convert 'this' pointer from 'std::vector' to 'const std::vector<_Ty,_Alloc> &'
              C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1146,59):
              Reason: cannot convert from 'std::vector' to 'const std::vector<_Ty,_Alloc>'
              C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1146,59):
              Conversion requires a second user-defined-conversion operator or constructor
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(1892,34):
      or       '_Ty *std::vector<_Ty,_Alloc>::_Unchecked_begin(void) noexcept'
          C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1146,59):
          '_Ty *std::vector<_Ty,_Alloc>::_Unchecked_begin(void) noexcept': cannot convert 'this' pointer from 'std::vector' to 'std::vector<_Ty,_Alloc> &'
              C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1146,59):
              Reason: cannot convert from 'std::vector' to 'std::vector<_Ty,_Alloc>'
              C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1146,59):
              Conversion requires a second user-defined-conversion operator or constructor
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1146,59):
      while trying to match the argument list '()'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1146,59): error C2663: 'std::vector<_Ty,_Alloc>::_Unchecked_end': no overloaded function has valid conversion for 'this' pointer
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(1904,40):
      could be 'const _Ty *std::vector<_Ty,_Alloc>::_Unchecked_end(void) noexcept const'
          C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1146,59):
          'const _Ty *std::vector<_Ty,_Alloc>::_Unchecked_end(void) noexcept const': cannot convert 'this' pointer from 'std::vector' to 'const std::vector<_Ty,_Alloc> &'
              C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1146,59):
              Reason: cannot convert from 'std::vector' to 'const std::vector<_Ty,_Alloc>'
              C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1146,59):
              Conversion requires a second user-defined-conversion operator or constructor
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(1900,34):
      or       '_Ty *std::vector<_Ty,_Alloc>::_Unchecked_end(void) noexcept'
          C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1146,59):
          '_Ty *std::vector<_Ty,_Alloc>::_Unchecked_end(void) noexcept': cannot convert 'this' pointer from 'std::vector' to 'std::vector<_Ty,_Alloc> &'
              C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1146,59):
              Reason: cannot convert from 'std::vector' to 'std::vector<_Ty,_Alloc>'
              C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1146,59):
              Conversion requires a second user-defined-conversion operator or constructor
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1146,59):
      while trying to match the argument list '()'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1148,41): error C2065: 'bestTarget': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1158,55): error C2663: 'std::vector<_Ty,_Alloc>::_Unchecked_begin': no overloaded function has valid conversion for 'this' pointer
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(1896,40):
      could be 'const _Ty *std::vector<_Ty,_Alloc>::_Unchecked_begin(void) noexcept const'
          C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1158,55):
          'const _Ty *std::vector<_Ty,_Alloc>::_Unchecked_begin(void) noexcept const': cannot convert 'this' pointer from 'std::vector' to 'const std::vector<_Ty,_Alloc> &'
              C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1158,55):
              Reason: cannot convert from 'std::vector' to 'const std::vector<_Ty,_Alloc>'
              C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1158,55):
              Conversion requires a second user-defined-conversion operator or constructor
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(1892,34):
      or       '_Ty *std::vector<_Ty,_Alloc>::_Unchecked_begin(void) noexcept'
          C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1158,55):
          '_Ty *std::vector<_Ty,_Alloc>::_Unchecked_begin(void) noexcept': cannot convert 'this' pointer from 'std::vector' to 'std::vector<_Ty,_Alloc> &'
              C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1158,55):
              Reason: cannot convert from 'std::vector' to 'std::vector<_Ty,_Alloc>'
              C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1158,55):
              Conversion requires a second user-defined-conversion operator or constructor
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1158,55):
      while trying to match the argument list '()'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1158,55): error C2663: 'std::vector<_Ty,_Alloc>::_Unchecked_end': no overloaded function has valid conversion for 'this' pointer
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(1904,40):
      could be 'const _Ty *std::vector<_Ty,_Alloc>::_Unchecked_end(void) noexcept const'
          C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1158,55):
          'const _Ty *std::vector<_Ty,_Alloc>::_Unchecked_end(void) noexcept const': cannot convert 'this' pointer from 'std::vector' to 'const std::vector<_Ty,_Alloc> &'
              C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1158,55):
              Reason: cannot convert from 'std::vector' to 'const std::vector<_Ty,_Alloc>'
              C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1158,55):
              Conversion requires a second user-defined-conversion operator or constructor
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(1900,34):
      or       '_Ty *std::vector<_Ty,_Alloc>::_Unchecked_end(void) noexcept'
          C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1158,55):
          '_Ty *std::vector<_Ty,_Alloc>::_Unchecked_end(void) noexcept': cannot convert 'this' pointer from 'std::vector' to 'std::vector<_Ty,_Alloc> &'
              C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1158,55):
              Reason: cannot convert from 'std::vector' to 'std::vector<_Ty,_Alloc>'
              C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1158,55):
              Conversion requires a second user-defined-conversion operator or constructor
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1158,55):
      while trying to match the argument list '()'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1186,33): error C2065: 'bestTarget': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1191,29): error C2065: 'bestTarget': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1192,48): error C2065: 'bestTarget': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1199,29): error C2065: 'bestTarget': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1200,36): warning C4456: declaration of 'screenCenter' hides previous local declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(396,25):
      see declaration of 'screenCenter'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1201,79): error C2065: 'bestTarget': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1202,78): error C2065: 'bestTarget': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1202,99): error C2065: 'bestTarget': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1205,77): error C2065: 'bestTarget': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1206,73): error C2065: 'bestTarget': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1206,90): error C7732: expected an expression before ']'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1210,52): error C2065: 'bestTarget': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1215,47): error C2065: 'bestTarget': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1215,64): error C7732: expected an expression before ']'
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1230,39): warning C4459: declaration of 'smoothFactor' hides global declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(17,7):
      see declaration of 'smoothFactor'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1253,38): warning C4456: declaration of 'currentTime' hides previous local declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1139,30):
      see declaration of 'currentTime'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1269,30): error C2662: 'void std::vector<_Ty,_Alloc>::clear(void) noexcept': cannot convert 'this' pointer from 'std::vector' to 'std::vector<_Ty,_Alloc> &'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1269,17):
      Reason: cannot convert from 'std::vector' to 'std::vector<_Ty,_Alloc>'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1269,17):
      Conversion requires a second user-defined-conversion operator or constructor
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(1807,23):
      see declaration of 'std::vector<_Ty,_Alloc>::clear'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1269,30):
      while trying to match the argument list '()'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1272,22): warning C4456: declaration of 'currentTime' hides previous local declaration
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(349,14):
      see declaration of 'currentTime'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1273,84): error C2065: 'lastUpdateTime': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1273,34): error C2672: 'std::chrono::duration_cast': no matching overloaded function found
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_chrono.hpp(87,19):
      could be '_To std::chrono::duration_cast(const std::chrono::duration<_Rep,_Period> &) noexcept(<expr>)'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1275,21): error C2065: 'lastUpdateTime': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1539,92): error C2662: 'allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type std::vector<_Ty,_Alloc>::size(void) noexcept const': cannot convert 'this' pointer from 'std::vector' to 'const std::vector<_Ty,_Alloc> &'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1539,79):
      Reason: cannot convert from 'std::vector' to 'const std::vector<_Ty,_Alloc>'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1539,79):
      Conversion requires a second user-defined-conversion operator or constructor
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(1913,39):
      see declaration of 'std::vector<_Ty,_Alloc>::size'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1539,92):
      while trying to match the argument list '()'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1540,45): error C2065: 'bestTarget': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1541,87): error C2065: 'bestTarget': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1542,85): error C2065: 'bestTarget': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1543,82): error C2065: 'bestTarget': undeclared identifier
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1787,133): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(522,42): error C2051: case expression not constant
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(525,42): error C2051: case expression not constant
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(528,42): error C2051: case expression not constant
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(531,42): error C2051: case expression not constant
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(534,42): error C2051: case expression not constant
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(537,42): error C2051: case expression not constant
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(543,25): warning C4065: switch statement contains 'default' but no 'case' labels
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(543,25): warning C4064: switch of incomplete enum 'AimKey'
      C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\ESP\ESP LINES\Complementos.h(32,12):
      see declaration of 'AimKey'
  
C:\Users\<USER>\Downloads\4CHAN ESP\4CHAN ESP\Anfezion + ESP - Project\examples\example_win32_directx11\main.cpp(1357,71): warning C4390: ';': empty controlled statement found; is this the intent?
  Encryptor.hpp
