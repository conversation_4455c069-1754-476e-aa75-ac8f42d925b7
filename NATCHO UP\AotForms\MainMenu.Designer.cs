﻿namespace AotForms
{
    partial class MainMenu
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges51 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges52 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges57 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges58 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges59 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges60 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges49 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges50 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges13 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges14 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges83 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges84 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges15 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges16 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges17 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges18 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges35 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges36 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges19 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges20 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges21 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges22 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges23 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges24 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges25 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges26 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges27 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges28 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges29 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges30 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges31 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges32 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges33 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges34 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges63 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges64 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges37 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges38 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges39 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges40 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges41 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges42 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges43 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges44 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges45 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges46 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges47 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges48 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges53 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges54 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges55 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges56 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges61 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges62 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges77 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges78 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges65 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges66 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges67 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges68 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges69 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges70 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges71 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges72 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges73 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges74 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges75 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges76 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges79 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges80 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges81 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges82 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges11 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges12 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges1 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges2 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges3 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges4 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges5 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges6 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges7 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges8 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges9 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges10 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(components);
            label23 = new Label();
            panel3 = new Panel();
            Slider3 = new Panel();
            rangelvl1 = new Label();
            label20 = new Label();
            panel2 = new Panel();
            Slider2 = new Panel();
            smthlvl2 = new Label();
            label19 = new Label();
            panel1 = new Panel();
            Slider1 = new Panel();
            valuelbl1 = new Label();
            guna2Elipse1 = new Guna.UI2.WinForms.Guna2Elipse(components);
            animationTimer1 = new System.Windows.Forms.Timer(components);
            guna2Elipse2 = new Guna.UI2.WinForms.Guna2Elipse(components);
            guna2Elipse3 = new Guna.UI2.WinForms.Guna2Elipse(components);
            guna2Elipse4 = new Guna.UI2.WinForms.Guna2Elipse(components);
            guna2Button7 = new Guna.UI2.WinForms.Guna2Button();
            guna2Button8 = new Guna.UI2.WinForms.Guna2Button();
            guna2Button9 = new Guna.UI2.WinForms.Guna2Button();
            guna2Button10 = new Guna.UI2.WinForms.Guna2Button();
            guna2Elipse5 = new Guna.UI2.WinForms.Guna2Elipse(components);
            guna2Button13 = new Guna.UI2.WinForms.Guna2Button();
            guna2Panel2 = new Guna.UI2.WinForms.Guna2Panel();
            guna2ControlBox2 = new Guna.UI2.WinForms.Guna2ControlBox();
            guna2Button2 = new Guna.UI2.WinForms.Guna2Button();
            label22 = new Label();
            statuslable2 = new Label();
            guna2Panel7 = new Guna.UI2.WinForms.Guna2Panel();
            label25 = new Label();
            guna2CustomCheckBox19 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label14 = new Label();
            guna2CustomCheckBox5 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2Button11 = new Guna.UI2.WinForms.Guna2Button();
            label12 = new Label();
            label10 = new Label();
            guna2CustomCheckBox8 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox6 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label5 = new Label();
            label6 = new Label();
            guna2CustomCheckBox1 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label35 = new Label();
            label11 = new Label();
            label2 = new Label();
            guna2CustomCheckBox2 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label1 = new Label();
            guna2CustomCheckBox21 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2Panel5 = new Guna.UI2.WinForms.Guna2Panel();
            label26 = new Label();
            guna2CustomCheckBox4 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label17 = new Label();
            label3 = new Label();
            guna2Button3 = new Guna.UI2.WinForms.Guna2Button();
            guna2ComboBox2 = new Guna.UI2.WinForms.Guna2ComboBox();
            guna2CustomCheckBox10 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox11 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox12 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox13 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox15 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label48 = new Label();
            guna2CustomCheckBox16 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label47 = new Label();
            label49 = new Label();
            label50 = new Label();
            label51 = new Label();
            label52 = new Label();
            mainpnl = new Guna.UI2.WinForms.Guna2Panel();
            guna2CustomCheckBox17 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox18 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label13 = new Label();
            label16 = new Label();
            guna2CustomCheckBox7 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox3 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label8 = new Label();
            label9 = new Label();
            guna2CustomCheckBox9 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label4 = new Label();
            label37 = new Label();
            guna2CustomCheckBox14 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label38 = new Label();
            guna2Panel4 = new Guna.UI2.WinForms.Guna2Panel();
            label15 = new Label();
            guna2ControlBox1 = new Guna.UI2.WinForms.Guna2ControlBox();
            guna2Panel1 = new Guna.UI2.WinForms.Guna2Panel();
            label21 = new Label();
            guna2ControlBox3 = new Guna.UI2.WinForms.Guna2ControlBox();
            label18 = new Label();
            guna2Button6 = new Guna.UI2.WinForms.Guna2Button();
            passwordtxt = new Guna.UI2.WinForms.Guna2TextBox();
            label7 = new Label();
            guna2Button4 = new Guna.UI2.WinForms.Guna2Button();
            statuslbl = new Label();
            usernametxt = new Guna.UI2.WinForms.Guna2TextBox();
            timer1 = new System.Windows.Forms.Timer(components);
            animationTimer3 = new System.Windows.Forms.Timer(components);
            animationTimer2 = new System.Windows.Forms.Timer(components);
            guna2DragControl1 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2DragControl2 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2DragControl3 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2DragControl4 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2DragControl5 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2DragControl6 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2DragControl7 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2DragControl8 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2DragControl9 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2DragControl10 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2DragControl11 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2DragControl12 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2DragControl13 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2AnimateWindow1 = new Guna.UI2.WinForms.Guna2AnimateWindow(components);
            panel1.SuspendLayout();
            guna2Panel2.SuspendLayout();
            guna2Panel7.SuspendLayout();
            guna2Panel5.SuspendLayout();
            mainpnl.SuspendLayout();
            guna2Panel4.SuspendLayout();
            guna2Panel1.SuspendLayout();
            SuspendLayout();
            // 
            // guna2BorderlessForm1
            // 
            guna2BorderlessForm1.BorderRadius = 10;
            guna2BorderlessForm1.ContainerControl = this;
            guna2BorderlessForm1.DockForm = false;
            guna2BorderlessForm1.DockIndicatorTransparencyValue = 1D;
            guna2BorderlessForm1.DragStartTransparencyValue = 1D;
            guna2BorderlessForm1.HasFormShadow = false;
            guna2BorderlessForm1.ResizeForm = false;
            guna2BorderlessForm1.TransparentWhileDrag = true;
            // 
            // label23
            // 
            label23.AutoSize = true;
            label23.BackColor = Color.Transparent;
            label23.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label23.ForeColor = Color.DarkGray;
            label23.Location = new Point(8, 111);
            label23.Name = "label23";
            label23.Size = new Size(54, 13);
            label23.TabIndex = 57;
            label23.Text = "RANGED";
            // 
            // panel3
            // 
            panel3.BackColor = Color.DarkGray;
            panel3.Location = new Point(10, 130);
            panel3.Name = "panel3";
            panel3.Size = new Size(145, 13);
            panel3.TabIndex = 56;
            // 
            // Slider3
            // 
            Slider3.BackColor = Color.Red;
            Slider3.Location = new Point(10, 130);
            Slider3.Name = "Slider3";
            Slider3.Size = new Size(145, 13);
            Slider3.TabIndex = 0;
            Slider3.Paint += Slider3_Paint;
            // 
            // rangelvl1
            // 
            rangelvl1.AutoSize = true;
            rangelvl1.BackColor = Color.Transparent;
            rangelvl1.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            rangelvl1.ForeColor = Color.DarkGray;
            rangelvl1.Location = new Point(128, 110);
            rangelvl1.Name = "rangelvl1";
            rangelvl1.Size = new Size(26, 13);
            rangelvl1.TabIndex = 2;
            rangelvl1.Text = "100";
            rangelvl1.Click += rangelvl1_Click;
            // 
            // label20
            // 
            label20.AutoSize = true;
            label20.BackColor = Color.Transparent;
            label20.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label20.ForeColor = Color.DarkGray;
            label20.Location = new Point(8, 68);
            label20.Name = "label20";
            label20.Size = new Size(56, 13);
            label20.TabIndex = 55;
            label20.Text = "SMOOTH";
            // 
            // panel2
            // 
            panel2.BackColor = Color.DarkGray;
            panel2.Location = new Point(10, 88);
            panel2.Name = "panel2";
            panel2.Size = new Size(145, 13);
            panel2.TabIndex = 54;
            // 
            // Slider2
            // 
            Slider2.BackColor = Color.Red;
            Slider2.Location = new Point(10, 88);
            Slider2.Name = "Slider2";
            Slider2.Size = new Size(145, 13);
            Slider2.TabIndex = 0;
            Slider2.Paint += Slider2_Paint;
            // 
            // smthlvl2
            // 
            smthlvl2.AutoSize = true;
            smthlvl2.BackColor = Color.Transparent;
            smthlvl2.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            smthlvl2.ForeColor = Color.DarkGray;
            smthlvl2.Location = new Point(127, 68);
            smthlvl2.Name = "smthlvl2";
            smthlvl2.Size = new Size(28, 13);
            smthlvl2.TabIndex = 2;
            smthlvl2.Text = "400";
            // 
            // label19
            // 
            label19.AutoSize = true;
            label19.BackColor = Color.Transparent;
            label19.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label19.ForeColor = Color.DarkGray;
            label19.Location = new Point(8, 28);
            label19.Name = "label19";
            label19.Size = new Size(52, 13);
            label19.TabIndex = 53;
            label19.Text = "AIMFOV";
            // 
            // panel1
            // 
            panel1.BackColor = Color.DarkGray;
            panel1.Controls.Add(Slider1);
            panel1.ForeColor = SystemColors.ControlText;
            panel1.Location = new Point(10, 47);
            panel1.Name = "panel1";
            panel1.Size = new Size(145, 13);
            panel1.TabIndex = 52;
            panel1.Paint += panel1_Paint;
            // 
            // Slider1
            // 
            Slider1.BackColor = Color.Red;
            Slider1.Location = new Point(0, 0);
            Slider1.Name = "Slider1";
            Slider1.Size = new Size(145, 13);
            Slider1.TabIndex = 0;
            // 
            // valuelbl1
            // 
            valuelbl1.AutoSize = true;
            valuelbl1.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            valuelbl1.ForeColor = Color.DarkGray;
            valuelbl1.Location = new Point(128, 28);
            valuelbl1.Name = "valuelbl1";
            valuelbl1.Size = new Size(26, 13);
            valuelbl1.TabIndex = 1;
            valuelbl1.Text = "100";
            // 
            // guna2Elipse1
            // 
            guna2Elipse1.BorderRadius = 5;
            // 
            // guna2Elipse2
            // 
            guna2Elipse2.BorderRadius = 3;
            guna2Elipse2.TargetControl = panel1;
            // 
            // guna2Elipse3
            // 
            guna2Elipse3.BorderRadius = 3;
            guna2Elipse3.TargetControl = panel2;
            // 
            // guna2Elipse4
            // 
            guna2Elipse4.BorderRadius = 3;
            guna2Elipse4.TargetControl = panel3;
            // 
            // guna2Button7
            // 
            guna2Button7.Animated = true;
            guna2Button7.BackColor = Color.FromArgb(10, 10, 10);
            guna2Button7.CustomizableEdges = customizableEdges51;
            guna2Button7.DisabledState.BorderColor = Color.DarkGray;
            guna2Button7.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button7.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button7.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button7.FillColor = Color.White;
            guna2Button7.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button7.ForeColor = Color.White;
            guna2Button7.Location = new Point(123, 102);
            guna2Button7.Name = "guna2Button7";
            guna2Button7.ShadowDecoration.CustomizableEdges = customizableEdges52;
            guna2Button7.Size = new Size(16, 15);
            guna2Button7.TabIndex = 73;
            guna2Button7.Click += guna2Button7_Click;
            // 
            // guna2Button8
            // 
            guna2Button8.Animated = true;
            guna2Button8.BackColor = Color.FromArgb(10, 10, 10);
            guna2Button8.CustomizableEdges = customizableEdges57;
            guna2Button8.DisabledState.BorderColor = Color.DarkGray;
            guna2Button8.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button8.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button8.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button8.FillColor = Color.White;
            guna2Button8.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button8.ForeColor = Color.Black;
            guna2Button8.Location = new Point(123, 77);
            guna2Button8.Name = "guna2Button8";
            guna2Button8.ShadowDecoration.CustomizableEdges = customizableEdges58;
            guna2Button8.Size = new Size(16, 15);
            guna2Button8.TabIndex = 71;
            guna2Button8.Click += guna2Button8_Click;
            // 
            // guna2Button9
            // 
            guna2Button9.Animated = true;
            guna2Button9.BackColor = Color.FromArgb(10, 10, 10);
            guna2Button9.CustomizableEdges = customizableEdges59;
            guna2Button9.DisabledState.BorderColor = Color.DarkGray;
            guna2Button9.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button9.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button9.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button9.FillColor = Color.White;
            guna2Button9.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button9.ForeColor = Color.Black;
            guna2Button9.Location = new Point(123, 53);
            guna2Button9.Name = "guna2Button9";
            guna2Button9.ShadowDecoration.CustomizableEdges = customizableEdges60;
            guna2Button9.Size = new Size(16, 15);
            guna2Button9.TabIndex = 69;
            guna2Button9.Click += guna2Button9_Click;
            // 
            // guna2Button10
            // 
            guna2Button10.Animated = true;
            guna2Button10.BackColor = Color.FromArgb(10, 10, 10);
            guna2Button10.CustomizableEdges = customizableEdges49;
            guna2Button10.DisabledState.BorderColor = Color.DarkGray;
            guna2Button10.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button10.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button10.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button10.FillColor = Color.White;
            guna2Button10.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button10.ForeColor = Color.Black;
            guna2Button10.Location = new Point(123, 28);
            guna2Button10.Name = "guna2Button10";
            guna2Button10.ShadowDecoration.CustomizableEdges = customizableEdges50;
            guna2Button10.Size = new Size(16, 15);
            guna2Button10.TabIndex = 67;
            guna2Button10.Click += guna2Button10_Click;
            // 
            // guna2Elipse5
            // 
            guna2Elipse5.BorderRadius = 5;
            // 
            // guna2Button13
            // 
            guna2Button13.Animated = true;
            guna2Button13.BackColor = Color.Transparent;
            guna2Button13.BorderColor = Color.Transparent;
            guna2Button13.BorderStyle = System.Drawing.Drawing2D.DashStyle.Dash;
            guna2Button13.BorderThickness = 1;
            guna2Button13.CustomizableEdges = customizableEdges13;
            guna2Button13.DisabledState.BorderColor = Color.DarkGray;
            guna2Button13.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button13.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button13.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button13.FillColor = Color.DarkGray;
            guna2Button13.Font = new Font("Microsoft Sans Serif", 8.25F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button13.ForeColor = Color.Black;
            guna2Button13.Location = new Point(680, 489);
            guna2Button13.Name = "guna2Button13";
            guna2Button13.PressedColor = Color.DarkSlateGray;
            guna2Button13.ShadowDecoration.CustomizableEdges = customizableEdges14;
            guna2Button13.Size = new Size(247, 28);
            guna2Button13.TabIndex = 23;
            guna2Button13.Text = "NOVA";
            guna2Button13.Click += guna2Button13_Click;
            // 
            // guna2Panel2
            // 
            guna2Panel2.AccessibleName = "BURIED MODS REBORN 1.0";
            guna2Panel2.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            guna2Panel2.BackColor = Color.Transparent;
            guna2Panel2.BackgroundImageLayout = ImageLayout.None;
            guna2Panel2.BorderColor = Color.Black;
            guna2Panel2.BorderStyle = System.Drawing.Drawing2D.DashStyle.Custom;
            guna2Panel2.Controls.Add(guna2ControlBox2);
            guna2Panel2.Controls.Add(guna2Button2);
            guna2Panel2.Controls.Add(label22);
            guna2Panel2.Controls.Add(statuslable2);
            guna2Panel2.Controls.Add(guna2Panel7);
            guna2Panel2.Controls.Add(guna2Panel5);
            guna2Panel2.Controls.Add(mainpnl);
            guna2Panel2.Controls.Add(guna2Panel4);
            guna2Panel2.Controls.Add(guna2ControlBox1);
            guna2Panel2.CustomBorderColor = Color.Transparent;
            customizableEdges83.BottomLeft = false;
            customizableEdges83.BottomRight = false;
            customizableEdges83.TopLeft = false;
            customizableEdges83.TopRight = false;
            guna2Panel2.CustomizableEdges = customizableEdges83;
            guna2Panel2.ForeColor = Color.Transparent;
            guna2Panel2.Location = new Point(12, 12);
            guna2Panel2.Name = "guna2Panel2";
            guna2Panel2.ShadowDecoration.CustomizableEdges = customizableEdges84;
            guna2Panel2.Size = new Size(383, 517);
            guna2Panel2.TabIndex = 76;
            guna2Panel2.Paint += guna2Panel2_Paint;
            // 
            // guna2ControlBox2
            // 
            guna2ControlBox2.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            guna2ControlBox2.BackColor = Color.Transparent;
            guna2ControlBox2.ControlBoxStyle = Guna.UI2.WinForms.Enums.ControlBoxStyle.Custom;
            guna2ControlBox2.ControlBoxType = Guna.UI2.WinForms.Enums.ControlBoxType.MinimizeBox;
            guna2ControlBox2.CustomIconSize = 14F;
            guna2ControlBox2.CustomizableEdges = customizableEdges15;
            guna2ControlBox2.FillColor = Color.Transparent;
            guna2ControlBox2.IconColor = Color.DarkGray;
            guna2ControlBox2.Location = new Point(306, 4);
            guna2ControlBox2.Name = "guna2ControlBox2";
            guna2ControlBox2.ShadowDecoration.CustomizableEdges = customizableEdges16;
            guna2ControlBox2.Size = new Size(27, 18);
            guna2ControlBox2.TabIndex = 86;
            // 
            // guna2Button2
            // 
            guna2Button2.Animated = true;
            guna2Button2.BackColor = Color.Transparent;
            guna2Button2.BorderColor = Color.Transparent;
            guna2Button2.BorderRadius = 5;
            guna2Button2.BorderThickness = 1;
            guna2Button2.CustomizableEdges = customizableEdges17;
            guna2Button2.DisabledState.BorderColor = Color.DarkGray;
            guna2Button2.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button2.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button2.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button2.FillColor = Color.Transparent;
            guna2Button2.Font = new Font("Segoe UI Black", 9.75F, FontStyle.Bold, GraphicsUnit.Point);
            guna2Button2.ForeColor = Color.Red;
            guna2Button2.Location = new Point(8, 10);
            guna2Button2.Name = "guna2Button2";
            guna2Button2.PressedColor = Color.Silver;
            guna2Button2.ShadowDecoration.CustomizableEdges = customizableEdges18;
            guna2Button2.Size = new Size(214, 21);
            guna2Button2.TabIndex = 85;
            guna2Button2.Text = "NATCHO CHEAT";
            // 
            // label22
            // 
            label22.AutoSize = true;
            label22.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label22.ForeColor = Color.LimeGreen;
            label22.Location = new Point(22, 498);
            label22.Name = "label22";
            label22.Size = new Size(56, 13);
            label22.TabIndex = 84;
            label22.Text = "STATUS :";
            label22.Click += label22_Click;
            // 
            // statuslable2
            // 
            statuslable2.AutoSize = true;
            statuslable2.BackColor = Color.Transparent;
            statuslable2.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            statuslable2.ForeColor = Color.LimeGreen;
            statuslable2.Location = new Point(74, 498);
            statuslable2.Name = "statuslable2";
            statuslable2.Size = new Size(0, 13);
            statuslable2.TabIndex = 83;
            statuslable2.Click += statuslable2_Click;
            // 
            // guna2Panel7
            // 
            guna2Panel7.BackColor = Color.Black;
            guna2Panel7.BorderColor = Color.Black;
            guna2Panel7.BorderStyle = System.Drawing.Drawing2D.DashStyle.Dash;
            guna2Panel7.BorderThickness = 1;
            guna2Panel7.Controls.Add(label25);
            guna2Panel7.Controls.Add(guna2CustomCheckBox19);
            guna2Panel7.Controls.Add(label14);
            guna2Panel7.Controls.Add(guna2CustomCheckBox5);
            guna2Panel7.Controls.Add(guna2Button11);
            guna2Panel7.Controls.Add(label12);
            guna2Panel7.Controls.Add(label10);
            guna2Panel7.Controls.Add(guna2CustomCheckBox8);
            guna2Panel7.Controls.Add(guna2CustomCheckBox6);
            guna2Panel7.Controls.Add(label5);
            guna2Panel7.Controls.Add(label6);
            guna2Panel7.Controls.Add(guna2CustomCheckBox1);
            guna2Panel7.Controls.Add(label35);
            guna2Panel7.Controls.Add(label11);
            guna2Panel7.Controls.Add(label2);
            guna2Panel7.Controls.Add(guna2CustomCheckBox2);
            guna2Panel7.Controls.Add(label1);
            guna2Panel7.Controls.Add(guna2CustomCheckBox21);
            guna2Panel7.CustomBorderColor = Color.Transparent;
            guna2Panel7.CustomBorderThickness = new Padding(0, 2, 0, 0);
            guna2Panel7.CustomizableEdges = customizableEdges35;
            guna2Panel7.ForeColor = Color.DimGray;
            guna2Panel7.Location = new Point(203, 219);
            guna2Panel7.Name = "guna2Panel7";
            guna2Panel7.ShadowDecoration.CustomizableEdges = customizableEdges36;
            guna2Panel7.Size = new Size(167, 275);
            guna2Panel7.TabIndex = 77;
            guna2Panel7.Paint += guna2Panel7_Paint;
            // 
            // label25
            // 
            label25.AutoSize = true;
            label25.BackColor = Color.Transparent;
            label25.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label25.ForeColor = Color.Red;
            label25.Location = new Point(7, 5);
            label25.Name = "label25";
            label25.Size = new Size(89, 13);
            label25.TabIndex = 87;
            label25.Text = "HELLISH NOVA";
            // 
            // guna2CustomCheckBox19
            // 
            guna2CustomCheckBox19.Animated = true;
            guna2CustomCheckBox19.BackColor = Color.Transparent;
            guna2CustomCheckBox19.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox19.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox19.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox19.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox19.CheckMarkColor = Color.DimGray;
            guna2CustomCheckBox19.CustomizableEdges = customizableEdges19;
            guna2CustomCheckBox19.Location = new Point(134, 173);
            guna2CustomCheckBox19.Name = "guna2CustomCheckBox19";
            guna2CustomCheckBox19.ShadowDecoration.CustomizableEdges = customizableEdges20;
            guna2CustomCheckBox19.Size = new Size(20, 18);
            guna2CustomCheckBox19.TabIndex = 90;
            guna2CustomCheckBox19.Text = "guna2CustomCheckBox19";
            guna2CustomCheckBox19.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox19.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox19.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox19.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox19.Click += guna2CustomCheckBox19_Click_1;
            // 
            // label14
            // 
            label14.AutoSize = true;
            label14.BackColor = Color.Transparent;
            label14.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label14.ForeColor = Color.DarkGray;
            label14.Location = new Point(9, 176);
            label14.Name = "label14";
            label14.Size = new Size(106, 13);
            label14.TabIndex = 89;
            label14.Text = "PROX FLY ME [F5]";
            // 
            // guna2CustomCheckBox5
            // 
            guna2CustomCheckBox5.Animated = true;
            guna2CustomCheckBox5.BackColor = Color.Transparent;
            guna2CustomCheckBox5.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox5.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox5.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox5.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox5.CheckMarkColor = Color.DimGray;
            guna2CustomCheckBox5.CustomizableEdges = customizableEdges21;
            guna2CustomCheckBox5.Location = new Point(134, 148);
            guna2CustomCheckBox5.Name = "guna2CustomCheckBox5";
            guna2CustomCheckBox5.ShadowDecoration.CustomizableEdges = customizableEdges22;
            guna2CustomCheckBox5.Size = new Size(20, 18);
            guna2CustomCheckBox5.TabIndex = 88;
            guna2CustomCheckBox5.Text = "guna2CustomCheckBox5";
            guna2CustomCheckBox5.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox5.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox5.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox5.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox5.Click += guna2CustomCheckBox5_Click_1;
            // 
            // guna2Button11
            // 
            guna2Button11.Animated = true;
            guna2Button11.BackColor = Color.Transparent;
            guna2Button11.BorderColor = Color.Transparent;
            guna2Button11.BorderStyle = System.Drawing.Drawing2D.DashStyle.Dash;
            guna2Button11.BorderThickness = 1;
            guna2Button11.CustomizableEdges = customizableEdges23;
            guna2Button11.DisabledState.BorderColor = Color.DarkGray;
            guna2Button11.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button11.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button11.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button11.FillColor = Color.DarkGray;
            guna2Button11.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            guna2Button11.ForeColor = Color.Black;
            guna2Button11.Location = new Point(10, 223);
            guna2Button11.Name = "guna2Button11";
            guna2Button11.PressedColor = Color.Silver;
            guna2Button11.ShadowDecoration.CustomizableEdges = customizableEdges24;
            guna2Button11.Size = new Size(145, 36);
            guna2Button11.TabIndex = 57;
            guna2Button11.Text = "REFRESH OVERLAY";
            guna2Button11.Click += guna2Button11_Click;
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.BackColor = Color.Transparent;
            label12.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label12.ForeColor = Color.DarkGray;
            label12.Location = new Point(9, 151);
            label12.Name = "label12";
            label12.Size = new Size(116, 13);
            label12.TabIndex = 87;
            label12.Text = "PROX TELEKILL [F4]";
            // 
            // label10
            // 
            label10.AutoSize = true;
            label10.BackColor = Color.Transparent;
            label10.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label10.ForeColor = Color.DarkGray;
            label10.Location = new Point(10, 150);
            label10.Name = "label10";
            label10.Size = new Size(0, 13);
            label10.TabIndex = 87;
            // 
            // guna2CustomCheckBox8
            // 
            guna2CustomCheckBox8.Animated = true;
            guna2CustomCheckBox8.BackColor = Color.Transparent;
            guna2CustomCheckBox8.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox8.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox8.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox8.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox8.CheckMarkColor = Color.Gray;
            guna2CustomCheckBox8.CustomizableEdges = customizableEdges25;
            guna2CustomCheckBox8.Location = new Point(134, 98);
            guna2CustomCheckBox8.Name = "guna2CustomCheckBox8";
            guna2CustomCheckBox8.ShadowDecoration.CustomizableEdges = customizableEdges26;
            guna2CustomCheckBox8.Size = new Size(20, 18);
            guna2CustomCheckBox8.TabIndex = 59;
            guna2CustomCheckBox8.Text = "guna2CustomCheckBox8";
            guna2CustomCheckBox8.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox8.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox8.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox8.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox8.Click += guna2CustomCheckBox8_Click;
            // 
            // guna2CustomCheckBox6
            // 
            guna2CustomCheckBox6.Animated = true;
            guna2CustomCheckBox6.BackColor = Color.Transparent;
            guna2CustomCheckBox6.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox6.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox6.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox6.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox6.CheckMarkColor = Color.DimGray;
            guna2CustomCheckBox6.CustomizableEdges = customizableEdges27;
            guna2CustomCheckBox6.Location = new Point(134, 124);
            guna2CustomCheckBox6.Name = "guna2CustomCheckBox6";
            guna2CustomCheckBox6.ShadowDecoration.CustomizableEdges = customizableEdges28;
            guna2CustomCheckBox6.Size = new Size(20, 18);
            guna2CustomCheckBox6.TabIndex = 79;
            guna2CustomCheckBox6.Text = "guna2CustomCheckBox6";
            guna2CustomCheckBox6.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox6.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox6.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox6.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox6.Click += guna2CustomCheckBox6_Click;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.BackColor = Color.Transparent;
            label5.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label5.ForeColor = Color.DarkGray;
            label5.Location = new Point(8, 126);
            label5.Name = "label5";
            label5.Size = new Size(110, 13);
            label5.TabIndex = 77;
            label5.Text = "CAM DIRECTA [F3]";
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.BackColor = Color.Transparent;
            label6.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label6.ForeColor = Color.SlateBlue;
            label6.Location = new Point(8, 6);
            label6.Name = "label6";
            label6.Size = new Size(0, 13);
            label6.TabIndex = 57;
            // 
            // guna2CustomCheckBox1
            // 
            guna2CustomCheckBox1.Animated = true;
            guna2CustomCheckBox1.BackColor = Color.Transparent;
            guna2CustomCheckBox1.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox1.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox1.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox1.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox1.CheckMarkColor = Color.Gray;
            guna2CustomCheckBox1.CustomizableEdges = customizableEdges29;
            guna2CustomCheckBox1.Location = new Point(134, 50);
            guna2CustomCheckBox1.Name = "guna2CustomCheckBox1";
            guna2CustomCheckBox1.ShadowDecoration.CustomizableEdges = customizableEdges30;
            guna2CustomCheckBox1.Size = new Size(20, 18);
            guna2CustomCheckBox1.TabIndex = 52;
            guna2CustomCheckBox1.Text = "guna2CustomCheckBox1";
            guna2CustomCheckBox1.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox1.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox1.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox1.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox1.Click += guna2CustomCheckBox1_Click;
            // 
            // label35
            // 
            label35.AutoSize = true;
            label35.BackColor = Color.Transparent;
            label35.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label35.ForeColor = Color.DarkGray;
            label35.Location = new Point(8, 27);
            label35.Name = "label35";
            label35.Size = new Size(86, 13);
            label35.TabIndex = 41;
            label35.Text = "RESET OVELAY";
            label35.Click += label35_Click;
            // 
            // label11
            // 
            label11.AutoSize = true;
            label11.BackColor = Color.Transparent;
            label11.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label11.ForeColor = Color.DarkGray;
            label11.Location = new Point(8, 102);
            label11.Name = "label11";
            label11.Size = new Size(93, 13);
            label11.TabIndex = 58;
            label11.Text = "UP PLAYER [F2]";
            label11.Click += label11_Click;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.BackColor = Color.Transparent;
            label2.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label2.ForeColor = Color.DarkGray;
            label2.Location = new Point(8, 77);
            label2.Name = "label2";
            label2.Size = new Size(89, 13);
            label2.TabIndex = 53;
            label2.Text = "STREAM MODE";
            // 
            // guna2CustomCheckBox2
            // 
            guna2CustomCheckBox2.Animated = true;
            guna2CustomCheckBox2.BackColor = Color.Transparent;
            guna2CustomCheckBox2.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox2.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox2.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox2.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox2.CheckMarkColor = Color.Gray;
            guna2CustomCheckBox2.CustomizableEdges = customizableEdges31;
            guna2CustomCheckBox2.Location = new Point(134, 74);
            guna2CustomCheckBox2.Name = "guna2CustomCheckBox2";
            guna2CustomCheckBox2.ShadowDecoration.CustomizableEdges = customizableEdges32;
            guna2CustomCheckBox2.Size = new Size(20, 18);
            guna2CustomCheckBox2.TabIndex = 54;
            guna2CustomCheckBox2.Text = "guna2CustomCheckBox2";
            guna2CustomCheckBox2.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox2.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox2.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox2.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox2.Click += guna2CustomCheckBox2_Click;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.BackColor = Color.Transparent;
            label1.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label1.ForeColor = Color.DarkGray;
            label1.Location = new Point(8, 52);
            label1.Name = "label1";
            label1.Size = new Size(111, 13);
            label1.TabIndex = 51;
            label1.Text = "OVERLAY ANTILAG";
            // 
            // guna2CustomCheckBox21
            // 
            guna2CustomCheckBox21.Animated = true;
            guna2CustomCheckBox21.BackColor = Color.Transparent;
            guna2CustomCheckBox21.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox21.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox21.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox21.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox21.CheckMarkColor = Color.Gray;
            guna2CustomCheckBox21.CustomizableEdges = customizableEdges33;
            guna2CustomCheckBox21.Location = new Point(134, 25);
            guna2CustomCheckBox21.Name = "guna2CustomCheckBox21";
            guna2CustomCheckBox21.ShadowDecoration.CustomizableEdges = customizableEdges34;
            guna2CustomCheckBox21.Size = new Size(20, 18);
            guna2CustomCheckBox21.TabIndex = 46;
            guna2CustomCheckBox21.Text = "guna2CustomCheckBox21";
            guna2CustomCheckBox21.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox21.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox21.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox21.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox21.Click += guna2CustomCheckBox21_Click;
            // 
            // guna2Panel5
            // 
            guna2Panel5.BackColor = Color.Black;
            guna2Panel5.BorderColor = Color.DarkGray;
            guna2Panel5.BorderStyle = System.Drawing.Drawing2D.DashStyle.Dash;
            guna2Panel5.Controls.Add(label26);
            guna2Panel5.Controls.Add(guna2CustomCheckBox4);
            guna2Panel5.Controls.Add(label17);
            guna2Panel5.Controls.Add(label3);
            guna2Panel5.Controls.Add(guna2Button3);
            guna2Panel5.Controls.Add(guna2ComboBox2);
            guna2Panel5.Controls.Add(guna2CustomCheckBox10);
            guna2Panel5.Controls.Add(guna2CustomCheckBox11);
            guna2Panel5.Controls.Add(guna2CustomCheckBox12);
            guna2Panel5.Controls.Add(guna2Button10);
            guna2Panel5.Controls.Add(guna2Button7);
            guna2Panel5.Controls.Add(guna2CustomCheckBox13);
            guna2Panel5.Controls.Add(guna2CustomCheckBox15);
            guna2Panel5.Controls.Add(label48);
            guna2Panel5.Controls.Add(guna2Button8);
            guna2Panel5.Controls.Add(guna2Button9);
            guna2Panel5.Controls.Add(guna2CustomCheckBox16);
            guna2Panel5.Controls.Add(label47);
            guna2Panel5.Controls.Add(label49);
            guna2Panel5.Controls.Add(label50);
            guna2Panel5.Controls.Add(label51);
            guna2Panel5.Controls.Add(label52);
            guna2Panel5.CustomBorderColor = Color.Transparent;
            guna2Panel5.CustomBorderThickness = new Padding(0, 2, 0, 0);
            guna2Panel5.CustomizableEdges = customizableEdges63;
            guna2Panel5.Location = new Point(12, 219);
            guna2Panel5.Name = "guna2Panel5";
            guna2Panel5.ShadowDecoration.CustomizableEdges = customizableEdges64;
            guna2Panel5.Size = new Size(185, 275);
            guna2Panel5.TabIndex = 78;
            guna2Panel5.Paint += guna2Panel5_Paint;
            // 
            // label26
            // 
            label26.AutoSize = true;
            label26.BackColor = Color.Transparent;
            label26.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label26.ForeColor = Color.DarkGray;
            label26.Location = new Point(7, 199);
            label26.Name = "label26";
            label26.Size = new Size(145, 13);
            label26.TabIndex = 88;
            label26.Text = "ESP RAGEWIRE POSITION";
            // 
            // guna2CustomCheckBox4
            // 
            guna2CustomCheckBox4.Animated = true;
            guna2CustomCheckBox4.BackColor = Color.Transparent;
            guna2CustomCheckBox4.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox4.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox4.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox4.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox4.CheckMarkColor = Color.DimGray;
            guna2CustomCheckBox4.CustomizableEdges = customizableEdges37;
            guna2CustomCheckBox4.Location = new Point(151, 170);
            guna2CustomCheckBox4.Name = "guna2CustomCheckBox4";
            guna2CustomCheckBox4.ShadowDecoration.CustomizableEdges = customizableEdges38;
            guna2CustomCheckBox4.Size = new Size(20, 18);
            guna2CustomCheckBox4.TabIndex = 80;
            guna2CustomCheckBox4.Text = "guna2CustomCheckBox4";
            guna2CustomCheckBox4.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox4.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox4.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox4.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox4.Click += guna2CustomCheckBox4_Click_2;
            // 
            // label17
            // 
            label17.AutoSize = true;
            label17.BackColor = Color.Transparent;
            label17.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label17.ForeColor = Color.DarkGray;
            label17.Location = new Point(7, 150);
            label17.Name = "label17";
            label17.Size = new Size(111, 13);
            label17.TabIndex = 78;
            label17.Text = "ESP INFORMATION";
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.BackColor = Color.Transparent;
            label3.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label3.ForeColor = Color.DarkGray;
            label3.Location = new Point(7, 174);
            label3.Name = "label3";
            label3.Size = new Size(98, 13);
            label3.TabIndex = 77;
            label3.Text = "ESP HEALTHBAR";
            // 
            // guna2Button3
            // 
            guna2Button3.Animated = true;
            guna2Button3.BackColor = Color.FromArgb(10, 10, 10);
            guna2Button3.CustomizableEdges = customizableEdges39;
            guna2Button3.DisabledState.BorderColor = Color.DarkGray;
            guna2Button3.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button3.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button3.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button3.FillColor = Color.White;
            guna2Button3.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button3.ForeColor = Color.Black;
            guna2Button3.Location = new Point(123, 126);
            guna2Button3.Name = "guna2Button3";
            guna2Button3.ShadowDecoration.CustomizableEdges = customizableEdges40;
            guna2Button3.Size = new Size(16, 15);
            guna2Button3.TabIndex = 76;
            guna2Button3.Click += guna2Button3_Click;
            // 
            // guna2ComboBox2
            // 
            guna2ComboBox2.BackColor = Color.Transparent;
            guna2ComboBox2.BorderColor = Color.Transparent;
            guna2ComboBox2.BorderStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            guna2ComboBox2.BorderThickness = 0;
            guna2ComboBox2.CustomizableEdges = customizableEdges41;
            guna2ComboBox2.DrawMode = DrawMode.OwnerDrawFixed;
            guna2ComboBox2.DropDownStyle = ComboBoxStyle.DropDownList;
            guna2ComboBox2.FillColor = Color.DarkGray;
            guna2ComboBox2.FocusedColor = Color.FromArgb(94, 148, 255);
            guna2ComboBox2.FocusedState.BorderColor = Color.FromArgb(94, 148, 255);
            guna2ComboBox2.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            guna2ComboBox2.ForeColor = Color.Black;
            guna2ComboBox2.IntegralHeight = false;
            guna2ComboBox2.ItemHeight = 30;
            guna2ComboBox2.Location = new Point(10, 223);
            guna2ComboBox2.Name = "guna2ComboBox2";
            guna2ComboBox2.ShadowDecoration.CustomizableEdges = customizableEdges42;
            guna2ComboBox2.Size = new Size(161, 36);
            guna2ComboBox2.TabIndex = 75;
            guna2ComboBox2.SelectedIndexChanged += guna2ComboBox2_SelectedIndexChanged;
            // 
            // guna2CustomCheckBox10
            // 
            guna2CustomCheckBox10.Animated = true;
            guna2CustomCheckBox10.BackColor = Color.Transparent;
            guna2CustomCheckBox10.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox10.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox10.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox10.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox10.CheckMarkColor = Color.DimGray;
            guna2CustomCheckBox10.CustomizableEdges = customizableEdges43;
            guna2CustomCheckBox10.Location = new Point(151, 146);
            guna2CustomCheckBox10.Name = "guna2CustomCheckBox10";
            guna2CustomCheckBox10.ShadowDecoration.CustomizableEdges = customizableEdges44;
            guna2CustomCheckBox10.Size = new Size(20, 18);
            guna2CustomCheckBox10.TabIndex = 52;
            guna2CustomCheckBox10.Text = "guna2CustomCheckBox10";
            guna2CustomCheckBox10.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox10.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox10.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox10.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox10.Click += guna2CustomCheckBox10_Click;
            // 
            // guna2CustomCheckBox11
            // 
            guna2CustomCheckBox11.Animated = true;
            guna2CustomCheckBox11.BackColor = Color.Transparent;
            guna2CustomCheckBox11.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox11.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox11.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox11.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox11.CheckMarkColor = Color.DimGray;
            guna2CustomCheckBox11.CustomizableEdges = customizableEdges45;
            guna2CustomCheckBox11.Location = new Point(151, 97);
            guna2CustomCheckBox11.Name = "guna2CustomCheckBox11";
            guna2CustomCheckBox11.ShadowDecoration.CustomizableEdges = customizableEdges46;
            guna2CustomCheckBox11.Size = new Size(20, 18);
            guna2CustomCheckBox11.TabIndex = 49;
            guna2CustomCheckBox11.Text = "guna2CustomCheckBox11";
            guna2CustomCheckBox11.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox11.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox11.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox11.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox11.Click += guna2CustomCheckBox11_Click;
            // 
            // guna2CustomCheckBox12
            // 
            guna2CustomCheckBox12.Animated = true;
            guna2CustomCheckBox12.BackColor = Color.Transparent;
            guna2CustomCheckBox12.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox12.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox12.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox12.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox12.CheckMarkColor = Color.DimGray;
            guna2CustomCheckBox12.CustomizableEdges = customizableEdges47;
            guna2CustomCheckBox12.Location = new Point(151, 121);
            guna2CustomCheckBox12.Name = "guna2CustomCheckBox12";
            guna2CustomCheckBox12.ShadowDecoration.CustomizableEdges = customizableEdges48;
            guna2CustomCheckBox12.Size = new Size(20, 18);
            guna2CustomCheckBox12.TabIndex = 48;
            guna2CustomCheckBox12.Text = "guna2CustomCheckBox12";
            guna2CustomCheckBox12.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox12.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox12.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox12.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox12.Click += guna2CustomCheckBox12_Click;
            // 
            // guna2CustomCheckBox13
            // 
            guna2CustomCheckBox13.Animated = true;
            guna2CustomCheckBox13.BackColor = Color.Transparent;
            guna2CustomCheckBox13.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox13.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox13.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox13.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox13.CheckMarkColor = Color.DimGray;
            guna2CustomCheckBox13.CustomizableEdges = customizableEdges53;
            guna2CustomCheckBox13.Location = new Point(151, 73);
            guna2CustomCheckBox13.Name = "guna2CustomCheckBox13";
            guna2CustomCheckBox13.ShadowDecoration.CustomizableEdges = customizableEdges54;
            guna2CustomCheckBox13.Size = new Size(20, 18);
            guna2CustomCheckBox13.TabIndex = 47;
            guna2CustomCheckBox13.Text = "guna2CustomCheckBox13";
            guna2CustomCheckBox13.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox13.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox13.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox13.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox13.Click += guna2CustomCheckBox13_Click;
            // 
            // guna2CustomCheckBox15
            // 
            guna2CustomCheckBox15.Animated = true;
            guna2CustomCheckBox15.BackColor = Color.Transparent;
            guna2CustomCheckBox15.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox15.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox15.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox15.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox15.CheckMarkColor = Color.DimGray;
            guna2CustomCheckBox15.CustomizableEdges = customizableEdges55;
            guna2CustomCheckBox15.Location = new Point(151, 49);
            guna2CustomCheckBox15.Name = "guna2CustomCheckBox15";
            guna2CustomCheckBox15.ShadowDecoration.CustomizableEdges = customizableEdges56;
            guna2CustomCheckBox15.Size = new Size(20, 18);
            guna2CustomCheckBox15.TabIndex = 46;
            guna2CustomCheckBox15.Text = "guna2CustomCheckBox15";
            guna2CustomCheckBox15.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox15.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox15.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox15.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox15.Click += guna2CustomCheckBox15_Click;
            // 
            // label48
            // 
            label48.AutoSize = true;
            label48.BackColor = Color.Transparent;
            label48.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label48.ForeColor = Color.DarkGray;
            label48.Location = new Point(7, 125);
            label48.Name = "label48";
            label48.Size = new Size(53, 13);
            label48.TabIndex = 43;
            label48.Text = "ESP BOX";
            // 
            // guna2CustomCheckBox16
            // 
            guna2CustomCheckBox16.Animated = true;
            guna2CustomCheckBox16.BackColor = Color.Transparent;
            guna2CustomCheckBox16.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox16.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox16.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox16.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox16.CheckMarkColor = Color.DimGray;
            guna2CustomCheckBox16.CustomizableEdges = customizableEdges61;
            guna2CustomCheckBox16.Location = new Point(151, 26);
            guna2CustomCheckBox16.Name = "guna2CustomCheckBox16";
            guna2CustomCheckBox16.ShadowDecoration.CustomizableEdges = customizableEdges62;
            guna2CustomCheckBox16.Size = new Size(20, 18);
            guna2CustomCheckBox16.TabIndex = 45;
            guna2CustomCheckBox16.Text = "guna2CustomCheckBox16";
            guna2CustomCheckBox16.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox16.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox16.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox16.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox16.Click += guna2CustomCheckBox16_Click;
            // 
            // label47
            // 
            label47.AutoSize = true;
            label47.BackColor = Color.Transparent;
            label47.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label47.ForeColor = Color.DarkGray;
            label47.Location = new Point(7, 101);
            label47.Name = "label47";
            label47.Size = new Size(79, 13);
            label47.TabIndex = 44;
            label47.Text = "ESP SKELTON";
            // 
            // label49
            // 
            label49.AutoSize = true;
            label49.BackColor = Color.Transparent;
            label49.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label49.ForeColor = Color.DarkGray;
            label49.Location = new Point(8, 77);
            label49.Name = "label49";
            label49.Size = new Size(78, 13);
            label49.TabIndex = 42;
            label49.Text = "ESP FILL BOX";
            // 
            // label50
            // 
            label50.AutoSize = true;
            label50.BackColor = Color.Transparent;
            label50.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label50.ForeColor = Color.DarkGray;
            label50.Location = new Point(8, 53);
            label50.Name = "label50";
            label50.Size = new Size(102, 13);
            label50.TabIndex = 41;
            label50.Text = "ESP CORNER BOX";
            // 
            // label51
            // 
            label51.AutoSize = true;
            label51.BackColor = Color.Transparent;
            label51.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label51.ForeColor = Color.Red;
            label51.Location = new Point(8, 6);
            label51.Name = "label51";
            label51.Size = new Size(88, 13);
            label51.TabIndex = 19;
            label51.Text = "OVERLAY PLUS";
            // 
            // label52
            // 
            label52.AutoSize = true;
            label52.BackColor = Color.Transparent;
            label52.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label52.ForeColor = Color.DarkGray;
            label52.Location = new Point(8, 31);
            label52.Name = "label52";
            label52.Size = new Size(54, 13);
            label52.TabIndex = 40;
            label52.Text = "ESP LINE";
            // 
            // mainpnl
            // 
            mainpnl.BackColor = Color.Black;
            mainpnl.BorderColor = Color.Transparent;
            mainpnl.BorderStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            mainpnl.BorderThickness = 1;
            mainpnl.Controls.Add(guna2CustomCheckBox17);
            mainpnl.Controls.Add(guna2CustomCheckBox18);
            mainpnl.Controls.Add(label13);
            mainpnl.Controls.Add(label16);
            mainpnl.Controls.Add(guna2CustomCheckBox7);
            mainpnl.Controls.Add(guna2CustomCheckBox3);
            mainpnl.Controls.Add(label8);
            mainpnl.Controls.Add(label9);
            mainpnl.Controls.Add(guna2CustomCheckBox9);
            mainpnl.Controls.Add(label4);
            mainpnl.Controls.Add(label37);
            mainpnl.Controls.Add(guna2CustomCheckBox14);
            mainpnl.Controls.Add(label38);
            mainpnl.CustomBorderColor = Color.Black;
            mainpnl.CustomBorderThickness = new Padding(0, 2, 0, 0);
            mainpnl.CustomizableEdges = customizableEdges77;
            mainpnl.ForeColor = Color.DimGray;
            mainpnl.Location = new Point(12, 37);
            mainpnl.Name = "mainpnl";
            mainpnl.ShadowDecoration.CustomizableEdges = customizableEdges78;
            mainpnl.Size = new Size(185, 176);
            mainpnl.TabIndex = 77;
            mainpnl.Paint += guna2Panel3_Paint;
            // 
            // guna2CustomCheckBox17
            // 
            guna2CustomCheckBox17.Animated = true;
            guna2CustomCheckBox17.BackColor = Color.Transparent;
            guna2CustomCheckBox17.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox17.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox17.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox17.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox17.CheckMarkColor = Color.DarkGray;
            guna2CustomCheckBox17.CustomizableEdges = customizableEdges65;
            guna2CustomCheckBox17.Location = new Point(151, 99);
            guna2CustomCheckBox17.Name = "guna2CustomCheckBox17";
            guna2CustomCheckBox17.ShadowDecoration.CustomizableEdges = customizableEdges66;
            guna2CustomCheckBox17.Size = new Size(20, 18);
            guna2CustomCheckBox17.TabIndex = 81;
            guna2CustomCheckBox17.Text = "guna2CustomCheckBox17";
            guna2CustomCheckBox17.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox17.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox17.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox17.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox17.Click += guna2CustomCheckBox17_Click;
            // 
            // guna2CustomCheckBox18
            // 
            guna2CustomCheckBox18.Animated = true;
            guna2CustomCheckBox18.BackColor = Color.Transparent;
            guna2CustomCheckBox18.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox18.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox18.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox18.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox18.CheckMarkColor = Color.DarkGray;
            guna2CustomCheckBox18.CustomizableEdges = customizableEdges67;
            guna2CustomCheckBox18.Location = new Point(151, 75);
            guna2CustomCheckBox18.Name = "guna2CustomCheckBox18";
            guna2CustomCheckBox18.ShadowDecoration.CustomizableEdges = customizableEdges68;
            guna2CustomCheckBox18.Size = new Size(20, 18);
            guna2CustomCheckBox18.TabIndex = 90;
            guna2CustomCheckBox18.Text = "guna2CustomCheckBox18";
            guna2CustomCheckBox18.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox18.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox18.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox18.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox18.Click += guna2CustomCheckBox18_Click;
            // 
            // label13
            // 
            label13.AutoSize = true;
            label13.BackColor = Color.Transparent;
            label13.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label13.ForeColor = Color.DarkGray;
            label13.Location = new Point(7, 104);
            label13.Name = "label13";
            label13.Size = new Size(108, 13);
            label13.TabIndex = 87;
            label13.Text = "SILENT KILL NOVA";
            // 
            // label16
            // 
            label16.AutoSize = true;
            label16.BackColor = Color.Transparent;
            label16.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label16.ForeColor = Color.DarkGray;
            label16.Location = new Point(7, 80);
            label16.Name = "label16";
            label16.Size = new Size(89, 13);
            label16.TabIndex = 89;
            label16.Text = "AIMBOT NOVA";
            // 
            // guna2CustomCheckBox7
            // 
            guna2CustomCheckBox7.Animated = true;
            guna2CustomCheckBox7.BackColor = Color.Transparent;
            guna2CustomCheckBox7.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox7.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox7.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox7.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox7.CheckMarkColor = Color.DarkGray;
            guna2CustomCheckBox7.CustomizableEdges = customizableEdges69;
            guna2CustomCheckBox7.ForeColor = Color.LightGray;
            guna2CustomCheckBox7.Location = new Point(151, 26);
            guna2CustomCheckBox7.Name = "guna2CustomCheckBox7";
            guna2CustomCheckBox7.ShadowDecoration.CustomizableEdges = customizableEdges70;
            guna2CustomCheckBox7.Size = new Size(20, 18);
            guna2CustomCheckBox7.TabIndex = 88;
            guna2CustomCheckBox7.Text = "guna2CustomCheckBox7";
            guna2CustomCheckBox7.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox7.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox7.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox7.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox7.Click += guna2CustomCheckBox7_Click;
            // 
            // guna2CustomCheckBox3
            // 
            guna2CustomCheckBox3.Animated = true;
            guna2CustomCheckBox3.BackColor = Color.Transparent;
            guna2CustomCheckBox3.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox3.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox3.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox3.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox3.CheckMarkColor = Color.DarkGray;
            guna2CustomCheckBox3.CustomizableEdges = customizableEdges71;
            guna2CustomCheckBox3.ForeColor = Color.PaleVioletRed;
            guna2CustomCheckBox3.Location = new Point(151, 51);
            guna2CustomCheckBox3.Name = "guna2CustomCheckBox3";
            guna2CustomCheckBox3.ShadowDecoration.CustomizableEdges = customizableEdges72;
            guna2CustomCheckBox3.Size = new Size(20, 18);
            guna2CustomCheckBox3.TabIndex = 87;
            guna2CustomCheckBox3.Text = "guna2CustomCheckBox3";
            guna2CustomCheckBox3.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox3.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox3.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox3.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox3.Click += guna2CustomCheckBox3_Click;
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.BackColor = Color.Transparent;
            label8.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label8.ForeColor = Color.DarkGray;
            label8.Location = new Point(7, 31);
            label8.Name = "label8";
            label8.Size = new Size(50, 13);
            label8.TabIndex = 87;
            label8.Text = "ENABLE";
            label8.Click += label8_Click;
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.BackColor = Color.Transparent;
            label9.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label9.ForeColor = Color.Red;
            label9.Location = new Point(7, 6);
            label9.Name = "label9";
            label9.Size = new Size(100, 13);
            label9.TabIndex = 53;
            label9.Text = "NOVA ASSORTED";
            // 
            // guna2CustomCheckBox9
            // 
            guna2CustomCheckBox9.Animated = true;
            guna2CustomCheckBox9.BackColor = Color.Transparent;
            guna2CustomCheckBox9.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox9.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox9.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox9.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox9.CheckMarkColor = Color.DarkGray;
            guna2CustomCheckBox9.CustomizableEdges = customizableEdges73;
            guna2CustomCheckBox9.Location = new Point(151, 149);
            guna2CustomCheckBox9.Name = "guna2CustomCheckBox9";
            guna2CustomCheckBox9.ShadowDecoration.CustomizableEdges = customizableEdges74;
            guna2CustomCheckBox9.Size = new Size(20, 18);
            guna2CustomCheckBox9.TabIndex = 49;
            guna2CustomCheckBox9.Text = "guna2CustomCheckBox9";
            guna2CustomCheckBox9.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox9.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox9.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox9.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox9.Click += guna2CustomCheckBox9_Click;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.BackColor = Color.Transparent;
            label4.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label4.ForeColor = Color.DarkGray;
            label4.Location = new Point(7, 56);
            label4.Name = "label4";
            label4.Size = new Size(98, 13);
            label4.TabIndex = 55;
            label4.Text = "AIMBOT VISIBLE";
            // 
            // label37
            // 
            label37.AutoSize = true;
            label37.BackColor = Color.Transparent;
            label37.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label37.ForeColor = Color.DarkGray;
            label37.Location = new Point(8, 153);
            label37.Name = "label37";
            label37.Size = new Size(107, 13);
            label37.TabIndex = 44;
            label37.Text = "IGNORE KNOCKED";
            // 
            // guna2CustomCheckBox14
            // 
            guna2CustomCheckBox14.Animated = true;
            guna2CustomCheckBox14.BackColor = Color.Transparent;
            guna2CustomCheckBox14.CheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox14.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox14.CheckedState.BorderThickness = 2;
            guna2CustomCheckBox14.CheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox14.CheckMarkColor = Color.DarkGray;
            guna2CustomCheckBox14.CustomizableEdges = customizableEdges75;
            guna2CustomCheckBox14.Location = new Point(151, 123);
            guna2CustomCheckBox14.Name = "guna2CustomCheckBox14";
            guna2CustomCheckBox14.ShadowDecoration.CustomizableEdges = customizableEdges76;
            guna2CustomCheckBox14.Size = new Size(20, 18);
            guna2CustomCheckBox14.TabIndex = 45;
            guna2CustomCheckBox14.Text = "guna2CustomCheckBox14";
            guna2CustomCheckBox14.UncheckedState.BorderColor = Color.SlateBlue;
            guna2CustomCheckBox14.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox14.UncheckedState.BorderThickness = 2;
            guna2CustomCheckBox14.UncheckedState.FillColor = Color.Transparent;
            guna2CustomCheckBox14.Click += guna2CustomCheckBox14_Click;
            // 
            // label38
            // 
            label38.AutoSize = true;
            label38.BackColor = Color.Transparent;
            label38.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label38.ForeColor = Color.DarkGray;
            label38.Location = new Point(7, 128);
            label38.Name = "label38";
            label38.Size = new Size(93, 13);
            label38.TabIndex = 40;
            label38.Text = "AIMFOV CIRCLE";
            // 
            // guna2Panel4
            // 
            guna2Panel4.BackColor = Color.Black;
            guna2Panel4.BorderColor = Color.Transparent;
            guna2Panel4.BorderStyle = System.Drawing.Drawing2D.DashStyle.Dash;
            guna2Panel4.BorderThickness = 1;
            guna2Panel4.Controls.Add(Slider3);
            guna2Panel4.Controls.Add(label20);
            guna2Panel4.Controls.Add(panel3);
            guna2Panel4.Controls.Add(label15);
            guna2Panel4.Controls.Add(Slider2);
            guna2Panel4.Controls.Add(rangelvl1);
            guna2Panel4.Controls.Add(smthlvl2);
            guna2Panel4.Controls.Add(panel2);
            guna2Panel4.Controls.Add(panel1);
            guna2Panel4.Controls.Add(valuelbl1);
            guna2Panel4.Controls.Add(label23);
            guna2Panel4.Controls.Add(label19);
            guna2Panel4.CustomBorderColor = Color.Black;
            guna2Panel4.CustomBorderThickness = new Padding(0, 2, 0, 0);
            guna2Panel4.CustomizableEdges = customizableEdges79;
            guna2Panel4.ForeColor = Color.DimGray;
            guna2Panel4.Location = new Point(203, 37);
            guna2Panel4.Name = "guna2Panel4";
            guna2Panel4.ShadowDecoration.CustomizableEdges = customizableEdges80;
            guna2Panel4.Size = new Size(167, 176);
            guna2Panel4.TabIndex = 78;
            guna2Panel4.Paint += guna2Panel4_Paint;
            // 
            // label15
            // 
            label15.AutoSize = true;
            label15.BackColor = Color.Transparent;
            label15.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label15.ForeColor = Color.Red;
            label15.Location = new Point(8, 6);
            label15.Name = "label15";
            label15.Size = new Size(74, 13);
            label15.TabIndex = 19;
            label15.Text = "ASSISTANCE";
            // 
            // guna2ControlBox1
            // 
            guna2ControlBox1.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            guna2ControlBox1.BackColor = Color.Transparent;
            guna2ControlBox1.ControlBoxStyle = Guna.UI2.WinForms.Enums.ControlBoxStyle.Custom;
            guna2ControlBox1.CustomIconSize = 13F;
            guna2ControlBox1.CustomizableEdges = customizableEdges81;
            guna2ControlBox1.FillColor = Color.Transparent;
            guna2ControlBox1.IconColor = Color.Red;
            guna2ControlBox1.Location = new Point(331, 10);
            guna2ControlBox1.Name = "guna2ControlBox1";
            guna2ControlBox1.ShadowDecoration.CustomizableEdges = customizableEdges82;
            guna2ControlBox1.Size = new Size(27, 18);
            guna2ControlBox1.TabIndex = 12;
            // 
            // guna2Panel1
            // 
            guna2Panel1.Controls.Add(label21);
            guna2Panel1.Controls.Add(guna2ControlBox3);
            guna2Panel1.Controls.Add(label18);
            guna2Panel1.Controls.Add(guna2Button6);
            guna2Panel1.Controls.Add(passwordtxt);
            guna2Panel1.Controls.Add(label7);
            guna2Panel1.Controls.Add(guna2Button4);
            guna2Panel1.Controls.Add(statuslbl);
            guna2Panel1.Controls.Add(usernametxt);
            guna2Panel1.CustomizableEdges = customizableEdges11;
            guna2Panel1.Location = new Point(1249, 10);
            guna2Panel1.Name = "guna2Panel1";
            guna2Panel1.ShadowDecoration.CustomizableEdges = customizableEdges12;
            guna2Panel1.Size = new Size(383, 518);
            guna2Panel1.TabIndex = 86;
            guna2Panel1.Paint += guna2Panel1_Paint;
            // 
            // label21
            // 
            label21.AutoSize = true;
            label21.BackColor = Color.Transparent;
            label21.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label21.ForeColor = Color.DarkGray;
            label21.Location = new Point(22, 444);
            label21.Name = "label21";
            label21.Size = new Size(98, 13);
            label21.TabIndex = 89;
            label21.Text = "INSTA - SHZZ.FX";
            // 
            // guna2ControlBox3
            // 
            guna2ControlBox3.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            guna2ControlBox3.BackColor = Color.Transparent;
            guna2ControlBox3.ControlBoxStyle = Guna.UI2.WinForms.Enums.ControlBoxStyle.Custom;
            guna2ControlBox3.CustomIconSize = 13F;
            guna2ControlBox3.CustomizableEdges = customizableEdges1;
            guna2ControlBox3.FillColor = Color.Transparent;
            guna2ControlBox3.IconColor = Color.Red;
            guna2ControlBox3.Location = new Point(331, 30);
            guna2ControlBox3.Name = "guna2ControlBox3";
            guna2ControlBox3.ShadowDecoration.CustomizableEdges = customizableEdges2;
            guna2ControlBox3.Size = new Size(27, 18);
            guna2ControlBox3.TabIndex = 93;
            // 
            // label18
            // 
            label18.AutoSize = true;
            label18.BackColor = Color.Transparent;
            label18.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label18.ForeColor = Color.DarkGray;
            label18.Location = new Point(22, 327);
            label18.Name = "label18";
            label18.Size = new Size(0, 13);
            label18.TabIndex = 92;
            // 
            // guna2Button6
            // 
            guna2Button6.Animated = true;
            guna2Button6.BorderColor = Color.MediumSlateBlue;
            guna2Button6.BorderThickness = 2;
            guna2Button6.CustomizableEdges = customizableEdges3;
            guna2Button6.DisabledState.BorderColor = Color.DarkGray;
            guna2Button6.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button6.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button6.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button6.FillColor = Color.FromArgb(30, 30, 30);
            guna2Button6.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            guna2Button6.ForeColor = Color.White;
            guna2Button6.Location = new Point(22, 302);
            guna2Button6.Name = "guna2Button6";
            guna2Button6.PressedColor = Color.DimGray;
            guna2Button6.ShadowDecoration.CustomizableEdges = customizableEdges4;
            guna2Button6.Size = new Size(336, 39);
            guna2Button6.TabIndex = 9;
            guna2Button6.Text = "INITIATE";
            guna2Button6.Click += guna2Button6_Click;
            // 
            // passwordtxt
            // 
            passwordtxt.Animated = true;
            passwordtxt.BorderColor = Color.MediumSlateBlue;
            passwordtxt.BorderThickness = 2;
            passwordtxt.CustomizableEdges = customizableEdges5;
            passwordtxt.DefaultText = "";
            passwordtxt.DisabledState.BorderColor = Color.FromArgb(208, 208, 208);
            passwordtxt.DisabledState.FillColor = Color.FromArgb(226, 226, 226);
            passwordtxt.DisabledState.ForeColor = Color.FromArgb(138, 138, 138);
            passwordtxt.DisabledState.PlaceholderForeColor = Color.FromArgb(138, 138, 138);
            passwordtxt.FillColor = Color.FromArgb(30, 30, 30);
            passwordtxt.FocusedState.BorderColor = Color.FromArgb(94, 148, 255);
            passwordtxt.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            passwordtxt.ForeColor = Color.DarkGray;
            passwordtxt.HoverState.BorderColor = Color.FromArgb(94, 148, 255);
            passwordtxt.Location = new Point(22, 188);
            passwordtxt.Name = "passwordtxt";
            passwordtxt.PasswordChar = '\0';
            passwordtxt.PlaceholderForeColor = Color.DarkGray;
            passwordtxt.PlaceholderText = "PASSWORD :";
            passwordtxt.SelectedText = "";
            passwordtxt.ShadowDecoration.CustomizableEdges = customizableEdges6;
            passwordtxt.Size = new Size(336, 40);
            passwordtxt.TabIndex = 8;
            passwordtxt.TextChanged += passwordtxt_TextChanged;
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.BackColor = Color.Transparent;
            label7.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label7.ForeColor = Color.DarkGray;
            label7.Location = new Point(22, 417);
            label7.Name = "label7";
            label7.Size = new Size(181, 13);
            label7.TabIndex = 88;
            label7.Text = "UI DESIGNED BY SHUBHAMBXQ";
            // 
            // guna2Button4
            // 
            guna2Button4.Animated = true;
            guna2Button4.BackColor = Color.Transparent;
            guna2Button4.BorderColor = Color.Transparent;
            guna2Button4.BorderRadius = 5;
            guna2Button4.BorderThickness = 1;
            guna2Button4.CustomizableEdges = customizableEdges7;
            guna2Button4.DisabledState.BorderColor = Color.DarkGray;
            guna2Button4.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button4.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button4.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button4.FillColor = Color.Transparent;
            guna2Button4.Font = new Font("Segoe UI Black", 12F, FontStyle.Bold, GraphicsUnit.Point);
            guna2Button4.ForeColor = Color.MediumSlateBlue;
            guna2Button4.Location = new Point(4, 29);
            guna2Button4.Name = "guna2Button4";
            guna2Button4.PressedColor = Color.DimGray;
            guna2Button4.ShadowDecoration.CustomizableEdges = customizableEdges8;
            guna2Button4.Size = new Size(265, 22);
            guna2Button4.TabIndex = 86;
            guna2Button4.Text = "BURIED MODS REBORN 13.0";
            // 
            // statuslbl
            // 
            statuslbl.AutoSize = true;
            statuslbl.BackColor = Color.Transparent;
            statuslbl.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            statuslbl.ForeColor = Color.LimeGreen;
            statuslbl.Location = new Point(22, 494);
            statuslbl.Name = "statuslbl";
            statuslbl.Size = new Size(56, 13);
            statuslbl.TabIndex = 11;
            statuslbl.Text = "STATUS :";
            statuslbl.Click += statuslbl_Click;
            // 
            // usernametxt
            // 
            usernametxt.Animated = true;
            usernametxt.BackColor = Color.Black;
            usernametxt.BorderColor = Color.MediumSlateBlue;
            usernametxt.BorderThickness = 2;
            usernametxt.CustomizableEdges = customizableEdges9;
            usernametxt.DefaultText = "";
            usernametxt.DisabledState.BorderColor = Color.FromArgb(208, 208, 208);
            usernametxt.DisabledState.FillColor = Color.FromArgb(226, 226, 226);
            usernametxt.DisabledState.ForeColor = Color.FromArgb(138, 138, 138);
            usernametxt.DisabledState.PlaceholderForeColor = Color.FromArgb(138, 138, 138);
            usernametxt.FillColor = Color.FromArgb(30, 30, 30);
            usernametxt.FocusedState.BorderColor = Color.FromArgb(94, 148, 255);
            usernametxt.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            usernametxt.ForeColor = Color.DarkGray;
            usernametxt.HoverState.BorderColor = Color.FromArgb(94, 148, 255);
            usernametxt.Location = new Point(22, 107);
            usernametxt.Name = "usernametxt";
            usernametxt.PasswordChar = '\0';
            usernametxt.PlaceholderForeColor = Color.DarkGray;
            usernametxt.PlaceholderText = "USERNAME :";
            usernametxt.SelectedText = "";
            usernametxt.ShadowDecoration.CustomizableEdges = customizableEdges10;
            usernametxt.Size = new Size(336, 40);
            usernametxt.TabIndex = 2;
            usernametxt.TextChanged += usernametxt_TextChanged;
            // 
            // guna2DragControl1
            // 
            guna2DragControl1.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl1.UseTransparentDrag = true;
            // 
            // guna2DragControl2
            // 
            guna2DragControl2.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl2.UseTransparentDrag = true;
            // 
            // guna2DragControl3
            // 
            guna2DragControl3.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl3.UseTransparentDrag = true;
            // 
            // guna2DragControl4
            // 
            guna2DragControl4.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl4.UseTransparentDrag = true;
            // 
            // guna2DragControl5
            // 
            guna2DragControl5.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl5.UseTransparentDrag = true;
            // 
            // guna2DragControl6
            // 
            guna2DragControl6.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl6.UseTransparentDrag = true;
            // 
            // guna2DragControl7
            // 
            guna2DragControl7.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl7.UseTransparentDrag = true;
            // 
            // guna2DragControl8
            // 
            guna2DragControl8.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl8.UseTransparentDrag = true;
            // 
            // guna2DragControl9
            // 
            guna2DragControl9.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl9.UseTransparentDrag = true;
            // 
            // guna2DragControl10
            // 
            guna2DragControl10.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl10.UseTransparentDrag = true;
            // 
            // guna2DragControl11
            // 
            guna2DragControl11.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl11.UseTransparentDrag = true;
            // 
            // guna2DragControl12
            // 
            guna2DragControl12.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl12.UseTransparentDrag = true;
            // 
            // guna2DragControl13
            // 
            guna2DragControl13.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl13.UseTransparentDrag = true;
            // 
            // MainMenu
            // 
            AccessibleName = "BURIED MODS REBORN 1.0";
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.Black;
            ClientSize = new Size(413, 542);
            ControlBox = false;
            Controls.Add(guna2Panel1);
            Controls.Add(guna2Button13);
            Controls.Add(guna2Panel2);
            FormBorderStyle = FormBorderStyle.None;
            KeyPreview = true;
            MaximizeBox = false;
            MdiChildrenMinimizedAnchorBottom = false;
            MinimizeBox = false;
            Name = "MainMenu";
            ShowIcon = false;
            SizeGripStyle = SizeGripStyle.Hide;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "NATCHO CHEAT";
            TopMost = true;
            Load += Form2_Load;
            panel1.ResumeLayout(false);
            guna2Panel2.ResumeLayout(false);
            guna2Panel2.PerformLayout();
            guna2Panel7.ResumeLayout(false);
            guna2Panel7.PerformLayout();
            guna2Panel5.ResumeLayout(false);
            guna2Panel5.PerformLayout();
            mainpnl.ResumeLayout(false);
            mainpnl.PerformLayout();
            guna2Panel4.ResumeLayout(false);
            guna2Panel4.PerformLayout();
            guna2Panel1.ResumeLayout(false);
            guna2Panel1.PerformLayout();
            ResumeLayout(false);
        }

        #endregion
        private Guna.UI2.WinForms.Guna2BorderlessForm guna2BorderlessForm1;
        private TreeView dataTreeView;
        private Guna.UI2.WinForms.Guna2Elipse guna2Elipse1;
        private Panel panel1;
        private Label valuelbl1;
        private Panel Slider1;
        private Label label23;
        private Panel panel3;
        private Panel Slider3;
        private Label rangelvl1;
        private Label label20;
        private Panel panel2;
        private Panel Slider2;
        private Label smthlvl2;
        private Label label19;
        private System.Windows.Forms.Timer animationTimer1;
        private Guna.UI2.WinForms.Guna2Elipse guna2Elipse2;
        private Guna.UI2.WinForms.Guna2Elipse guna2Elipse3;
        private Guna.UI2.WinForms.Guna2Elipse guna2Elipse4;
        private Guna.UI2.WinForms.Guna2Elipse guna2Elipse5;
        private Guna.UI2.WinForms.Guna2Button guna2Button7;
        private Guna.UI2.WinForms.Guna2Button guna2Button8;
        private Guna.UI2.WinForms.Guna2Button guna2Button9;
        private Guna.UI2.WinForms.Guna2Button guna2Button10;
        private Guna.UI2.WinForms.Guna2Button guna2Button13;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel2;
        private Guna.UI2.WinForms.Guna2Panel mainpnl;
        private Label label37;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox9;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel4;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox14;
        private Label label15;
        private Label label38;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel5;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox11;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox12;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox13;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox15;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox16;
        private Label label47;
        private Label label48;
        private Label label49;
        private Label label50;
        private Label label51;
        private Label label52;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox10;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel7;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox21;
        private Label label35;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox1;
        private Label label1;
        private Label label9;
        private Label label6;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox2;
        private Label label2;
        private Label label22;
        private Label statuslable2;
        private Guna.UI2.WinForms.Guna2Button guna2Button11;
        private Guna.UI2.WinForms.Guna2ControlBox guna2ControlBox1;
        private Guna.UI2.WinForms.Guna2Button guna2Button2;
        private Guna.UI2.WinForms.Guna2ComboBox guna2ComboBox2;
        private System.Windows.Forms.Timer timer1;
        private Guna.UI2.WinForms.Guna2Button guna2Button3;
        private System.Windows.Forms.Timer animationTimer3;
        private System.Windows.Forms.Timer animationTimer2;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl1;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl2;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl3;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl4;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl5;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl6;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl7;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl8;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl9;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl10;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl11;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl12;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl13;
        private Guna.UI2.WinForms.Guna2AnimateWindow guna2AnimateWindow1;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel1;
        private Guna.UI2.WinForms.Guna2Button guna2Button4;
        private Label statuslbl;
        private Guna.UI2.WinForms.Guna2TextBox usernametxt;
        private Guna.UI2.WinForms.Guna2TextBox passwordtxt;
        private Guna.UI2.WinForms.Guna2Button guna2Button6;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox3;
        private Label label4;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox6;
        private Label label5;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox7;
        private Label label8;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox8;
        private Label label11;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox17;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox18;
        private Label label16;
        private Label label17;
        private Label label3;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox4;
        private Label label13;
        private Label label7;
        private Label label18;
        private Guna.UI2.WinForms.Guna2ControlBox guna2ControlBox3;
        private Guna.UI2.WinForms.Guna2ControlBox guna2ControlBox2;
        private Label label10;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox5;
        private Label label12;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox19;
        private Label label14;
        private Label label25;
        private Label label26;
        private Label label21;
    }
}