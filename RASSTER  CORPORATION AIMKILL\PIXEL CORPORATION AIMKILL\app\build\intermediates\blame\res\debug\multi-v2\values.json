{"logs": [{"outputFile": "E:\\aimkill_apk\\BROKEN\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values\\values.xml", "map": [{"source": "E:\\aimkill_apk\\BROKEN\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "43", "endOffsets": "55"}, "to": {"startLines": "3", "startColumns": "4", "startOffsets": "105", "endColumns": "43", "endOffsets": "144"}}, {"source": "E:\\aimkill_apk\\BROKEN\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}}, {"source": "E:\\aimkill_apk\\BROKEN\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "53", "endLines": "5", "endColumns": "12", "endOffsets": "254"}, "to": {"startLines": "4", "startColumns": "4", "startOffsets": "149", "endLines": "7", "endColumns": "12", "endOffsets": "350"}}]}]}