﻿
namespace AotForms
{
    internal enum Bones : uint
    {
        LeftWrist = 0x388,
        Head = 0x38C,
        Spine = 0x394,
        Hip = 0x39C,
        Root = 0x3A0,
        RightCalf = 0x3A4,
        LeftCalf = 0x3A8,
        RightFoot = 0x3AC,
        LeftFoot = 0x3B0,
        RightWrist = 0x3B4,
        LeftHand = 0x3B8,
        LeftSholder = 0x3C0,
        RightSholder = 0x3C4,
        RightWristJoint = 0x3C8,
        LeftWristJoint = 0x3CC,
        LeftElbow = 0x3D0,
        Neck = 0x394,            // Cuello
        Pelvis = 0x390,          // Cadera
        ShoulderR = 0x3c4,       // HombroDerecho
        ShoulderL = 0x3c0,       // HombroIzquierdo
        ElbowR = 0x3d0,          // CodoDerecho
        ElbowL = 0x3d4,          // CodoIzquierdo
        HandR = 0x388,           // ManoDerecha
        HandL = 0x3b8,           // ManoIzquierda
        FootR = 0x3b4,           // PieDerecho
        FootL = 0x3b0,
        Knee<PERSON> = 0x390,
        KneeR = 0x3b4,
        RightElbow = 0x3D4
    }
}
