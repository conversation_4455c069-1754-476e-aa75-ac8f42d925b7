<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\aimkill_apk\BROKEN\app\src\main\jniLibs"><file name="armeabi-v7a/libinjectEmulator.so" path="E:\aimkill_apk\BROKEN\app\src\main\jniLibs\armeabi-v7a\libinjectEmulator.so"/><file name="armeabi-v7a/liblkteam.so" path="E:\aimkill_apk\BROKEN\app\src\main\jniLibs\armeabi-v7a\liblkteam.so"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\aimkill_apk\BROKEN\app\src\debug\jniLibs"/></dataSet></merger>