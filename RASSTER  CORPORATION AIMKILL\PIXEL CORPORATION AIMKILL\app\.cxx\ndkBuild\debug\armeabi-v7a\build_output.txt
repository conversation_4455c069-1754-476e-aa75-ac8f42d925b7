md "E:\aimkill_apk\BROKEN\app\build\intermediates\ndkBuild\debug\obj\local\armeabi-v7a\objs\hawdawdawdawda" >NUL 2>NUL || rem
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../prebuilt/windows-x86_64/bin/echo.exe [armeabi-v7a] "Compile++ arm  ": "hawdawdawdawda <= Client.cpp"
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/hawdawdawdawda/Client.o.d -target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni    -DANDROID -Wno-error=format-security -fvisibility=hidden -ffunction-sections -fdata-sections -w -fno-rtti -fno-exceptions -fpermissive -Wformat -Werror=format-security -Wno-error=format-security -fvisibility=hidden -ffunction-sections -fdata-sections -w -Werror -s -std=c++17 -Wno-error=c++11-narrowing -fms-extensions -fno-rtti -fno-exceptions -fpermissive  -c  E:/aimkill_apk/BROKEN/app/src/main/jni/Client.cpp -o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/hawdawdawdawda/Client.o
md "E:\aimkill_apk\BROKEN\app\build\intermediates\ndkBuild\debug\obj\local\armeabi-v7a\objs\hawdawdawdawda\Tools\SOCKET" >NUL 2>NUL || rem
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../prebuilt/windows-x86_64/bin/echo.exe [armeabi-v7a] "Compile++ arm  ": "hawdawdawdawda <= client.cpp"
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/hawdawdawdawda/Tools/SOCKET/client.o.d -target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni    -DANDROID -Wno-error=format-security -fvisibility=hidden -ffunction-sections -fdata-sections -w -fno-rtti -fno-exceptions -fpermissive -Wformat -Werror=format-security -Wno-error=format-security -fvisibility=hidden -ffunction-sections -fdata-sections -w -Werror -s -std=c++17 -Wno-error=c++11-narrowing -fms-extensions -fno-rtti -fno-exceptions -fpermissive  -c  E:/aimkill_apk/BROKEN/app/src/main/jni/Tools/SOCKET/client.cpp -o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/hawdawdawdawda/Tools/SOCKET/client.o
md "E:\aimkill_apk\BROKEN\app\build\intermediates\ndkBuild\debug\obj\local\armeabi-v7a" >NUL 2>NUL || rem
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../prebuilt/windows-x86_64/bin/echo.exe [armeabi-v7a] "SharedLibrary  ": "libhawdawdawdawda.so"
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -Wl,-soname,libhawdawdawdawda.so -shared E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/hawdawdawdawda/Client.o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/hawdawdawdawda/Tools/SOCKET/client.o -latomic -target armv7-none-linux-androideabi21 -no-canonical-prefixes  -Wl,--gc-sections  -Wl,--build-id=sha1 -Wl,--no-rosegment  -Wl,--gc-sections,--strip-all, -llog -static-libstdc++ -Wl,--no-undefined -Wl,--fatal-warnings -Wl,--no-undefined-version -llog -landroid -lGLESv2 -lc -lm -o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/libhawdawdawdawda.so
md "E:\aimkill_apk\BROKEN\app\build\intermediates\ndkBuild\debug\lib\armeabi-v7a" >NUL 2>NUL || rem
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../prebuilt/windows-x86_64/bin/echo.exe [armeabi-v7a] "Install        ": "libhawdawdawdawda.so => E:\aimkill_apk\BROKEN\app\build\intermediates\ndkBuild\debug\lib/armeabi-v7a/libhawdawdawdawda.so"
copy /b/y "E:\aimkill_apk\BROKEN\app\build\intermediates\ndkBuild\debug\obj\local\armeabi-v7a\libhawdawdawdawda.so" "E:\aimkill_apk\BROKEN\app\build\intermediates\ndkBuild\debug\lib\armeabi-v7a\libhawdawdawdawda.so" > NUL
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/llvm-strip.exe --strip-unneeded  E:\aimkill_apk\BROKEN\app\build\intermediates\ndkBuild\debug\lib/armeabi-v7a/libhawdawdawdawda.so
md "E:\aimkill_apk\BROKEN\app\build\intermediates\ndkBuild\debug\obj\local\armeabi-v7a\objs\jifjf" >NUL 2>NUL || rem
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../prebuilt/windows-x86_64/bin/echo.exe [armeabi-v7a] "Compile++ thumb": "jifjf <= Server.cpp"
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Server.o.d -target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -mthumb -Oz -DNDEBUG  -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni    -DANDROID  -Wformat -Werror=format-security   -c  E:/aimkill_apk/BROKEN/app/src/main/jni/Server.cpp -o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Server.o
md "E:\aimkill_apk\BROKEN\app\build\intermediates\ndkBuild\debug\obj\local\armeabi-v7a\objs\jifjf\Tools\KittyMemory" >NUL 2>NUL || rem
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../prebuilt/windows-x86_64/bin/echo.exe [armeabi-v7a] "Compile++ thumb": "jifjf <= KittyArm64.cpp"
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/KittyMemory/KittyArm64.o.d -target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -mthumb -Oz -DNDEBUG  -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni    -DANDROID  -Wformat -Werror=format-security   -c  E:/aimkill_apk/BROKEN/app/src/main/jni/Tools/KittyMemory/KittyArm64.cpp -o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/KittyMemory/KittyArm64.o
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../prebuilt/windows-x86_64/bin/echo.exe [armeabi-v7a] "Compile++ thumb": "jifjf <= KittyMemory.cpp"
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/KittyMemory/KittyMemory.o.d -target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -mthumb -Oz -DNDEBUG  -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni    -DANDROID  -Wformat -Werror=format-security   -c  E:/aimkill_apk/BROKEN/app/src/main/jni/Tools/KittyMemory/KittyMemory.cpp -o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/KittyMemory/KittyMemory.o
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../prebuilt/windows-x86_64/bin/echo.exe [armeabi-v7a] "Compile++ thumb": "jifjf <= KittyScanner.cpp"
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/KittyMemory/KittyScanner.o.d -target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -mthumb -Oz -DNDEBUG  -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni    -DANDROID  -Wformat -Werror=format-security   -c  E:/aimkill_apk/BROKEN/app/src/main/jni/Tools/KittyMemory/KittyScanner.cpp -o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/KittyMemory/KittyScanner.o
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../prebuilt/windows-x86_64/bin/echo.exe [armeabi-v7a] "Compile++ thumb": "jifjf <= KittyUtils.cpp"
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/KittyMemory/KittyUtils.o.d -target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -mthumb -Oz -DNDEBUG  -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni    -DANDROID  -Wformat -Werror=format-security   -c  E:/aimkill_apk/BROKEN/app/src/main/jni/Tools/KittyMemory/KittyUtils.cpp -o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/KittyMemory/KittyUtils.o
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../prebuilt/windows-x86_64/bin/echo.exe [armeabi-v7a] "Compile++ thumb": "jifjf <= MemoryBackup.cpp"
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/KittyMemory/MemoryBackup.o.d -target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -mthumb -Oz -DNDEBUG  -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni    -DANDROID  -Wformat -Werror=format-security   -c  E:/aimkill_apk/BROKEN/app/src/main/jni/Tools/KittyMemory/MemoryBackup.cpp -o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/KittyMemory/MemoryBackup.o
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../prebuilt/windows-x86_64/bin/echo.exe [armeabi-v7a] "Compile++ thumb": "jifjf <= MemoryPatch.cpp"
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/KittyMemory/MemoryPatch.o.d -target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -mthumb -Oz -DNDEBUG  -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni    -DANDROID  -Wformat -Werror=format-security   -c  E:/aimkill_apk/BROKEN/app/src/main/jni/Tools/KittyMemory/MemoryPatch.cpp -o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/KittyMemory/MemoryPatch.o
md "E:\aimkill_apk\BROKEN\app\build\intermediates\ndkBuild\debug\obj\local\armeabi-v7a\objs\jifjf\Tools\SOCKET" >NUL 2>NUL || rem
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../prebuilt/windows-x86_64/bin/echo.exe [armeabi-v7a] "Compile++ thumb": "jifjf <= server.cpp"
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/SOCKET/server.o.d -target armv7-none-linux-androideabi21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -mthumb -Oz -DNDEBUG  -IE:/aimkill_apk/BROKEN/app/src/main/jni -IE:/aimkill_apk/BROKEN/app/src/main/jni    -DANDROID  -Wformat -Werror=format-security   -c  E:/aimkill_apk/BROKEN/app/src/main/jni/Tools/SOCKET/server.cpp -o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/SOCKET/server.o
md "E:\aimkill_apk\BROKEN\app\src\main\jni\Tools\Dobby" >NUL 2>NUL || rem
md "E:\aimkill_apk\BROKEN\app\src\main\jni\Tools\KittyMemory\Deps\Keystone\libs-android\armeabi-v7a" >NUL 2>NUL || rem
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../prebuilt/windows-x86_64/bin/echo.exe [armeabi-v7a] "SharedLibrary  ": "libjifjf.so"
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -Wl,-soname,libjifjf.so -shared E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Server.o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/KittyMemory/KittyArm64.o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/KittyMemory/KittyMemory.o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/KittyMemory/KittyScanner.o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/KittyMemory/KittyUtils.o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/KittyMemory/MemoryBackup.o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/KittyMemory/MemoryPatch.o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/objs/jifjf/Tools/SOCKET/server.o E:/aimkill_apk/BROKEN/app/src/main/jni/Tools/Dobby/libdobby.a E:/aimkill_apk/BROKEN/app/src/main/jni/Tools/KittyMemory/Deps/Keystone/libs-android/armeabi-v7a/libkeystone.a -latomic -target armv7-none-linux-androideabi21 -no-canonical-prefixes  -Wl,--gc-sections  -Wl,--build-id=sha1 -Wl,--no-rosegment  -static-libstdc++ -Wl,--no-undefined -Wl,--fatal-warnings -Wl,--no-undefined-version -llog -landroid -lGLESv2 -lc -lm -o E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/libjifjf.so
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../prebuilt/windows-x86_64/bin/echo.exe [armeabi-v7a] "Install        ": "libjifjf.so => E:\aimkill_apk\BROKEN\app\build\intermediates\ndkBuild\debug\lib/armeabi-v7a/libjifjf.so"
copy /b/y "E:\aimkill_apk\BROKEN\app\build\intermediates\ndkBuild\debug\obj\local\armeabi-v7a\libjifjf.so" "E:\aimkill_apk\BROKEN\app\build\intermediates\ndkBuild\debug\lib\armeabi-v7a\libjifjf.so" > NUL
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/llvm-strip.exe --strip-unneeded  E:\aimkill_apk\BROKEN\app\build\intermediates\ndkBuild\debug\lib/armeabi-v7a/libjifjf.so
