﻿namespace AotForms
{
    internal static class Offsets
    {

        internal static uint Il2Cpp;
        internal static uint InitBase = 0x8CA88B4;
        internal static uint StaticClass = 0x5c;

        internal static uint CurrentMatch = 0x50;
        internal static uint MatchStatus = 0x3c;
        internal static uint LocalPlayer = 0x44;
        internal static uint DictionaryEntities = 0x68;

        internal static uint Player_IsDead = 0x4c;
        internal static uint Player_Name = 0x224;
        internal static uint Player_Data = 0x44;


        internal static uint FollowCamera = 0x384;
        internal static uint Camera = 0x14;
        internal static uint AimRotation = 0x33c;
        internal static uint MainCameraTransform = 0x194;

        internal static uint AimAnglesCheck = 0x424;



        internal static uint Weapon = 0x330;
        internal static uint WeaponData = 0x44;
        internal static uint WeaponRecoil = 0xc;
        internal static uint ViewMatrix = 0xBC;// 0x98 + 0x24; = 0xBC
        internal static uint GetKillRequest = 0x9C;
        internal static uint Aimkill = 0x9C;
        internal static uint PlayerData_Aimkill = 0x9C;
        internal static uint pomba = 0x474;
        internal static uint AvatarManager = 0x3f4;
        internal static uint Avatar = 0x94;
        internal static uint Avatar_IsVisible = 0x7c;
        internal static uint Avatar_Data = 0x10;
        internal static uint Avatar_Data_IsTeam = 0x51;
        internal static uint AvatarData = 0x10;
        internal static uint isBotOffs = 0x1e8;



        internal static uint Player_ShadowBase = 0x11d0;
        internal static uint XPose = 0x78;
        internal static uint CNIKONPMDHF = 0x8551A48;
        internal static uint undercam = 0x1FC;


        internal static uint Acess_PlayerAttributes = 0x10;
        internal static uint Class_PlayerAttributes_RunSpeedUpScale = 0x24;
        internal static uint bisteca = 0x808;
        internal static uint arma = 0x38;
        internal static uint tiro = 0x2C;


        internal static class Match
        {
            internal static uint State = 0x8;
            internal static uint LocalPlayer = 0x10;
            internal static uint isBotOffs = 0x1e8;
            internal static uint bisteca = 0x808;
            internal static uint arma = 0x38;
            internal static uint tiro = 0x2C;
        }

        internal static class Game
        {
            internal static uint Match = 0x50;
            internal static uint DictionaryEntities = 0x64;
            internal static uint isBotOffs = 0x1e8;
        }

        internal static class Player
        {
            internal static uint Name = 0x20c;
            internal static uint IsDead = 0x4c;
            internal static uint Data = 0x44;
            internal static uint AimRotation = 0x33c;
            internal static uint AimShooting = 0x454;
            internal static uint WeaponIsReloading = 0xC4;
            internal static uint WeaponAutoFire = 0x22a1c00;
            internal static uint isBotOffs = 0x1e8;
            internal static uint bisteca = 0x808;
            internal static uint arma = 0x38;
            internal static uint tiro = 0x2C;
        }
    }
}