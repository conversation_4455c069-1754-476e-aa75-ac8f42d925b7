using AotForms;
using System.Diagnostics;
using System.Runtime.InteropServices;

namespace AotForms
{
    internal static class Program
    {



        [UnmanagedCallersOnly(EntryPoint = "Load")]
        public static void Load(nint pVM)
        {
            if (pVM != 0)
            {
                InternalMemory.Initialize(pVM);

                var process = Process.GetCurrentProcess();

                ComWrappers.RegisterForMarshalling(WinFormsComInterop.WinFormsComWrappers.Instance);

                Application.SetHighDpiMode(HighDpiMode.PerMonitorV2);
                Application.EnableVisualStyles();
                Application.Run(new MainMenu(process.MainWindowHandle));

            }
            else
            {
                MessageBox.Show("RESTART YOUR EMULATOR AND TWEAK ADB WITH BS TWEAKER IF ITS STILL SHOWING THIS ERROR CONTACT OWNER");

                Environment.Exit(0);
            }
        }
    }
}