{"abi": "ARMEABI_V7A", "abiPlatformVersion": 21, "buildSettings": {"environmentVariables": []}, "cxxBuildFolder": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\.cxx\\ndkBuild\\release\\armeabi-v7a", "info": {"abi": "ARMEABI_V7A", "bitness": 32, "deprecated": false, "default": true}, "originalCxxBuildFolder": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\.cxx\\ndkBuild\\release\\armeabi-v7a", "variant": {"buildSystemArgumentList": [], "buildTargetSet": [], "cFlagsList": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "cppFlagsList": [], "isDebuggableEnabled": false, "module": {"buildSystem": "NDK_BUILD", "cmakeToolchainFile": "D:\\AIMKILL\\Android\\Sdk\\ndk\\26.1.10909125\\build\\cmake\\android.toolchain.cmake", "cxxFolder": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\.cxx", "gradleModulePathName": ":app", "intermediatesFolder": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\build\\intermediates", "makeFile": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\src\\main\\jni\\Android.mk", "moduleBuildFile": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\build.gradle", "moduleRootFolder": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app", "ndkDefaultAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkFolder": "D:\\AIMKILL\\Android\\Sdk\\ndk\\26.1.10909125", "ndkMetaAbiList": [{"abi": "ARMEABI_V7A", "bitness": 32, "deprecated": false, "default": true}, {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, {"abi": "X86", "bitness": 32, "deprecated": false, "default": true}, {"abi": "X86_64", "bitness": 64, "deprecated": false, "default": true}], "ndkMetaPlatforms": {"min": 21, "max": 34, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34}}, "ndkSupportedAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultStl": "SYSTEM", "ndkVersion": "26.1.10909125", "project": {"compilerSettingsCacheFolder": "C:\\Users\\<USER>\\Downloads\\BROKEN\\.cxx", "cxxFolder": "C:\\Users\\<USER>\\Downloads\\BROKEN\\.cxx", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isNativeCompilerSettingsCacheEnabled": false, "rootBuildGradleFolder": "C:\\Users\\<USER>\\Downloads\\BROKEN", "sdkFolder": "D:\\AIMKILL\\Android\\Sdk", "isPrefabEnabled": false}, "splitsAbiFilterSet": [], "stlSharedObjectMap": {"LIBCXX_SHARED": {"ARMEABI_V7A": "D:\\AIMKILL\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "ARM64_V8A": "D:\\AIMKILL\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "X86": "D:\\AIMKILL\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "X86_64": "D:\\AIMKILL\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}}, "objFolder": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\build\\intermediates\\ndkBuild\\release\\obj\\local", "variantName": "release", "validAbiList": ["ARMEABI_V7A"], "prefabDirectory": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\.cxx\\ndkBuild\\release\\prefab", "prefabPackageDirectoryList": []}, "prefabFolder": "C:\\Users\\<USER>\\Downloads\\BROKEN\\app\\.cxx\\ndkBuild\\release\\prefab\\armeabi-v7a"}