{"buildFiles": ["E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Android.mk", "E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Application.mk"], "cleanCommands": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\ndk-build.cmd NDK_PROJECT_PATH=null APP_BUILD_SCRIPT=E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Android.mk NDK_APPLICATION_MK=E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Application.mk APP_ABI=armeabi-v7a NDK_ALL_ABIS=armeabi-v7a NDK_DEBUG=1 APP_PLATFORM=android-21 NDK_OUT=E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj NDK_LIBS_OUT=E:\\aimkill_apk\\BROKEN\\app\\build\\intermediates\\ndkBuild\\debug\\lib clean"], "buildTargetsCommand": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\ndk-build.cmd NDK_PROJECT_PATH=null APP_BUILD_SCRIPT=E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Android.mk NDK_APPLICATION_MK=E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Application.mk APP_ABI=armeabi-v7a NDK_ALL_ABIS=armeabi-v7a NDK_DEBUG=1 APP_PLATFORM=android-21 NDK_OUT=E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj NDK_LIBS_OUT=E:\\aimkill_apk\\BROKEN\\app\\build\\intermediates\\ndkBuild\\debug\\lib {LIST_OF_TARGETS_TO_BUILD}", "libraries": {"hawdawdawdawda-debug-armeabi-v7a": {"artifactName": "hawdaw<PERSON><PERSON><PERSON><PERSON>da", "buildCommand": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\ndk-build.cmd NDK_PROJECT_PATH=null APP_BUILD_SCRIPT=E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Android.mk NDK_APPLICATION_MK=E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Application.mk APP_ABI=armeabi-v7a NDK_ALL_ABIS=armeabi-v7a NDK_DEBUG=1 APP_PLATFORM=android-21 NDK_OUT=E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj NDK_LIBS_OUT=E:\\aimkill_apk\\BROKEN\\app\\build\\intermediates\\ndkBuild\\debug\\lib E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/libhawdawdawdawda.so", "abi": "armeabi-v7a", "output": "E:\\aimkill_apk\\BROKEN\\app\\build\\intermediates\\ndkBuild\\debug\\obj\\local\\armeabi-v7a\\libhawdawdawdawda.so", "runtimeFiles": []}, "jifjf-debug-armeabi-v7a": {"artifactName": "jifjf", "buildCommand": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\ndk-build.cmd NDK_PROJECT_PATH=null APP_BUILD_SCRIPT=E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Android.mk NDK_APPLICATION_MK=E:\\aimkill_apk\\BROKEN\\app\\src\\main\\jni\\Application.mk APP_ABI=armeabi-v7a NDK_ALL_ABIS=armeabi-v7a NDK_DEBUG=1 APP_PLATFORM=android-21 NDK_OUT=E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj NDK_LIBS_OUT=E:\\aimkill_apk\\BROKEN\\app\\build\\intermediates\\ndkBuild\\debug\\lib E:/aimkill_apk/BROKEN/app/build/intermediates/ndkBuild/debug/obj/local/armeabi-v7a/libjifjf.so", "abi": "armeabi-v7a", "output": "E:\\aimkill_apk\\BROKEN\\app\\build\\intermediates\\ndkBuild\\debug\\obj\\local\\armeabi-v7a\\libjifjf.so", "runtimeFiles": []}}}